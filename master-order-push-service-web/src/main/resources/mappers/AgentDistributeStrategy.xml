<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wanshifu.master.order.push.mapper.AgentDistributeStrategyMapper">
    <resultMap id="BaseResultMap" type="com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy">
        <id column="strategy_id" jdbcType="INTEGER" property="strategyId"/>
        <id column="agent_id" jdbcType="BIGINT" property="agentId"/>
        <id column="agent_name" jdbcType="VARCHAR" property="agentName"/>
        <result column="strategy_name" jdbcType="VARCHAR" property="strategyName"/>
        <result column="city_division_id" jdbcType="BIGINT" property="cityDivisionId"/>
        <result column="serve_ids" jdbcType="BIGINT" property="serveIds"/>
        <result column="master_source_type" jdbcType="VARCHAR" property="masterSourceType"/>
        <result column="serve_names" jdbcType="BIGINT" property="serveNames"/>
        <result column="third_division_ids" jdbcType="BIGINT" property="thirdDivisionIds"/>
        <result column="distribute_rule" jdbcType="BIGINT" property="distributeRule"/>
        <result column="distribute_priority" jdbcType="BIGINT" property="distributePriority"/>
        <result column="scoring_strategy_id" jdbcType="BIGINT" property="scoringStrategyId"/>
        <result column="strategy_status" jdbcType="INTEGER" property="strategyStatus"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_account_id" jdbcType="BIGINT" property="createAccountId"/>
        <result column="update_account_id" jdbcType="BIGINT" property="updateAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/></resultMap>
    <sql id="Base_Column_List">id, orderNo, orderId, userId, masterId, proposedPriceFrom, bigDataAccuracy, masterOfferPrice, proposedPrice, exceedProposedPrice, userRemark, masterRemark, offerPriceTime, isInterceptOfferPrice, createTime, updateTime </sql>


    <select id="selectByServeId"  resultMap="BaseResultMap">
      SELECT * FROM agent_distribute_strategy WHERE agent_id = #{agentId}
                                                AND find_in_set(#{thirdDivisionId},third_division_ids)
                                                AND find_in_set(#{serveId},serve_ids)
                                                and is_delete = 0 and strategy_status = 1
    </select>

    <select id="selectByAgentIdAndServeIdsAndDivisionId" resultMap="BaseResultMap">
        SELECT * FROM agent_distribute_strategy WHERE agent_id = #{agentId}
                                                  AND find_in_set(#{thirdDivisionId},third_division_ids)
                                                  and distribute_rule = 'direct_appoint'
                                                    AND (
                                                    <foreach collection="serveIdList" item="serveId" separator="OR" index="index">
                                                        FIND_IN_SET(#{serveId},serve_ids)
                                                    </foreach>
                                                    )
                                                  and is_delete = 0 and strategy_status = 1
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select * from agent_distribute_strategy
        <where>
            <if test="strategyName != null and strategyName !=''">
                AND strategy_name like CONCAT( '%', #{strategyName},'%')
            </if>

            <if test="agentName != null and agentName !=''">
                AND agent_name like CONCAT( '%', #{agentName},'%')
            </if>

            <if test="serveName != null and serveName !=''">
                AND serve_names like CONCAT( '%', #{serveName},'%')
            </if>

            <if test="cityDivisionId != null ">
                AND city_division_id >= #{cityDivisionId}
            </if>

            <if test="createTimeStart != null ">
                AND create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null ">
                AND create_time <![CDATA[ <= ]]> #{createTimeEnd}
            </if>

            <if test="masterSourceType != null and masterSourceType !=''">
                AND master_source_type  =  #{masterSourceType}
            </if>

            and is_delete = 0
            order by update_time desc
        </where>
    </select>


</mapper>