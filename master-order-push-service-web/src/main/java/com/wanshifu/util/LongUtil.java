package com.wanshifu.util;

import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 作用描述
 * @Author: <EMAIL>
 * @CreateDate: 2019/5/10 13:44
 * @Version: 1.0
 **/
public class LongUtil {


    private LongUtil() {
    }

    /***
     *字符串转long类型
     * @param num
     * @return
     */
    public static Long strToLong(String num){
        try {
            if(StringUtils.isBlank(num)){
                return null;
            }
            return  Long.valueOf(num);
        } catch (Exception e) {
        }
        return  null;
    }

    /***
     *Object转long类型
     * @param num
     * @return
     */
    public static Long objToLong(Object num){
        try {
            if(num==null) {
                return null;
            }
            return  Long.valueOf(num.toString());
        } catch (Exception e) {
        }
        return  null;
    }

    /***
     *Object转long类型
     * @param num
     * @return
     */
    public static Long intToLong(Integer num){
        try {
            if(num==null) {
                return 0L;
            }
            return  Long.valueOf(num);
        } catch (Exception e) {
        }
        return  0L;
    }


    /***
     *Object转long类型
     * @param num
     * @return
     */
    public static String objToString(Object num){
        try {
            return  num.toString();
        } catch (Exception e) {
        }
        return  null;
    }

    /**
     * Long类型不为空
     *
     * @param t
     * @return
     */
    public static boolean notEmpty(Long t) {
        return t != null && t != 0;
    }

    /**
     * Long类型为空
     *
     * @param t
     * @return
     */
    public static boolean isEmpty(Long t) {
        return t == null || t == 0;
    }


}
