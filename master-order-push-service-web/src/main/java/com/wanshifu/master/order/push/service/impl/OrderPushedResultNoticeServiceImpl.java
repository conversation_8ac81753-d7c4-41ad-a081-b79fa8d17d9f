package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.master.order.push.domain.dto.OrderPushedResultNotice;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.service.OrderPushedResultNoticeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 订单推单结果通知
 */
@Service
public class OrderPushedResultNoticeServiceImpl implements OrderPushedResultNoticeService {

    @Override
    public int pushedResultNotice(OrderPushedResultNotice orderPushedResultNotice){

        List<String> pushModeList = new ArrayList<>();
        pushModeList.add(PushMode.AGREEMENT_MASTER.code);
        pushModeList.add(PushMode.NORMAL.code);
        pushModeList.add(PushMode.NEW_MODEL_SINGLE.code);
        pushModeList.add("afresh_new_model_single");
        pushModeList.add("collect_contract_master");
        pushModeList.add(PushMode.COOPERATION_BUSINESS_MASTER.getCode());
        pushModeList.add(PushMode.TECHNIQUE_VERIFY_MASTER_DISPATCH.getCode());
        pushModeList.add(PushMode.AFTER_TECHNIQUE_VERIFY_MASTER.getCode());
        pushModeList.add(PushMode.FULL_TIME_MASTER_DISPATCH.getCode());
        pushModeList.add(PushMode.SPECIAL_GROUP.getCode());
        pushModeList.add("agent");




        if(!pushModeList.contains(orderPushedResultNotice.getPushMode())){
            return 0;
        }

        return OrderDistributeContext.getInstance(orderPushedResultNotice.getPushMode()).orderDistribute(orderPushedResultNotice);
    }

}
