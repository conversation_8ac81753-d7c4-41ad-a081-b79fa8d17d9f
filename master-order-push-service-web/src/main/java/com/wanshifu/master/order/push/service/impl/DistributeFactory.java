package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.ql.util.express.DefaultContext;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderBaseByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.response.GetOrderBaseByGlobalIdRsp;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.AgentDistributor;
import com.wanshifu.master.order.push.domain.dto.DistributeStrategyExpressionDto;
import com.wanshifu.master.order.push.domain.dto.ScorerAgent;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.resp.newMasterOrderDistributeStrategy.DetailResp;
import com.wanshifu.master.order.push.domain.resp.orderSelectStrategy.GetOrderDistributeStrategyDetailResp;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.agreementOrderDistributeStrategy.CreateRqt;
import com.wanshifu.master.order.push.domain.rqt.orderDistributeStrategy.GetOrderDistributeStrategyListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.OrderScoringStrategyByIdListRqt;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.OrderScoringStrategyDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.OrderSelectStrategyDetailRqt;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.OrderScoringStrategyService;
import com.wanshifu.master.order.push.service.OrderSelectStrategyService;
import com.wanshifu.master.order.push.service.PushHandler;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderExtraData;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import com.wanshifu.util.LoCollectionsUtil;
import com.wanshifu.util.QlExpressStatic;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class DistributeFactory {

    @Resource
    private FeatureRepository featureRepository;

    @Resource
    private OrderDistributeStrategyRepository orderDistributeStrategyRepository;

    @Resource
    private OrderSelectStrategyRepository orderSelectStrategyRepository;

    @Resource
    private OrderScoringStrategyRepository orderScoringStrategyRepository;


    @Autowired
    DistributeFactory distributeFactory;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;



    @Resource
    private AddressCommon addressCommon;

    @Resource
    private OrderConfigCommon orderConfigCommon;

    @Resource
    private Tools tools;

    @Resource
    private InfoQueryApi infoQueryApi;


    @Resource
    private NewMasterOrderDistributeStrategyRepository newMasterOrderDistributeStrategyRepository;



    @Resource
    private PushHandler pushHandler;


    private void extractDistributeFeatures(OrderDistributor orderDistributor,
                                           List<OrderDistributeStrategy> orderDistributeStrategies){
        for (OrderDistributeStrategy orderDistributeStrategy : orderDistributeStrategies) {
            final String distributeStrategyExpression =
                    orderDistributeStrategy.getDistributeStrategyExpression();
            final JSONArray distributeStrategyExpressionArray = JSONObject.parseArray(distributeStrategyExpression);
            for (int i = 0; i < distributeStrategyExpressionArray.size(); i++) {
                final JSONObject strategyGroup = distributeStrategyExpressionArray.getJSONObject(i);
                String groupOpenFeatures=strategyGroup.getString(FieldConstant.OPEN_CONDITION_RULE_PARAMS);
                if (groupOpenFeatures!=null) {
                    for (String featureCode : groupOpenFeatures.split(SymbolConstant.COMMA)) {
                        orderDistributor.addAdmittanceOrderFeatureCode(featureCode);
                    }
                }
            }
        }
        orderDistributor.addAdmittanceOrderFeatureCode("appoint_user");
        orderDistributor.addAdmittanceOrderFeatureCode("user_group");
    }



    public OrderDistributor matchDistributor(OrderDetailData orderDetailData,
                                             DefaultContext<String, Object> orderFeatures, String distributeType){


        final OrderDistributor orderDistributor = new OrderDistributor();


        final List<OrderDistributeStrategy> orderDistributeStrategies = orderDistributeStrategyRepository
                .selectStrategyIds(
                        GetOrderDistributeStrategyListRqt
                                .GetOrderDistributeStrategyListRqtBuilder
                                .aGetOrderDistributeStrategyListRqt()
                                .withBusinessLineId(pushHandler.getBusinessLineId(orderDetailData))
                                .withCategoryIds(String.valueOf(orderDetailData.getOrderCategoryId()))
                                .withCityId(orderDetailData.getSecondDivisionId())
                                .withStrategyStatus(1)
                                .withDistributeType(distributeType)
                                .build()
                );


        if(CollectionUtils.isEmpty(orderDistributeStrategies)){
            return orderDistributor;
        }


        OrderDistributeStrategy distributeStrategy = orderDistributeStrategies.stream().filter(orderDistributeStrategy -> !"all".equals(orderDistributeStrategy.getOpenCityMode())).findFirst().orElse(null);

        if(Objects.isNull(distributeStrategy)) {
            distributeStrategy = orderDistributeStrategies.stream().filter(orderDistributeStrategy -> "all".equals(orderDistributeStrategy.getOpenCityMode())).findFirst().orElse(null);
        }

        List<GetOrderDistributeStrategyDetailResp.DistributeStrategyVo> distributeStrategyVoList = JSON.parseArray(distributeStrategy.getDistributeStrategy(),GetOrderDistributeStrategyDetailResp.DistributeStrategyVo.class);

        List<Integer> orderSelectStrategyIdList = distributeStrategyVoList.stream().map(GetOrderDistributeStrategyDetailResp.DistributeStrategyVo::getOrderSelectStrategyId).collect(Collectors.toList());

        List<Integer> orderScoringStrategyIdList = distributeStrategyVoList.stream().map(GetOrderDistributeStrategyDetailResp.DistributeStrategyVo::getOrderScoringStrategyId).collect(Collectors.toList());
        List<OrderSelectStrategy> orderSelectStrategyList = orderSelectStrategyRepository.selectByStrategyIdList(orderSelectStrategyIdList);
        List<OrderScoringStrategy> orderScoringStrategyList = orderScoringStrategyRepository.selectByStrategyIdList(orderScoringStrategyIdList);



        Map<Integer, OrderSelectStrategy> orderSelectStrategyMap = orderSelectStrategyList.stream()
                .collect(Collectors.toMap(OrderSelectStrategy::getStrategyId, strategy -> strategy));




        Map<Integer, OrderScoringStrategy> orderScoringStrategyMap = CollectionUtils.isNotEmpty(orderScoringStrategyList) ? orderScoringStrategyList.stream()
                .collect(Collectors.toMap(OrderScoringStrategy::getStrategyId, strategy -> strategy)) : null;

        /**
         * 初始化分配器
         */
        extractDistributeFeatures(orderDistributor,orderDistributeStrategies);
        featureRepository.orderFeatureReplenish(orderFeatures,orderDistributor.getAdmittanceOrderFeatureSet());

        if (orderFeatures.containsKey("appoint_user")) {
            Object appointUser = orderFeatures.get("appoint_user");
            if (Objects.nonNull(appointUser)
                    && appointUser instanceof String
                    && !Strings.isNullOrEmpty(appointUser.toString())) {
                // 下单用户  需要转换成list才能使用containsAny运算
                orderFeatures.put("appoint_user", Stream.of(appointUser.toString().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            } else {
                // 该用户没有 人群  数据，则设置空数组
                orderFeatures.put("appoint_user", new ArrayList<>());
            }
        }


        if (orderFeatures.containsKey("user_group")) {
            Object appointUser = orderFeatures.get("user_group");
            if (Objects.nonNull(appointUser)
                    && appointUser instanceof String
                    && !Strings.isNullOrEmpty(appointUser.toString())) {
                // 下单用户  需要转换成list才能使用containsAny运算
                orderFeatures.put("user_group", Stream.of(appointUser.toString().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            } else {
                // 该用户没有 人群  数据，则设置空数组
                orderFeatures.put("user_group", new ArrayList<>());
            }
        }

        log.info("orderFeatures:" + JSON.toJSONString(orderFeatures));
        /**
         * 过滤分配策略开启条件
         */
        final List<Integer> orderSelectStrategyIds =new ArrayList<>();
        final List<Integer> orderScorerStrategyIds =new ArrayList<>();

        final String distributeStrategyExpression =
                distributeStrategy.getDistributeStrategyExpression();
        final JSONArray distributeStrategyExpressionArray = JSONObject.parseArray(distributeStrategyExpression);
        for (int i = 0; i < distributeStrategyExpressionArray.size(); i++) {
            final JSONObject strategyGroup = distributeStrategyExpressionArray.getJSONObject(i);
            String groupOpen=strategyGroup.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
            final boolean expressBoolean = QlExpressStatic.QlExpressBoolean(
                    groupOpen, orderFeatures
            );
            if (expressBoolean) {
                orderSelectStrategyIds.add(strategyGroup.getInteger(FieldConstant.ORDER_SELECT_STRATEGY_ID));
                if(strategyGroup.containsKey(FieldConstant.ORDER_SCORING_STRATEGY_ID)){
                    orderScorerStrategyIds.add(strategyGroup.getInteger(FieldConstant.ORDER_SCORING_STRATEGY_ID));
                }
                orderDistributor.setDistributeRule(strategyGroup.getString(FieldConstant.ORDER_DISTRIBUTE_RULE));
                orderDistributor.setMatched(true);
                orderDistributor.setCooperationBusinessServeDivisionAtLast(strategyGroup.getInteger(FieldConstant.COOPERATION_BUSINESS_SERVE_DIVISION_AT_LAST));
                break;
            }
        }

        if (orderSelectStrategyIds.size()!=0) {
            final List<OrderSelectStrategy> orderSelectStrategies = new ArrayList<>();
            orderSelectStrategyIds.forEach(strategyId -> orderSelectStrategies.add(orderSelectStrategyMap.get(strategyId)));
            if(StringUtils.isNotBlank(orderSelectStrategies.get(0).getSelectRuleExpression())){
                orderDistributor.setSelectStrategy(orderSelectStrategies,orderFeatures);
            }else{
                orderDistributor.setSelectStrategy(orderSelectStrategies);
            }
        }
        if (CollectionUtils.isNotEmpty(orderScorerStrategyIds)) {
            final List<OrderScoringStrategy> orderScoringStrategies = new ArrayList<>();
            orderScorerStrategyIds.forEach(strategyId -> orderScoringStrategies.add(orderScoringStrategyMap.get(strategyId)));
            orderDistributor.setScoringStrategy(orderScoringStrategies);
        }


        if(StringUtils.isNotBlank(distributeStrategy.getCompensateDistributeStrategy())){
            orderDistributor.setCompensateDistributeList(JSON.parseArray(distributeStrategy.getCompensateDistributeStrategy(), OrderDistributor.CompensateDistributeDTO.class));
        }


        return orderDistributor;
    }

    public OrderDistributor matchCooperationBusinessDistributorOnly(OrderDetailData orderDetailData,
                                             DefaultContext<String, Object> orderFeatures, String distributeType){


        final OrderDistributor orderDistributor = new OrderDistributor();


        final List<OrderDistributeStrategy> orderDistributeStrategies = orderDistributeStrategyRepository
                .selectStrategyIds(
                        GetOrderDistributeStrategyListRqt
                                .GetOrderDistributeStrategyListRqtBuilder
                                .aGetOrderDistributeStrategyListRqt()
                                .withBusinessLineId(orderDetailData.getBusinessLineId())
                                .withCategoryIds(String.valueOf(orderDetailData.getOrderCategoryId()))
                                .withCityId(orderDetailData.getSecondDivisionId())
                                .withStrategyStatus(1)
                                .withDistributeType(distributeType)
                                .build()
                );


        if(CollectionUtils.isEmpty(orderDistributeStrategies)){
            return orderDistributor;
        }

        /**
         * 初始化分配器
         */
        extractDistributeFeatures(orderDistributor,orderDistributeStrategies);
        featureRepository.orderFeatureReplenish(orderFeatures,orderDistributor.getAdmittanceOrderFeatureSet());

        if (orderFeatures.containsKey("appoint_user")) {
            Object appointUser = orderFeatures.get("appoint_user");
            if (Objects.nonNull(appointUser)
                    && appointUser instanceof String
                    && !Strings.isNullOrEmpty(appointUser.toString())) {
                // 下单用户  需要转换成list才能使用containsAny运算
                orderFeatures.put("appoint_user", Stream.of(appointUser.toString().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            } else {
                // 该用户没有 人群  数据，则设置空数组
                orderFeatures.put("appoint_user", new ArrayList<>());
            }

        }

        out:for (OrderDistributeStrategy orderDistributeStrategy : orderDistributeStrategies) {
            final String distributeStrategyExpression =
                    orderDistributeStrategy.getDistributeStrategyExpression();
            final JSONArray distributeStrategyExpressionArray = JSONObject.parseArray(distributeStrategyExpression);
            for (int i = 0; i < distributeStrategyExpressionArray.size(); i++) {
                final JSONObject strategyGroup = distributeStrategyExpressionArray.getJSONObject(i);
                String groupOpen = strategyGroup.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
                final boolean expressBoolean = QlExpressStatic.QlExpressBoolean(
                        groupOpen, orderFeatures
                );
                if (expressBoolean) {
                    orderDistributor.setCooperationBusinessServeDivisionAtLast(strategyGroup.getInteger(FieldConstant.COOPERATION_BUSINESS_SERVE_DIVISION_AT_LAST));

                    orderDistributor.setMatched(true);
                    break out;
                }
            }
        }


        return orderDistributor;
    }


    @Resource
    private AgreementOrderDistributeStrategyRepository agreementOrderDistributeStrategyRepository;

    @Resource
    private OrderSelectStrategyService orderSelectStrategyService;

    @Resource
    private OrderScoringStrategyService orderScoringStrategyService;


    public OrderDistributor matchAgreementDistributor(OrderDetailData orderDetailData,
                                             DefaultContext<String, Object> orderFeatures){

        Integer businessLineId = getBusinessLineId(orderDetailData);

        OrderDistributor orderDistributor = new OrderDistributor();
        AgreementOrderDistributeStrategy agreementOrderDistributeStrategy = agreementOrderDistributeStrategyRepository.selectByCategoryIdAndCityId(businessLineId,orderDetailData.getOrderCategoryId(),String.valueOf(orderDetailData.getSecondDivisionId()));


        if(Objects.isNull(agreementOrderDistributeStrategy)){
            agreementOrderDistributeStrategy = agreementOrderDistributeStrategyRepository.selectByCategoryIdAndCityId(businessLineId,orderDetailData.getOrderCategoryId(),"all");
        }


        if(Objects.isNull(agreementOrderDistributeStrategy)){
            return orderDistributor;
        }


        List<CreateRqt.DistributeStrategyItem> distributeStrategyList = JSON.parseArray(agreementOrderDistributeStrategy.getDistributeStrategyList(),CreateRqt.DistributeStrategyItem.class);

        if(CollectionUtils.isEmpty(distributeStrategyList)){
            return orderDistributor;
        }

        Map<Integer, OrderSelectStrategy> orderSelectStrategyMap = new HashMap<>();
        distributeStrategyList.forEach(distributeStrategyItem -> {
            OrderSelectStrategyDetailRqt orderSelectStr = new OrderSelectStrategyDetailRqt();
            orderSelectStr.setStrategyId(distributeStrategyItem.getSelectStrategyId());
            orderSelectStrategyMap.put(distributeStrategyItem.getSelectStrategyId(),orderSelectStrategyService.detail(orderSelectStr));
        });






        Map<Integer, OrderScoringStrategy> orderScoringStrategyMap = new HashMap<>();
        distributeStrategyList.forEach(distributeStrategyItem -> {
            if(Objects.nonNull(distributeStrategyItem.getScoreStrategyId()) && distributeStrategyItem.getScoreStrategyId() > 0){
                OrderScoringStrategyDetailRqt orderScoringStrategyDetailRqt = new OrderScoringStrategyDetailRqt();
                orderScoringStrategyDetailRqt.setStrategyId(distributeStrategyItem.getScoreStrategyId());
                orderScoringStrategyMap.put(distributeStrategyItem.getScoreStrategyId(),orderScoringStrategyService.detail(orderScoringStrategyDetailRqt));
            }
        });
        /**
         * 初始化分配器
         */
//        extractDistributeFeatures(orderDistributor,orderDistributeStrategies);
        featureRepository.orderFeatureReplenish(orderFeatures,orderDistributor.getAdmittanceOrderFeatureSet());

        /**
         * 过滤分配策略开启条件
         */
        final List<Integer> orderSelectStrategyIds =new ArrayList<>();
        final List<Integer> orderScorerStrategyIds =new ArrayList<>();



        final String distributeStrategyExpression =
                agreementOrderDistributeStrategy.getDistributeStrategyExpressionList();
        List<DistributeStrategyExpressionDto> distributeStrategyExpressionDtoList = JSONObject.parseArray(distributeStrategyExpression, DistributeStrategyExpressionDto.class);
        for(DistributeStrategyExpressionDto expressionDto : distributeStrategyExpressionDtoList){

            String groupOpen = expressionDto.getOpenConditionRuleExpression();
            final boolean expressBoolean = QlExpressStatic.QlExpressBoolean(
                    groupOpen, orderFeatures
            );
            if (expressBoolean) {
                orderSelectStrategyIds.add(expressionDto.getSelectStrategyId());
                if(Objects.nonNull(expressionDto.getScoreStrategyId())){
                    orderScorerStrategyIds.add(expressionDto.getScoreStrategyId());
                }
                orderDistributor.setDistributeRule(expressionDto.getDistributeRule());
                orderDistributor.setDistributeNum(expressionDto.getDistributeNum());
                orderDistributor.setMatched(true);
                break;

            }
        }

        if (orderSelectStrategyIds.size()!=0) {
            final List<OrderSelectStrategy> orderSelectStrategies = new ArrayList<>();
            orderSelectStrategyIds.forEach(strategyId -> orderSelectStrategies.add(orderSelectStrategyMap.get(strategyId)));
            if(StringUtils.isNotBlank(orderSelectStrategies.get(0).getSelectRuleExpression())){
                orderDistributor.setSelectStrategy(orderSelectStrategies,orderFeatures);
            }else{
                orderDistributor.setSelectStrategy(orderSelectStrategies);
            }
        }
        if (orderScorerStrategyIds.size()!=0) {
            final List<OrderScoringStrategy> orderScoringStrategies = new ArrayList<>();
            orderScorerStrategyIds.forEach(strategyId -> orderScoringStrategies.add(orderScoringStrategyMap.get(strategyId)));
            orderDistributor.setScoringStrategy(orderScoringStrategies);
        }

        return orderDistributor;
    }


//    public OrderDistributor matchDistributor(DefaultContext<String, Object> orderFeatures, String distributeType) {
//        final List<OrderDistributeStrategy> orderDistributeStrategies = orderDistributeStrategyRepository
//                .selectStrategyIds(
//                        GetOrderDistributeStrategyListRqt
//                                .GetOrderDistributeStrategyListRqtBuilder
//                                .aGetOrderDistributeStrategyListRqt()
//                                .withBusinessLineId((Integer) orderFeatures.get("business_line_id"))
//                                .withCategoryIds(String.valueOf((Integer) orderFeatures.get("category_id")))
//                                .withCityId((Long) orderFeatures.get("second_division_id"))
//                                .withStrategyStatus(1)
//                                .withDistributeType(distributeType)
//                                .build()
//                );
//
//        /**
//         * 初始化分配器
//         */
//        final OrderDistributor orderDistributor = new OrderDistributor();
//        extractDistributeFeatures(orderDistributor, orderDistributeStrategies);
//        featureRepository.orderFeatureReplenish(orderFeatures, orderDistributor.getAdmittanceOrderFeatureSet());
//
//        /**
//         * 过滤分配策略开启条件
//         */
//        final List<Integer> orderSelectStrategyIds = new ArrayList<>();
//        final List<Integer> orderScorerStrategyIds = new ArrayList<>();
//        out:for (OrderDistributeStrategy orderDistributeStrategy : orderDistributeStrategies) {
//            final String distributeStrategyExpression =
//                    orderDistributeStrategy.getDistributeStrategyExpression();
//            final JSONArray distributeStrategyExpressionArray = JSONObject.parseArray(distributeStrategyExpression);
//            for (int i = 0; i < distributeStrategyExpressionArray.size(); i++) {
//                final JSONObject strategyGroup = distributeStrategyExpressionArray.getJSONObject(i);
//                String groupOpen = strategyGroup.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
//                final boolean expressBoolean = QlExpressStatic.QlExpressBoolean(
//                        groupOpen, orderFeatures
//                );
//                if (expressBoolean) {
//                    orderSelectStrategyIds.add(strategyGroup.getInteger(FieldConstant.ORDER_SELECT_STRATEGY_ID));
//                    orderScorerStrategyIds.add(strategyGroup.getInteger(FieldConstant.ORDER_SCORING_STRATEGY_ID));
//                    orderDistributor.setDistributeRule(strategyGroup.getString(FieldConstant.ORDER_DISTRIBUTE_RULE));
//                    orderDistributor.setMatched(true);
//                    break out;
//                }
//            }
//        }
//        if (orderSelectStrategyIds.size() != 0) {
//            final List<OrderSelectStrategy> orderSelectStrategies = orderSelectStrategyRepository.selectAvailableStrategyByIdList(orderSelectStrategyIds);
//            if (com.wanshifu.framework.utils.StringUtils.isNotBlank(orderSelectStrategies.get(0).getSelectRuleExpression())) {
//                orderDistributor.setSelectStrategy(orderSelectStrategies, orderFeatures);
//            } else {
//                orderDistributor.setSelectStrategy(orderSelectStrategies);
//            }
//        }
//        if (orderScorerStrategyIds.size() != 0) {
//            final List<OrderScoringStrategy> orderScoringStrategies = orderScoringStrategyRepository.selectAvailableStrategyByIdList(orderScorerStrategyIds);
//            orderDistributor.setScoringStrategy(orderScoringStrategies);
//        }
//
//        return orderDistributor;
//    }


    private Integer getBusinessLineId(OrderDetailData orderDetailData){
        Integer businessLineId = 1;
        String pushMasterSourceType = orderDetailData.getPushExtraData().getMasterSourceType();
        if(orderDetailData.getBusinessLineId() == 1){
            businessLineId = 1;
        }else if(orderDetailData.getBusinessLineId() == 2 && OrderFrom.APPLET.valueEn.equals(orderDetailData.getOrderFrom())){
            //家庭小程序订单推单分流新师傅APP
            businessLineId = MasterSourceType.TOB.code.equals(pushMasterSourceType) ? 2 : 999;
        }else{
            businessLineId = orderDetailData.getBusinessLineId();
        }
        return businessLineId;
    }


    public AgentDistributor generateByScorer(Map<String,Integer> agentScoreId){
        final AgentDistributor agentDistributor =new AgentDistributor();
        final Map<Integer, Set<String>> scorerMapping = new HashMap<>();
        final List<Integer> scorerList =
                new ArrayList<Integer>(
                        agentScoreId.entrySet().stream().map(
                                row -> {
                                    LoCollectionsUtil.putToSet(scorerMapping,row.getValue(),row.getKey());
                                    return row.getValue();
                                }
                        ).collect(Collectors.toSet())
                );
        if (CollectionUtil.isEmpty(scorerList)) {
            agentDistributor.setScoringStrategy(scorerMapping, null);
        } else {
            OrderScoringStrategyByIdListRqt rqt = new OrderScoringStrategyByIdListRqt();
            rqt.setStrategyIdList(scorerList);

            final List<OrderScoringStrategy> orderScoringStrategies =
                    orderScoringStrategyService.selectAvailableStrategyByIdList(rqt);
            agentDistributor.setScoringStrategy(scorerMapping, orderScoringStrategies);
        }
        return agentDistributor;
    }


    private DefaultContext<String, Object> initOrderFeatures(Long masterOrderId){
        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(masterOrderId);
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderExtraData orderExtraData = orderGrabByIdResp.getOrderExtraData();
        final OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();
        return this.initOrderFeatures(orderBase,orderExtraData,orderGrab);
    }


    public DefaultContext<String, Object> initOrderFeatures(OrderBase orderBase, OrderExtraData orderExtraData, OrderGrab orderGrab){

        DefaultContext<String, Object> orderFeatures=new DefaultContext<>();
        orderFeatures.put(FieldConstant.ACCOUNT_ID,orderBase.getAccountId());
        orderFeatures.put(FieldConstant.BUSINESS_LINE_ID,orderBase.getBusinessLineId());
        orderFeatures.put(FieldConstant.CATEGORY_ID,orderBase.getCategoryId());
        orderFeatures.put(FieldConstant.APPOINT_TYPE,orderGrab.getAppointType());

        JSONObject orderPosition = new JSONObject()
                .fluentPut("buyerAddressLatitude", orderExtraData.getBuyerAddressLatitude())
                .fluentPut("buyerAddressLongitude", orderExtraData.getBuyerAddressLongitude());

        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());

        orderFeatures.put(FieldConstant.ORDER_LNG_LAT, orderExtraData.getBuyerAddressLongitude()+","+orderExtraData.getBuyerAddressLatitude());
        orderFeatures.put(FieldConstant.SECOND_DIVISION_ID, addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId()));
        orderFeatures.put(FieldConstant.THIRD_DIVISION_ID, orderBase.getThirdDivisionId());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());
        orderFeatures.put(FieldConstant.LV1_SERVE_ID,orderBase.getServeLevel1Ids());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(orderBase.getServeIds())) {
            List<ServeBaseInfoResp> serveBaseInfoRespList = orderConfigCommon.getServeList(orderBase.getServeIds(), orderBase.getBusinessLineId());
            if (CollectionUtils.isNotEmpty(serveBaseInfoRespList)) {
                List<Long> lv2ServeIdList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lv2ServeIdList)) {
                    orderFeatures.put(FieldConstant.LV2_SERVE_IDS, Joiner.on(",").join(lv2ServeIdList));
                }

            }

        }
        orderFeatures.put(FieldConstant.LV3_SERVE_IDS,orderBase.getServeIds());

        //订单包：如果是总包订单,需要把总包下单用户id传给智能推单 V7.7.2
        if (this.isEnterpriseOrder(orderBase)) {

            GetOrderBaseByGlobalIdRqt getOrderByGlobalIdRqt = new GetOrderBaseByGlobalIdRqt();
            getOrderByGlobalIdRqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
            GetOrderBaseByGlobalIdRsp getOrderBaseByGlobalIdRsp = tools.catchNoLog(() -> infoQueryApi.getOrderBaseByGlobalId(getOrderByGlobalIdRqt));
            if(Objects.isNull(getOrderBaseByGlobalIdRsp) || getOrderBaseByGlobalIdRsp.getOrderBase() == null){
                orderFeatures.put(FieldConstant.USER_ID, 0L);
            }else{
                orderFeatures.put(FieldConstant.USER_ID,"user".equals(getOrderBaseByGlobalIdRsp.getOrderBase().getFromAccountType()) ? getOrderBaseByGlobalIdRsp.getOrderBase().getFromAccountId() : 0L);
            }
        }else {
            orderFeatures.put(FieldConstant.USER_ID,orderBase.getAccountId());
        }

        orderFeatures.put(FieldConstant.ORDER_FROM,getOrderFrom(orderBase));
        orderFeatures.put(FieldConstant.TIME_LINESS_TAG,getTimeLinessTag(orderExtraData));

        return orderFeatures;
    }


    public List<ScorerAgent> scoreAgent(Long masterOrderId, Map<String,Integer> agentScoreId){
        final Set<String> agentSet = agentScoreId.keySet();
        //订单特征初始化
        final DefaultContext<String, Object> orderFeatures = initOrderFeatures(masterOrderId);
        final AgentDistributor agentDistributor = distributeFactory.generateByScorer(agentScoreId);
        System.out.println(orderFeatures);
        //获取特征
        featureRepository.orderFeatureReplenish(orderFeatures,agentDistributor.getOrderFeatureSet());
        final DefaultContext<String, DefaultContext<String, Object>> agentFeatures
                = featureRepository
                .agentFeatures(agentSet, agentDistributor.getAgentFeatureSet(),orderFeatures);
        System.out.println(agentFeatures);
        //评分
        final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                .withOrderId(String.valueOf(masterOrderId))
                .withType(FieldConstant.RANK_DETAIL)
                .build();
        final List<ScorerAgent> scorerAgent = agentDistributor.scorer(agentSet, orderFeatures, agentFeatures, rankDetail);
        return scorerAgent;
    }


    private boolean isEnterpriseOrder(OrderBase orderBase) {
        return AccountType.ENTERPRISE.code.equals(orderBase.getAccountType());
    }

    private String getOrderFrom(com.wanshifu.enterprise.order.domain.po.OrderBase orderBase){
        String orderFrom = orderBase.getOrderFrom();
        if(AccountType.USER.code.equals(orderBase.getFromAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom) || OrderFrom.THIRDPART.valueEn.equals(orderFrom)){
                return "site";
            }else  if(OrderFrom.APPLET.valueEn.equals(orderFrom)){
                return "family";
            }
        }else if(AccountType.ENTERPRISE.code.equals(orderBase.getFromAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom)){
                return "enterprise_inside";
            }else if(OrderFrom.APPLET.valueEn.equals(orderFrom)){
                return "family_enterprise";
            }else if(OrderFrom.ENTERPRISE_SYSTEM.valueEn.equals(orderFrom)){
                return "family_outside";
            }
        }

        return "";


    }

    private String getOrderFrom(OrderBase orderBase){
        String orderFrom = orderBase.getOrderFrom();
        if(AccountType.USER.code.equals(orderBase.getAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom) || OrderFrom.THIRDPART.valueEn.equals(orderFrom)){
                return "site";
            }else  if(OrderFrom.APPLET.valueEn.equals(orderFrom)){
                return "family";
            }
        }else if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())){
            if(OrderFrom.SITE.valueEn.equals(orderFrom)){
                return "enterprise_inside";
            }else if(OrderFrom.APPLET.valueEn.equals(orderFrom)){
                return "family_enterprise";
            }else if(OrderFrom.ENTERPRISE_SYSTEM.valueEn.equals(orderFrom)){
                return "family_outside";
            }
        }

        return "";


    }


    private List<String> getTimeLinessTag(OrderExtraData orderExtraData){

        List<String> timeLinessTagList = new ArrayList<>();


        if(orderExtraData.getTimerFlag() != null && orderExtraData.getTimerFlag() == 1){
            timeLinessTagList.add("regular_time_order");
        }

        if(orderExtraData.getEmergencyOrderFlag() != null && orderExtraData.getEmergencyOrderFlag() == 1){
            timeLinessTagList.add("emergency_order");
        }

        if(orderExtraData.getOnTimeOrderFlag() != null && orderExtraData.getOnTimeOrderFlag() == 1){
            timeLinessTagList.add("on_time_order");
        }

        if(orderExtraData.getExpectDoorInStartDate() != null){
            timeLinessTagList.add("expect_time_order");
        }

        return timeLinessTagList;
    }


    private List<String> getTimeLinessTag(com.wanshifu.enterprise.order.domain.po.OrderExtraData orderExtraData){

        List<String> timeLinessTagList = new ArrayList<>();


//        if(orderExtraData.getTimerFlag() != null && orderExtraData.getTimerFlag() == 1){
//            timeLinessTagList.add("regular_time_order");
//        }
//
//        if(orderExtraData.getEmergencyOrderFlag() != null && orderExtraData.getEmergencyOrderFlag() == 1){
//            timeLinessTagList.add("emergency_order");
//        }
//
//        if(orderExtraData.getOnTimeOrderFlag() != null && orderExtraData.getOnTimeOrderFlag() == 1){
//            timeLinessTagList.add("on_time_order");
//        }
//
//        if(orderExtraData.getExpectDoorInStartDate() != null){
//            timeLinessTagList.add("expect_time_order");
//        }

        return timeLinessTagList;
    }

    public OrderDistributor matchNewMasterDistributor(OrderDetailData orderDetailData,
                                                      DefaultContext<String, Object> orderFeatures, String distributeType){


        final OrderDistributor orderDistributor = new OrderDistributor();

        Integer businessLineId = orderDetailData.getBusinessLineId();

        NewMasterOrderDistributeStrategy newMasterOrderDistributeStrategy = newMasterOrderDistributeStrategyRepository.selectByCategoryIdAndCityId(businessLineId,distributeType,orderDetailData.getOrderCategoryId(),String.valueOf(orderDetailData.getSecondDivisionId()));


        if(Objects.isNull(newMasterOrderDistributeStrategy)){
            newMasterOrderDistributeStrategy = newMasterOrderDistributeStrategyRepository.selectByCategoryIdAndCityId(businessLineId,distributeType,orderDetailData.getOrderCategoryId(),"all");
        }


        if(Objects.isNull(newMasterOrderDistributeStrategy)){
            return orderDistributor;
        }


        List<DetailResp.DistributeStrategyVo> distributeStrategyVoList = JSON.parseArray(newMasterOrderDistributeStrategy.getDistributeStrategy(),DetailResp.DistributeStrategyVo.class);

        List<Integer> orderSelectStrategyIdList = distributeStrategyVoList.stream().map(DetailResp.DistributeStrategyVo::getOrderSelectStrategyId).collect(Collectors.toList());

        List<Integer> orderScoringStrategyIdList = distributeStrategyVoList.stream().map(DetailResp.DistributeStrategyVo::getOrderScoringStrategyId).collect(Collectors.toList());
        List<OrderSelectStrategy> orderSelectStrategyList = orderSelectStrategyRepository.selectByStrategyIdList(orderSelectStrategyIdList);
        List<OrderScoringStrategy> orderScoringStrategyList = orderScoringStrategyRepository.selectByStrategyIdList(orderScoringStrategyIdList);



        Map<Integer, OrderSelectStrategy> orderSelectStrategyMap = orderSelectStrategyList.stream()
                .collect(Collectors.toMap(OrderSelectStrategy::getStrategyId, strategy -> strategy));




        Map<Integer, OrderScoringStrategy> orderScoringStrategyMap = CollectionUtils.isNotEmpty(orderScoringStrategyList) ? orderScoringStrategyList.stream()
                .collect(Collectors.toMap(OrderScoringStrategy::getStrategyId, strategy -> strategy)) : null;

        /**
         * 初始化分配器
         */
        extractDistributeOrderFeatures(orderDistributor,Collections.singletonList(newMasterOrderDistributeStrategy));
        featureRepository.orderFeatureReplenish(orderFeatures,orderDistributor.getAdmittanceOrderFeatureSet());

        if (orderFeatures.containsKey("appoint_user")) {
            Object appointUser = orderFeatures.get("appoint_user");
            if (Objects.nonNull(appointUser)
                    && appointUser instanceof String
                    && !Strings.isNullOrEmpty(appointUser.toString())) {
                // 下单用户  需要转换成list才能使用containsAny运算
                orderFeatures.put("appoint_user", Stream.of(appointUser.toString().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            } else {
                // 该用户没有 人群  数据，则设置空数组
                orderFeatures.put("appoint_user", new ArrayList<>());
            }
        }

        /**
         * 过滤分配策略开启条件
         */
        final List<Integer> orderSelectStrategyIds =new ArrayList<>();
        final List<Integer> orderScorerStrategyIds =new ArrayList<>();

        final String distributeStrategyExpression =
                newMasterOrderDistributeStrategy.getDistributeStrategyExpression();
        final JSONArray distributeStrategyExpressionArray = JSONObject.parseArray(distributeStrategyExpression);
        for (int i = 0; i < distributeStrategyExpressionArray.size(); i++) {
            final JSONObject strategyGroup = distributeStrategyExpressionArray.getJSONObject(i);
            String groupOpen=strategyGroup.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
            final boolean expressBoolean = QlExpressStatic.QlExpressBoolean(
                    groupOpen, orderFeatures
            );
            if (expressBoolean) {
                orderSelectStrategyIds.add(strategyGroup.getInteger(FieldConstant.ORDER_SELECT_STRATEGY_ID));
                if(strategyGroup.containsKey(FieldConstant.ORDER_SCORING_STRATEGY_ID)){
                    orderScorerStrategyIds.add(strategyGroup.getInteger(FieldConstant.ORDER_SCORING_STRATEGY_ID));
                }
                orderDistributor.setDistributeRule(strategyGroup.getString(FieldConstant.ORDER_DISTRIBUTE_RULE));
                orderDistributor.setMatched(true);
            }
        }

        if (orderSelectStrategyIds.size()!=0) {
            final List<OrderSelectStrategy> orderSelectStrategies = new ArrayList<>();
            orderSelectStrategyIds.forEach(strategyId -> orderSelectStrategies.add(orderSelectStrategyMap.get(strategyId)));
            if(StringUtils.isNotBlank(orderSelectStrategies.get(0).getSelectRuleExpression())){
                orderDistributor.setSelectStrategy(orderSelectStrategies,orderFeatures);
            }else{
                orderDistributor.setSelectStrategy(orderSelectStrategies);
            }
        }
        if (CollectionUtils.isNotEmpty(orderScorerStrategyIds)) {
            final List<OrderScoringStrategy> orderScoringStrategies = new ArrayList<>();
            orderScorerStrategyIds.forEach(strategyId -> orderScoringStrategies.add(orderScoringStrategyMap.get(strategyId)));
            orderDistributor.setScoringStrategy(orderScoringStrategies);
        }


        return orderDistributor;
    }

    private void extractDistributeOrderFeatures(OrderDistributor orderDistributor,
                                                List<NewMasterOrderDistributeStrategy> orderDistributeStrategies){
        for (NewMasterOrderDistributeStrategy orderDistributeStrategy : orderDistributeStrategies) {
            final String distributeStrategyExpression =
                    orderDistributeStrategy.getDistributeStrategyExpression();
            final JSONArray distributeStrategyExpressionArray = JSONObject.parseArray(distributeStrategyExpression);
            for (int i = 0; i < distributeStrategyExpressionArray.size(); i++) {
                final JSONObject strategyGroup = distributeStrategyExpressionArray.getJSONObject(i);
                String groupOpenFeatures=strategyGroup.getString(FieldConstant.OPEN_CONDITION_RULE_PARAMS);
                if (groupOpenFeatures!=null) {
                    for (String featureCode : groupOpenFeatures.split(SymbolConstant.COMMA)) {
                        orderDistributor.addAdmittanceOrderFeatureCode(featureCode);
                    }
                }
            }
        }
    }

}
