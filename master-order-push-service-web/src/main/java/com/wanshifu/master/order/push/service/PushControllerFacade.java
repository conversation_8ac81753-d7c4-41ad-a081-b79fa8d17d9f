package com.wanshifu.master.order.push.service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.wanshifu.enterprise.order.domain.enums.BusinessLineEnum;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
import com.wanshifu.master.order.push.domain.common.*;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.enums.PushRound;
import com.wanshifu.master.order.push.domain.enums.PushStatus;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.PushConfig;
import com.wanshifu.master.order.push.domain.po.PushProgress;
import com.wanshifu.master.order.push.domain.rqt.DelayPushRqt;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.service.impl.Tools;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderGrab;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 推送控制上下文防腐层
 * 
 * <AUTHOR>
 * @since 2019-05-29
 */
@Component
@Slf4j
public class PushControllerFacade {

	@Resource
	private PushControllerService pushControllerService;

	@Resource
	private PushProgressRepository pushProgressRepository;

//	@Resource
//	private TableStoreClient tableStoreClient;

	@Resource
	private NormalOrderResourceApi normalOrderResourceApi;

	@Resource
	private BigdataOpenServiceApi bigdataOpenServiceApi;

	@Resource
	private MasterBaseEsRepository masterBaseEsRepository;

	@Resource
	private Tools tools;


	@Value("${abTry.bestOfferNum:10}")
	private Integer abTryBestOfferNum;


	@Value("${abTry.delayMinutesBetweenRounds:15}")
	private Integer abTryDelayMinutesBetweenRounds;


	@Value("${abTry.firstPushNewMasterNum:48}")
	private Integer abTryFirstPushNewMasterNum;

	@Value("${abTry.firstPushOldMasterNum:192}")
	private Integer abTryFirstPushOldMasterNum;
	
	@Value("${abTry.delayPushNewMasterNumPerRound:80}")
	private Integer abTryDelayPushNewMasterNumPerRound;

	@Value("${abTry.delayPushOldMasterNumPerRound:320}")
	private Integer abTryDelayPushOldMasterNumPerRound;




	public Integer push(PushFeature feature, String timeMark, List<PushMaster> pushMasterList,
					 JSONObject commonFeature, PushCommonObject pushCommonObject,
					 int firstTimeValidPush, int normalFirstTimeValidPush, Integer normalFirstMatchMasterNum) {
    	log.info("pushCommonObject:" + JSON.toJSONString(pushCommonObject));

    	if (pushCommonObject.getPushConfig() != null) {
            //旧模式固定轮
			log.info("fixedRoundsPush");
            return fixedRoundsPush(feature, timeMark, pushMasterList, commonFeature, pushCommonObject, firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum,pushCommonObject.getOrderDetailData());
        } else if (pushCommonObject.getPushRuleConfig() != null) {
            if (pushCommonObject.getPushRuleConfig().getFixedRoundsRule() != null) {
                PushConfig pushConfig = new PushConfig();
                BeanUtils.copyProperties(pushCommonObject.getPushRuleConfig().getFixedRoundsRule(), pushConfig);
                pushCommonObject.setPushConfig(pushConfig);
                return fixedRoundsPush(feature, timeMark, pushMasterList, commonFeature, pushCommonObject, firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum,pushCommonObject.getOrderDetailData());
			} else if (CollectionUtils.isNotEmpty(pushCommonObject.getPushRuleConfig().getDynamicRoundsRuleList())) {
				log.info("dynamicRoundsPush");
				return dynamicRoundsPush(feature, timeMark, pushMasterList, commonFeature, pushCommonObject, firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum);
            }
        }
    	return 0;
    }


    /**
     * 首轮推送
     * <p>
     * 师傅特征 推送上下文需要特征包括: skill_degree→技能熟悉程度（1-老师傅，0-新手）
     * direct_push_mark→强推标记（1） score→分数
     * <p>
     * 智能推单 (true:开；false：关)
     */
    public Integer fixedRoundsPush(PushFeature feature, String timeMark, List<PushMaster> pushMasterList, JSONObject commonFeature,
								PushCommonObject pushCommonObject, int firstTimeValidPush, Integer normalFirstTimeValidPush, Integer normalFirstMatchMasterNum,OrderDetailData orderDetailData) {
        Long globalOrderId = feature.getGlobalOrderId();
        if (CollectionUtils.isEmpty(pushMasterList)) {
            return 0;
        }
        // 获取推单参数
        Long userOrderId = (Long) feature.getOrderFeature().get(FieldConstant.USER_ORDER_ID);
        Long enterpriseOrderId = (Long) feature.getDimensionFeature().get(FieldConstant.ENTERPRISE_ORDER_ID);
        Long masterOrderId = (Long) feature.getDimensionFeature().get(FieldConstant.MASTER_ORDER_ID);
        PushParameter pushParameter = PushParameter.builder().setUserOrderId(userOrderId).setMasterOrderId(masterOrderId)
                .setGlobalOrderId(globalOrderId).setEnterpriseOrderId(enterpriseOrderId).build();
        // 首轮推送
        pushCommonObject.getPushConfig().setPushMark(PushRound.FIRST_PUSH.getCode());
		return pushControllerService.firstRoundPush(timeMark, pushParameter, pushMasterList, commonFeature, pushCommonObject,
				firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum,pushCommonObject.getOrderDetailData());
    }


    /**
     * 首轮推送
     * <p>
     * 师傅特征 推送上下文需要特征包括: skill_degree→技能熟悉程度（1-老师傅，0-新手）
     * direct_push_mark→强推标记（1） score→分数
     * <p>
     * 智能推单 (true:开；false：关)
     */
    public Integer dynamicRoundsPush(PushFeature feature, String timeMark, List<PushMaster> pushMasterList, JSONObject commonFeature,
								  PushCommonObject pushCommonObject, int firstTimeValidPush,Integer normalFirstTimeValidPush,Integer normalFirstMatchMasterNum) {
        Long globalOrderId = feature.getGlobalOrderId();
        if (CollectionUtils.isEmpty(pushMasterList)) {
            return 0;
        }
        // 获取推单参数
        Long userOrderId = (Long) feature.getOrderFeature().get(FieldConstant.USER_ORDER_ID);
        Long enterpriseOrderId = (Long) feature.getDimensionFeature().get(FieldConstant.ENTERPRISE_ORDER_ID);
        Long masterOrderId = (Long) feature.getDimensionFeature().get(FieldConstant.MASTER_ORDER_ID);
        PushParameter pushParameter = PushParameter.builder().setUserOrderId(userOrderId).setMasterOrderId(masterOrderId)
                .setGlobalOrderId(globalOrderId).setEnterpriseOrderId(enterpriseOrderId).build();

        return pushControllerService.dynamicRoundsPush(timeMark, pushParameter, pushMasterList, commonFeature, pushCommonObject, firstTimeValidPush
				, normalFirstTimeValidPush, normalFirstMatchMasterNum
		);
    }
	


	/**
	 * 非首轮推送
	 * 
	 * @param delayPushRqt
	 *            延时消息消息体:包括order_id和order_version
	 */
	public void delayPush(DelayPushRqt delayPushRqt) {
		try {
			// 获取推单参数
			Long userOrderId = delayPushRqt.getUserOrderId();
			Long masterOrderId = delayPushRqt.getMasterOrderId();
			Long enterpriseOrderId = delayPushRqt.getEnterpriseOrderId();
			Long globalOrderId = delayPushRqt.getGlobalOrderId();
			String orderVersion = delayPushRqt.getOrderVersion();
			JSONObject commonFeature = delayPushRqt.getCommonFeature();

			//构建参数
			PushParameter pushParameter=PushParameter.builder()
					.setUserOrderId(userOrderId)
					.setMasterOrderId(masterOrderId)
					.setGlobalOrderId(globalOrderId)
					.setEnterpriseOrderId(enterpriseOrderId)
					.build();

			// 获取推送进度
			PushProgress pushProgress = pushProgressRepository.getPushProgress(globalOrderId, orderVersion);
			if(PushStatus.STOP.code.equals(pushProgress.getPushStatus())){
				return ;
			}

			OrderStatus orderStatus = this.getOrderStatus(delayPushRqt.getPushConfig().getBestOfferNum(),globalOrderId,orderVersion,commonFeature.getInteger(FieldConstant.OCS_ORDER_OFFER_NUM));
			if (orderStatus.getOrderStatus() != OrderStatusValue.NORMAL) {

				if(!(BusinessLineEnum.FAMILY.code == delayPushRqt.getOrderDetailData().getBusinessLineId()  && orderStatus.getOrderStatus() == OrderStatusValue.MODIFY_AND_REPUSH)){
                    // 订单停止推送
					pushProgress.setPushStatus(PushStatus.STOP.code);
					pushProgress.setCurrentStopReason(orderStatus.getOrderStatus().toString());
					pushProgressRepository.updatePushOverMessage(globalOrderId, orderVersion, pushProgress.getPushStatus(), pushProgress.getCurrentStopReason());
					return;
				}

			}

			// 非首轮推送
			pushControllerService.delayRoundPush(pushParameter, orderVersion,commonFeature,delayPushRqt);
		} catch (Exception e) {
			log.error(String.format("分轮推单失败,delayPushRqt:%s", JSON.toJSONString(delayPushRqt)),e);
		}
	}


	private static final String ORDER_STATUS = "order_status";
    private static final String ORDER_GRAB_STATUS = "order_grab_status";

    /**
	 * 获取订单推送状态
	 *
	 * @return
	 */
	public void pushOrNotStatus(OrderStatus orderStatus, Integer bestOfferNum) {

		// t_order获取数据
		Long globalOrderId = orderStatus.getGlobalOrderId();
		OrderBaseComposite orderBaseComposite = normalOrderResourceApi.getOrderBaseComposite(globalOrderId);
		if (Objects.isNull(orderBaseComposite) || Objects.isNull(orderBaseComposite.getOrderBase()) || Objects.isNull(orderBaseComposite.getOrderGrab())) {
			orderStatus.setOrderStatus(OrderStatusValue.NORMAL);
			return;
		}

		OrderBase orderBase = orderBaseComposite.getOrderBase();
		OrderGrab orderGrab = orderBaseComposite.getOrderGrab();


		try {
			String pushOrderStatus = orderBase.getOrderStatus();
			if ("trading".equals(pushOrderStatus)) {
				if (orderGrab.getConfirmServeStatus() == 1) {
					orderStatus.setOrderStatus(OrderStatusValue.APPOINTED);
				}  else if (orderGrab.getOfferNumber() >= bestOfferNum) {
					orderStatus.setOrderStatus(OrderStatusValue.OFFER_REACH_THE_STANDARD);
				} else {
					orderStatus.setOrderStatus(OrderStatusValue.NORMAL);
					orderStatus.setOfferNum(orderGrab.getOfferNumber());
				}

			} else if ("close".equals(pushOrderStatus)) {
				orderStatus.setOrderStatus(OrderStatusValue.ClOSE);
			} else if ("finish".equals(pushOrderStatus)) {
				orderStatus.setOrderStatus(OrderStatusValue.FINISH);
			}
		} catch (Exception e) {
			orderStatus.setOrderStatus(OrderStatusValue.NORMAL);
		}
	}


	/**
	 * 获取报价数
	 *
	 * @param districtId
	 * @param categoryId
	 * @return
	 */
	private static final String OFFER_NUM = "offer_num";
	private static final String ORDER_GRAB_OFFER_NUM_CITY_USER = "order_grab_offer_num_enterprise_master";

//	private Integer getOfferNum(Row row) {
//		int offerNum = 0;
//		// 获取该订单特征行
//		try {
//			String offerNumStr = tableStoreClient.getValue(row, OFFER_NUM);
//			if(StringUtils.isNotBlank(offerNumStr)){
//				offerNum = Integer.valueOf(offerNumStr);
//			}
//
//		} catch (Exception e) {
//			log.error(String.format("getOfferNum error,row:%s", row.toString()),e);
//		}
//
//		return offerNum;
//	}



	public OrderStatus getOrderStatus(Integer bestOfferNum,Long globalOrderId,String orderVersion,Integer maxOfferNum){

		// 校验订单状态（关单,雇佣,报价达标,修改）
		OrderStatus orderStatus = new OrderStatus(globalOrderId, orderVersion);

		//与ocs后台最优报价数对比,取较小值
		if (maxOfferNum != null && maxOfferNum < bestOfferNum) {
			bestOfferNum = maxOfferNum;
		}

		return isContinuePush(bestOfferNum,orderStatus);


	}


	/**
	 * 是否继续延迟推送
	 */
	private OrderStatus isContinuePush(Integer bestOfferNum,OrderStatus orderStatus) {
		this.pushOrNotStatus(orderStatus, bestOfferNum);
		if (orderStatus.getOrderStatus() == OrderStatusValue.NORMAL && orderStatus.getOrderVersion() != null) {
			// 验证版本号
			if (!orderStatus.getOrderVersion().equals(pushProgressRepository.getOrderLatestVersion(orderStatus.getGlobalOrderId()))) {
				orderStatus.setOrderStatus(OrderStatusValue.MODIFY_AND_REPUSH);
				return orderStatus;
			}
		}
		return orderStatus;
	}


	private List<PushMaster> buildListToBeHandle(Set<String> masterSet,OrderDetailData orderDetailData) {
		// 初始化待处理师傅列表
		List<PushMaster> listToBeHandle = new ArrayList<>();
		// 构造待处理师傅列表
		masterSet.forEach(masterId -> listToBeHandle.add(PushMaster.builder().setMasterId(masterId).build()));

		List<List<String>> masterBatchList = LocalCollectionsUtil.groupByBatch(masterSet,100);

		List<MasterBaseSearch> masterBaseList = new ArrayList<>();

		for(List<String> batch : masterBatchList){
			BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
			boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", batch));
			EsResponse esResponse = masterBaseEsRepository.searchAfter(boolQueryBuilder, SortBuilders.fieldSort("masterId"),100,Integer.MAX_VALUE);
			if( CollectionUtils.isNotEmpty(esResponse.getDataList())){
				masterBaseList.addAll(esResponse.getDataList());
			}
		}

		Map<String, MasterBaseSearch> masterBaseMap = masterBaseList.stream()
				.collect(Collectors.toMap(MasterBaseSearch::getMasterId, masterBase -> masterBase));

		listToBeHandle.forEach(pushMaster -> {
			if(masterBaseMap.containsKey(pushMaster.getMasterId())){
                if (Objects.nonNull(masterBaseMap.get(pushMaster.getMasterId()).getCityDivisionId())
                        && !masterBaseMap.get(pushMaster.getMasterId()).getCityDivisionId().equals(orderDetailData.getSecondDivisionId())) {
                    pushMaster.setIsCrossCityPush(1);
                } else {
                    pushMaster.setIsCrossCityPush(0);
                }

			}else{
				pushMaster.setIsCrossCityPush(0);
			}
		});
		return listToBeHandle;
	}



	private List<PushMaster> buildListToBeHandle(List<PushMaster> pushMasterList,OrderDetailData orderDetailData) {

		Set<String> masterSet = pushMasterList.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());


		List<List<String>> masterBatchList = LocalCollectionsUtil.groupByBatch(masterSet,100);

		List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();


		for(List<String> masterList : masterBatchList){
			BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
			boolQuery.must(QueryBuilders.termsQuery("masterId",masterList));

			EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQuery,new Pageable(1,200),null);
			System.out.println("boolQueryBuilder:"+boolQuery.toString());
			if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
				masterBaseSearchList.addAll(esResponse.getDataList());
			}

		}

		Map<String, MasterBaseSearch> masterBaseMap = masterBaseSearchList.stream()
				.collect(Collectors.toMap(MasterBaseSearch::getMasterId, masterBase -> masterBase));

		pushMasterList.forEach(pushMaster -> {
			if(masterBaseMap.containsKey(pushMaster.getMasterId())){
				MasterBaseSearch masterBaseSearch = masterBaseMap.get(pushMaster.getMasterId());
				if(Objects.isNull(masterBaseSearch.getCityDivisionId())){
					masterBaseSearch.setCityDivisionId(0L);
				}
				pushMaster.setIsCrossCityPush(orderDetailData.getSecondDivisionId().equals(masterBaseMap.get(pushMaster.getMasterId()).getCityDivisionId()) ? 0 : 1);
			}else{
				pushMaster.setIsCrossCityPush(0);
			}
		});
		return pushMasterList;
	}

	private List<PushMaster> buildMeasurePriListToBeHandle(PushCommonObject pushCommonObject) {
		final Set<String> priorityPushMasterSet = pushCommonObject.getPriorityPushMasterSet();
		// 初始化待处理师傅列表
		List<PushMaster> listToBeHandle = new ArrayList<>();
		// 构造待处理师傅列表
		pushCommonObject.getMasterSet().stream().forEach(masterId -> {
			// 构造推送师傅
			PushMaster pushMaster = PushMaster.builder().setMasterId(masterId)
					.build();
			if (CollectionUtils.isNotEmpty(priorityPushMasterSet) && priorityPushMasterSet.contains(masterId)) {
				pushMaster.setScore(new BigDecimal(9999));
			}
			// 加入待处理列表
			listToBeHandle.add(pushMaster);
		});
		return listToBeHandle;
	}


    public void directPush(OrderDetailData orderDetailData, String orderVersion, Set<String> masterSet, JSONObject commonFeature) {

        Long globalOrderId = orderDetailData.getGlobalOrderId();
        // 获取推单参数
        Long masterOrderId = orderDetailData.getMasterOrderId();
        PushParameter pushParameter = PushParameter.builder().setMasterOrderId(masterOrderId)
                .setGlobalOrderId(globalOrderId).build();
        List<PushMaster> listToBeHandle = buildListToBeHandle(masterSet, orderDetailData);
        //直接推送
        commonFeature.put(FieldConstant.ORDER_LNG_LAT, orderDetailData.getOrderLngLat());
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, globalOrderId);
        pushControllerService.directPush(orderVersion, orderVersion, pushParameter,
                listToBeHandle, commonFeature, false,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
    }

	public boolean pushOrderToMaster(String orderVersion, List<OrderDetailData> orderDetailDataList,String masterId
								   , JSONObject commonFeature, Integer firstTimeValidPush,
								   String masterSourceType) {

		PushMaster pushMaster = PushMaster.builder().setMasterId(masterId).build();
		return pushControllerService.pushOrderToMaster(orderVersion,orderDetailDataList,pushMaster,commonFeature,firstTimeValidPush,masterSourceType);
	}



	public void directPush(OrderDetailData orderDetailData,String orderVersion , List<PushMaster> pushMasterList,JSONObject commonFeature) {

		Long globalOrderId = orderDetailData.getGlobalOrderId();
		// 获取推单参数
		Long masterOrderId = orderDetailData.getMasterOrderId();
		PushParameter pushParameter=PushParameter.builder().setMasterOrderId(masterOrderId)
				.setGlobalOrderId(globalOrderId).build();
		List<PushMaster> listToBeHandle = buildListToBeHandle(pushMasterList,orderDetailData);
		//直接推送
		commonFeature.put(FieldConstant.ORDER_LNG_LAT,orderDetailData.getOrderLngLat());
        pushControllerService.directPush(orderVersion, orderVersion, pushParameter,
                listToBeHandle, commonFeature, false,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
	}

	public void packageOrderPush(OrderDetailData orderDetailData, String orderVersion, String timeMark,
								 Set<String> masterList, JSONObject commonFeature) {
		Long globalOrderId=orderDetailData.getGlobalOrderId();
//		// 获取推单参数
		Long masterOrderId = orderDetailData.getMasterOrderId();
		PushParameter pushParameter=PushParameter.builder().setMasterOrderId(masterOrderId)
				.setGlobalOrderId(globalOrderId).build();
		List<PushMaster> listToBeHandle = buildListToBeHandle(masterList,orderDetailData);
//		// 首轮推送
        pushControllerService.packageOrderPush(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
	}


	public void exclusivePush(OrderDetailData orderDetailData,String orderVersion,String timeMark,
							  Set<String> masterList,JSONObject commonFeature) {
		Long globalOrderId=orderDetailData.getGlobalOrderId();
//		// 获取推单参数
		Long masterOrderId = orderDetailData.getMasterOrderId();
		PushParameter pushParameter=PushParameter.builder().setMasterOrderId(masterOrderId)
				.setGlobalOrderId(globalOrderId).build();
		List<PushMaster> listToBeHandle = buildListToBeHandle(masterList,orderDetailData);
//		// 首轮推送
        pushControllerService.exclusivePush(orderVersion, timeMark,
                pushParameter, listToBeHandle, commonFeature,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
	}

	public void agreementMasterPush(OrderDetailData orderDetailData, String orderVersion, String timeMark,
								 Set<String> masterList, JSONObject commonFeature) {
		Long globalOrderId=orderDetailData.getGlobalOrderId();
//		// 获取推单参数
		Long masterOrderId = orderDetailData.getMasterOrderId();
        PushParameter pushParameter = PushParameter.builder().setMasterOrderId(masterOrderId)
                .setGlobalOrderId(globalOrderId).build();
		List<PushMaster> listToBeHandle = buildListToBeHandle(masterList,orderDetailData);
//		// 首轮推送
        pushControllerService.agreementMasterPush(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
	}

    public void cooperationBusinessMasterPush(OrderDetailData orderDetailData, String orderVersion, String timeMark,
                                    Set<String> masterList, JSONObject commonFeature) {
        Long globalOrderId = orderDetailData.getGlobalOrderId();
//		// 获取推单参数
        Long masterOrderId = orderDetailData.getMasterOrderId();
        PushParameter pushParameter = PushParameter.builder().setMasterOrderId(masterOrderId)
                .setGlobalOrderId(globalOrderId).build();
        List<PushMaster> listToBeHandle = buildListToBeHandle(masterList, orderDetailData);
//		// 首轮推送
        pushControllerService.cooperationBusinessMasterPush(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
    }

    public void afterTechniqueVerifyMasterPush(OrderDetailData orderDetailData, String orderVersion, String timeMark,
                                              Set<String> masterList, JSONObject commonFeature) {
        Long globalOrderId = orderDetailData.getGlobalOrderId();
//		// 获取推单参数
        Long masterOrderId = orderDetailData.getMasterOrderId();
        PushParameter pushParameter = PushParameter.builder().setMasterOrderId(masterOrderId)
                .setGlobalOrderId(globalOrderId).build();
        List<PushMaster> listToBeHandle = buildListToBeHandle(masterList, orderDetailData);
//		// 首轮推送
        pushControllerService.afterTechniqueVerifyMasterPush(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
    }

    public void familyAgreementMasterPush(OrderDetailData orderDetailData, String orderVersion, String timeMark,
                                              Set<String> masterList, JSONObject commonFeature) {
        Long globalOrderId = orderDetailData.getGlobalOrderId();
//		// 获取推单参数
        Long masterOrderId = orderDetailData.getMasterOrderId();
        PushParameter pushParameter = PushParameter.builder().setMasterOrderId(masterOrderId)
                .setGlobalOrderId(globalOrderId).build();
        List<PushMaster> listToBeHandle = buildListToBeHandle(masterList, orderDetailData);
//		// 首轮推送
        pushControllerService.familyAgreementMasterPush(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
    }


	public void newModelPush(OrderDetailData orderDetailData, String orderVersion, String timeMark,
									Set<String> masterList, JSONObject commonFeature) {
		Long globalOrderId=orderDetailData.getGlobalOrderId();
//		// 获取推单参数
		Long masterOrderId = orderDetailData.getMasterOrderId();
		PushParameter pushParameter=PushParameter.builder().setMasterOrderId(masterOrderId)
				.setGlobalOrderId(globalOrderId).build();
		List<PushMaster> listToBeHandle = buildListToBeHandle(masterList,orderDetailData);
//		// 首轮推送
        pushControllerService.newModelPush(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
	}


	public void wheelRoundsPush(OrderDetailData orderDetailData,String orderVersion , Set<String> masterSet,JSONObject commonFeature) {

		Long globalOrderId = orderDetailData.getGlobalOrderId();
		// 获取推单参数
		Long masterOrderId = orderDetailData.getMasterOrderId();
		PushParameter pushParameter=PushParameter.builder().setMasterOrderId(masterOrderId)
				.setGlobalOrderId(globalOrderId).build();
		List<PushMaster> listToBeHandle = buildListToBeHandle(masterSet,orderDetailData);
		//直接推送
		commonFeature.put(FieldConstant.ORDER_LNG_LAT,orderDetailData.getOrderLngLat());
        pushControllerService.wheelRoundsPush(orderVersion, orderVersion, pushParameter, listToBeHandle, commonFeature,
                orderDetailData.getPushExtraData().getMasterSourceType(),orderDetailData);
	}


	public void interfereOrderPush(List<PushMaster> pushMasterList,Long globalOrderId,Long orderId){
		pushControllerService.interfereOrderPush(pushMasterList,globalOrderId,orderId);
	}


	}
