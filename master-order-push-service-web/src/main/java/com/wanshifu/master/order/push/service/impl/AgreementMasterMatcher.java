package com.wanshifu.master.order.push.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.base.address.domain.po.PositionConvert;
import com.wanshifu.fee.center.api.FeeRuleApi;
import com.wanshifu.fee.center.domain.dto.AccountInfo;
import com.wanshifu.fee.center.domain.dto.AddressInfo;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateBatchReq;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateReq;
import com.wanshifu.fee.center.domain.request.feeRule.BizRuleBatchReq;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateBatchResp;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.supply.demand.domain.api.req.GetFenceInfoByDivisionLv3IdsReq;
import com.wanshifu.iop.supply.demand.domain.api.req.HitByOrderInfoReq;
import com.wanshifu.iop.supply.demand.domain.api.resp.GetFenceInfoByDivisionLv3IdsResp;
import com.wanshifu.iop.supply.demand.domain.api.resp.HitByOrderInfoResp;
import com.wanshifu.iop.supply.demand.service.api.InterFenceServiceApi;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.DistanceUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.po.OrderBase;
import com.wanshifu.order.offer.domains.po.OrderServiceAttributeInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.geo.GeoPoint;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.GeoDistanceQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 协议师傅匹配器
 * <AUTHOR>
 */
@Slf4j
@Component("agreement_master")
public class AgreementMasterMatcher extends AbstractOrderMasterMatcher{


    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private PushProgressRepository pushProgressRepository;


    @Resource
    private PushQueueService pushQueueService;


    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private MasterUserRepository masterUserRepository;

    @Resource
    private MasterEnterpriseRepository masterEnterpriseRepository;


    @Resource
    private DistributeFactory distributeFactory;


    @Value("${night.push.switch}")
    private String nightPushSwitch;

    @Value("${night.push.start.time}")
    private String nightPushStartTime;

    @Value("${night.push.end.time}")
    private String nightPushEndTime;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;

    @Resource
    private FeeRuleApi feeRuleApi;

    @Resource
    private AddressApi addressApi;


    @Value("${agreement.master.push.switch}")
    private String agreementMasterPushSwitch;


    @Value("${agreement.master.user.blacklist}")
    private String agreementMasterUserBlacklist;


    @Value("${agreement.master.order.distance:5}")
    private Integer agreementMasterOrderDistance;


    @Value("${agreement.master.center.point.distance:5}")
    private Integer agreementMasterCenterPointDistance;


    @Value("${agreement.master.centerPoint.switch:off}")
    private String agreementMasterCenterPointSwitch;


    @Value("${agreement.master.order.master.distance.switch:off}")
    private String agreementMasterOrderMasterDistanceSwitch;


    @Value("${agreement.master.city:all}")
    private String agreementMasterCity;


    @Value("${agreement.master.blackList.city:0}")
    private String agreementMasterBlackListCity;


    @Value("${agreement.special.city.list:441900}")
    private String agreementSpecialCityList;


    @Value("${agreement.serve.blackList:0}")
    private String agreementServeBlackList;



    @Resource
    private HBaseClient hBaseClient;


    @Resource
    private AddressCommon addressCommon;


    @Resource
    private FeatureRepository featureRepository;


    @Resource
    private AgreementMasterMatchRepository agreementMasterMatchRepository;


    @Resource
    private ApolloConfigUtils apolloConfigUtils;


    @Resource
    private Tools tools;

    /**
     * 获取业务范围
     * @param orderDetailData
     * @return
     */
    public List<String> getCooperationBusiness(OrderDetailData orderDetailData) {

        String accountType = orderDetailData.getAccountType();
        if (StrUtil.isEmpty(accountType)) {
            return null;
        }
        if (OrderFrom.IKEA.valueEn.equals(orderDetailData.getAccountType())) {
            return null;
        }
        Integer businessLineId = orderDetailData.getBusinessLineId();
        if(businessLineId != 1 && businessLineId != 2){
            return null;
        }
        if (businessLineId == 1) {
            if(AccountType.USER.code.equals(accountType)){
                return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.FINISHED_PRODUCT.code);
            }else if(AccountType.ENTERPRISE.code.equals(accountType)){
                return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.ENTERPRISE.code);
            }
        }else if (businessLineId == 2) {
            return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.FAMILY.code);
        }
        return null;
    }


    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData){


        if("off".equals(agreementMasterPushSwitch)){
            return false;
        }


        if(StringUtils.isNotBlank(orderDetailData.getPushExtraData().getMatchSceneCode()) && (!MatchSceneCode.ORDER_CREATE.getCode().equals(orderDetailData.getPushExtraData().getMatchSceneCode()))){
            return false;
        }


        if(apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())){
            this.insertAgreementMasterMatch(orderDetailData,"该城市订单不推单");
            return false;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(pushMode) && (!PushMode.AGREEMENT_MASTER.code.equals(pushMode))){
            return Boolean.FALSE;
        }

        if(AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())){
            return false;
        }


        if(!checkWhitelist(orderDetailData)){
            if (StringUtils.isNotEmpty(agreementMasterUserBlacklist) && (!"0".equals(agreementMasterUserBlacklist)) && Objects.nonNull(orderDetailData.getUserId()) && orderDetailData.getUserId() > 0) {
                final List<String> forbiddenUserList =
                        new ArrayList<>(Arrays.asList(agreementMasterUserBlacklist.split(",")));
                if (forbiddenUserList.contains(String.valueOf(orderDetailData.getUserId()))) {
                    this.insertAgreementMasterMatch(orderDetailData,"商家在黑名单");
                    log.info("checkPreCondition filter:" + JSON.toJSONString(orderDetailData));
                    return false;
                }
            }
        }

//
//        if(!(AccountType.USER.code.equals(orderDetailData.getAccountType()) && orderDetailData.getBusinessLineId() == 1
//                && OrderFrom.SITE.valueEn.equals(orderDetailData.getOrderFrom()))){
//            return false;
//        }

        final Integer emergencyOrderFlag = orderDetailData.getEmergencyOrderFlag();
        if (SymbolConstant.ONE.equals(emergencyOrderFlag)) {
            log.info("debug1::{},{}",orderDetailData.getMasterOrderId(),emergencyOrderFlag);
            return false;
        }
        List<String> cooperationBusinessList = getCooperationBusiness(orderDetailData);
        if (CollectionUtils.isEmpty(cooperationBusinessList)) {
            log.info("debug2::{},{}",orderDetailData.getMasterOrderId(),cooperationBusinessList);
            return false;
        }

        if(AppointType.ADVANCE_PAY.value.equals(orderDetailData.getAppointType())){
            return false;
        }


        if(!checkCityAndServe(orderDetailData)){
            return false;
        }


        return true;
    }


    private boolean checkWhitelist(OrderDetailData orderDetailData){
        //不限制订单服务的城市
        if(StringUtils.isNotBlank(agreementMasterCity)){
            if("all".equals(agreementMasterCity)){
                return true;
            }else{
                List<Long> cityList = Arrays.stream(agreementMasterCity.split(",")).map(Long::valueOf).collect(Collectors.toList());
                if(cityList.contains(orderDetailData.getSecondDivisionId())){
                    return true;
                }
            }
        }
        return false;
    }


    private boolean checkCityAndServe(OrderDetailData orderDetailData){

        if(StringUtils.isNotBlank(agreementMasterBlackListCity)){
            if("all".equals(agreementMasterBlackListCity)){
                return false;
            }else{
                List<Long> blackListCityList = Arrays.stream(agreementMasterBlackListCity.split(",")).map(Long::valueOf).collect(Collectors.toList());
                if(blackListCityList.contains(orderDetailData.getSecondDivisionId())){
                    log.info("checkCityAndServe filter:" + JSON.toJSONString(orderDetailData));
                    return false;
                }
            }
        }



        //不限制订单服务的城市
        if(StringUtils.isNotBlank(agreementMasterCity)){
            if("all".equals(agreementMasterCity)){
                return true;
            }else{
                List<Long> cityList = Arrays.stream(agreementMasterCity.split(",")).map(Long::valueOf).collect(Collectors.toList());
                if(cityList.contains(orderDetailData.getSecondDivisionId())){
                    return true;
                }
            }
        }


        //其他城市限制服务模式和服务
        if(orderDetailData.getBusinessLineId() == 1 && orderDetailData.getAppointType() == 4){
            List<Long> orderServeIdList = Arrays.stream(orderDetailData.getLv3ServeIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
            List<Long> specialServeList = Arrays.stream(agreementServeBlackList.split(",")).map(Long::valueOf).collect(Collectors.toList());
            if(!Collections.disjoint(orderServeIdList,specialServeList)){
                //如果订单是平台一口价，服务包含板式床安装或实木床安装或衣柜安装则不推送协议师傅
                log.info("checkCityAndServe filter:" + JSON.toJSONString(orderDetailData));
                return false;
            }

        }
        return true;
    }


    private List<String> getTimeLinessTag(OrderDetailData orderDetailData){

        List<String> timeLinessTagList = new ArrayList<>();


        if(orderDetailData.getTimerFlag() != null && orderDetailData.getTimerFlag() == 1){
            timeLinessTagList.add("regular_time_order");
        }

        if(orderDetailData.getEmergencyOrderFlag() != null && orderDetailData.getEmergencyOrderFlag() == 1){
            timeLinessTagList.add("emergency_order");
        }

        if(orderDetailData.getOnTimeOrderFlag() != null && orderDetailData.getOnTimeOrderFlag() == 1){
            timeLinessTagList.add("on_time_order");
        }

        if(orderDetailData.getExpectDoorInStartDate() != null){
            timeLinessTagList.add("expect_time_order");
        }

        return timeLinessTagList;
    }


    private List<AgreementMaster> filterRestMaster(List<AgreementMaster> agreementMasterList){

        List<String> masterSet = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).distinct().collect(Collectors.toList());
        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,100);
        Set<String> resultSet = new HashSet<>();

        for(List<String> masterList : batchList){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("masterId",masterList));
            boolQuery.must(QueryBuilders.termQuery("restState", 1L));

            List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQuery,new Pageable(pageNum,pageSize),null);
                System.out.println("boolQueryBuilder:"+boolQuery.toString());
                if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    masterBaseSearchList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }
            if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
                resultSet.addAll(masterBaseSearchList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet()));
            }

        }

        if(CollectionUtils.isEmpty(resultSet)){
            return null;
        }

        return agreementMasterList.stream().filter(agreementMaster -> resultSet.contains(String.valueOf(agreementMaster.getMasterId()))).collect(Collectors.toList());

    }



    public void insertAgreementMasterMatch(OrderDetailData orderDetailData,String matchFailReason){
        AgreementMasterMatch agreementMasterMatch = new AgreementMasterMatch();
        agreementMasterMatch.setOrderId(orderDetailData.getMasterOrderId());
        agreementMasterMatch.setUserId(orderDetailData.getAccountId());
        agreementMasterMatch.setAppointType(orderDetailData.getAppointType());
        agreementMasterMatch.setMatchDivisionLevel((Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0) ? 4 : 3);
        agreementMasterMatch.setMasterId(0L);
        agreementMasterMatch.setRecruitId(-1L);
        agreementMasterMatch.setOrderNo(orderDetailData.getOrderNo());
        agreementMasterMatch.setOrderCreateTime(orderDetailData.getOrderCreateTime());
        agreementMasterMatch.setIsMatchSucc(0);
        agreementMasterMatch.setMatchFailReason(matchFailReason);
        agreementMasterMatch.setCreateTime(new Date());
        agreementMasterMatch.setUpdateTime(new Date());
        this.agreementMasterMatchRepository.insertSelective(agreementMasterMatch);
    }

    /**
     * 匹配师傅
     * @param orderDetailData
     * @param masterCondition
     * @return
     */
    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition){

        List<String> cooperationBusinessList = getCooperationBusiness(orderDetailData);


        List<AgreementMasterBase> agreementMasterBaseList = null;
        if(checkCityGeoFence(orderDetailData.getSecondDivisionId())){
            agreementMasterBaseList = matchAgreementMasterByGeoFence(orderDetailData,masterCondition,cooperationBusinessList);
        }
        
        if(CollectionUtil.isEmpty(agreementMasterBaseList)){
            agreementMasterBaseList = matchAgreementMaster(orderDetailData,masterCondition,cooperationBusinessList);
        }

        log.info("matchAgreementMaster result:" + JSON.toJSONString(agreementMasterBaseList));


        Set<String> masterIdSet = null;
        if(CollectionUtils.isNotEmpty(agreementMasterBaseList)){
            masterIdSet = agreementMasterBaseList.stream().map(AgreementMasterBase::getMasterId).collect(Collectors.toSet());
        }else{
            AgreementMasterMatch agreementMasterMatch = agreementMasterMatchRepository.selectByOrderIdAndOrderVersion(orderDetailData.getMasterOrderId(),orderDetailData.getOrderVersion());
            if(Objects.isNull(agreementMasterMatch)){
                insertAgreementMasterMatch(orderDetailData,"无招募师傅");
            }

        }
        

//        Set<String> pushMasterIdSet = agreementMasterBaseList.stream().map(AgreementMasterBase::getMasterId).collect(Collectors.toSet());
        log.info("agreementMasterBaseList:" + JSON.toJSONString(agreementMasterBaseList));
        MatchMasterResult masterResult = new MatchMasterResult(masterIdSet);
        masterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL,masterCondition.getDivisionMatchLevel());
        masterResult.putExtraData("agreement_master_list",agreementMasterBaseList);
        masterResult.putExtraData("agreement_master_match",null);

        return masterResult;
    }


    @Resource
    private InterFenceServiceApi interFenceServiceApi;

    /**
     * 根据电子围栏匹配协议师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @param cooperationBusinessList
     * @return
     */
    private List<AgreementMasterBase> matchAgreementMasterByGeoFence(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition,List<String> cooperationBusinessList){

        if(StringUtils.isBlank(orderDetailData.getOrderLngLat())){
            return null;
        }

        if(Objects.isNull(orderDetailData.getFourthDivisionId()) || orderDetailData.getFourthDivisionId() == 0){
            return null;
        }

        HitByOrderInfoReq hitByOrderInfoReq = new HitByOrderInfoReq();
        hitByOrderInfoReq.setServeId(Long.valueOf(orderDetailData.getLv1ServeIds()));
        hitByOrderInfoReq.setLatitude(orderDetailData.getOrderLngLat());
        String[] latLngArray = orderDetailData.getOrderLngLat().split(",");
        hitByOrderInfoReq.setLatitude(latLngArray[0]);
        hitByOrderInfoReq.setLongitude(latLngArray[1]);
        hitByOrderInfoReq.setLv3DivisionId(orderDetailData.getSecondDivisionId());
        log.info("hitByOrderInfoReq:" + JSON.toJSONString(hitByOrderInfoReq));
        HitByOrderInfoResp hitByOrderInfoResp = tools.catchLog(() -> interFenceServiceApi.hitByOrderInfo(hitByOrderInfoReq),"interFenceServiceApi.hitByOrderInfo",hitByOrderInfoReq);
        log.info("hitByOrderInfoResp:" + JSON.toJSONString(hitByOrderInfoResp));
        if(Objects.isNull(hitByOrderInfoResp) || Objects.isNull(hitByOrderInfoResp.getFenceId()) || hitByOrderInfoResp.getFenceId() <= 0){
            return null;
        }

        Long fenceId = hitByOrderInfoResp.getFenceId();

        return matchAgreementMasterByGeoFenceId(orderDetailData,masterMatchCondition,fenceId,cooperationBusinessList);
    }


    private List<AgreementMasterBase> matchAgreementMasterByGeoFenceId(OrderDetailData orderDetailData,MasterMatchCondition
            masterMatchCondition,Long geoFenceId, List<String> cooperationBusinessList) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus", 1));

        boolQueryBuilder.must(QueryBuilders.termQuery("recruitDistrictMethod", RecruitDistrictMethod.GEO_FENCE.getCode()));


        boolQueryBuilder.must(QueryBuilders.termQuery("geoFenceIds", String.valueOf(geoFenceId)));

        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));


        boolQueryBuilder.must(QueryBuilders.termQuery("masterCityDivisionId", orderDetailData.getSecondDivisionId()));
        boolQueryBuilder.must(QueryBuilders.termQuery("appointType",  String.valueOf(orderDetailData.getAppointType())));


        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", orderDetailData.getPushExtraData().getMasterSourceType()));



        if(Objects.nonNull(orderDetailData.getUserId()) && orderDetailData.getUserId() > 0){
            BoolQueryBuilder userBoolQueryBuilder = QueryBuilders.boolQuery();
            userBoolQueryBuilder.should(QueryBuilders.termsQuery("userIds", Collections.singletonList(orderDetailData.getUserId())));
            BoolQueryBuilder userNotExistQueryBuilder = QueryBuilders.boolQuery();
            userNotExistQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
            userBoolQueryBuilder.should(userNotExistQueryBuilder);
            userBoolQueryBuilder.minimumShouldMatch(1);
            boolQueryBuilder.must(userBoolQueryBuilder);
        }else{
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
        }


        boolQueryBuilder.mustNot(QueryBuilders.termQuery("pricingType", RecruitPricingType.NO_COOPERATION_PRICE.getCode()));


        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
        if (CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())) {
            return null;
        }
        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));

        log.info("matchAgreementMasterByFenceId searchAgreementMaster request:" + boolQueryBuilder.toString());


        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }

        log.info("matchAgreementMasterByFenceId agreementMasterList:" + JSON.toJSONString(agreementMasterList));

        return filterAgreementMaster(agreementMasterList,orderDetailData,masterMatchCondition);


    }


    /**
     * 查询城市电子围栏开启状态
     * @param cityDivisionId
     * @return
     */
    private boolean checkCityGeoFence(Long cityDivisionId){

        if(!apolloConfigUtils.isOpenAgreementGeoFence()){
            return false;
        }

        GetFenceInfoByDivisionLv3IdsReq req = new GetFenceInfoByDivisionLv3IdsReq();
        List<Long> cityList = new ArrayList<>();
        cityList.add(cityDivisionId);
        req.setLv3DivisionIds(cityList);
        List<GetFenceInfoByDivisionLv3IdsResp> respList = tools.catchLog(() -> interFenceServiceApi.getInfoByDivisionLv3Id(req),"InterFenceServiceApi.getInfoByDivisionLv3Id",req);
        if(CollectionUtils.isNotEmpty(respList)){
            return "open".equals(respList.get(0).getOpenStatus());
        }
        return false;
    }


    /**
     * 协议师傅分配
     * @param scorerMasterList
     * @param orderDistributor
     * @param agreementMasterBaseList
     * @return
     */
    public List<AgreementMasterBase> distributeAgreementMaster(List<ScorerMaster> scorerMasterList,OrderDistributor orderDistributor,
                                                               List<AgreementMasterBase> agreementMasterBaseList){

        agreementMasterBaseList.forEach(agreementMasterBase -> {
            if(agreementMasterBase.getCooperationPrice() == null){
                agreementMasterBase.setCooperationPrice(BigDecimal.ZERO);
            }
        });


        String distributeRule = orderDistributor.getDistributeRule();
        Integer distributeNum = Objects.nonNull(orderDistributor.getDistributeNum()) ? orderDistributor.getDistributeNum() : Integer.MAX_VALUE;
        List<AgreementMasterBase> agreementMasterBases = null;
        List<ScorerMaster> scorerMasters = null;

        if(OrderDistributeRule.SCORING_ORDER.getCode().equals(distributeRule)){
            Collections.sort(scorerMasterList);
            scorerMasters = scorerMasterList.size() <= distributeNum ? scorerMasterList : scorerMasterList.subList(0,distributeNum);
        }else if(OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(distributeRule)){
            Collections.sort(scorerMasterList);
            scorerMasters = scorerMasterList.size() <= 50 ? scorerMasterList : scorerMasterList.subList(0,50);
            Collections.shuffle(scorerMasters);
            scorerMasters = scorerMasterList.size() <= distributeNum ? scorerMasterList : scorerMasterList.subList(0,distributeNum);
        }else if(OrderDistributeRule.SCORING_ORDER_TOP50_LOW_PRICE_PRIORITY.getCode().equals(distributeRule)){
            Collections.sort(scorerMasterList);
            scorerMasters = scorerMasterList.size() <= 50 ? scorerMasterList : scorerMasterList.subList(0,50);
            Set<String> masterSet = scorerMasters.stream().map(ScorerMaster::getMasterId).collect(Collectors.toSet());
            agreementMasterBases = agreementMasterBaseList.stream().filter(agreementMasterBase -> masterSet.contains(agreementMasterBase.getMasterId())).collect(Collectors.toList());
            Collections.sort(agreementMasterBases);
            agreementMasterBases = agreementMasterBases.size() <= distributeNum ? agreementMasterBases : agreementMasterBases.subList(0,distributeNum);

        }else if(OrderDistributeRule.LOW_PRICE_PRIORITY.getCode().equals(distributeRule)){
            Set<String> masterSet = scorerMasters.stream().map(ScorerMaster::getMasterId).collect(Collectors.toSet());
            agreementMasterBases = agreementMasterBaseList.stream().filter(agreementMasterBase -> masterSet.contains(agreementMasterBase.getMasterId())).collect(Collectors.toList());
            Collections.sort(agreementMasterBases);
            agreementMasterBases = agreementMasterBases.size() <= distributeNum ? agreementMasterBases : agreementMasterBases.subList(0,distributeNum);
        }


        if(CollectionUtils.isNotEmpty(scorerMasters)){
            List<AgreementMasterBase> finalAgreementMasterBaseList = new ArrayList<>();
            Map<String, AgreementMasterBase> agreementMasterBaseMap = agreementMasterBaseList.stream().collect(Collectors.toMap(AgreementMasterBase::getMasterId, Function.identity()));
            for(int i = 0;i < scorerMasters.size();i++){
                AgreementMasterBase agreementMasterBase = agreementMasterBaseMap.get(scorerMasters.get(i).getMasterId());
                agreementMasterBase.setOfferSort(i + 1);
                finalAgreementMasterBaseList.add(agreementMasterBase);
            }
            return finalAgreementMasterBaseList;
        }

        if(CollectionUtils.isNotEmpty(agreementMasterBases)){
            for(int i = 0;i < agreementMasterBases.size();i++){
                agreementMasterBases.get(i).setOfferSort(i+1);
            }
            return agreementMasterBases;
        }

        return null;
    }


    /**
     * 过滤已推送的协议师傅
     * @param masterIdSet
     * @param getGlobalOrderTraceId
     * @param agreementMasterMatchList
     */
    public void filterPushedMaster(Set<String> masterIdSet,Long getGlobalOrderTraceId,List<AgreementMasterMatch> agreementMasterMatchList){
        String masterIdStr = hBaseClient.querySingle("order_push",String.valueOf(getGlobalOrderTraceId),"agreement");
        if(org.apache.commons.lang3.StringUtils.isNotBlank(masterIdStr)){
            Set<String> pushedMasterIdSet = Arrays.stream(masterIdStr.split(",")).collect(Collectors.toSet());
            agreementMasterMatchList.forEach(agreementMasterMatch -> {
                if(pushedMasterIdSet.contains(String.valueOf(agreementMasterMatch.getMasterId()))){
                    agreementMasterMatch.setIsMatchSucc(0);
                    agreementMasterMatch.setMatchFailReason("已推送的协议师傅");
                }
            });

            masterIdSet.removeAll(pushedMasterIdSet);
        }

        log.info("after filterPushedMaster " ,masterIdSet);

    }


//    public void filterNewContractRestMaster(Set<String> masterIdSet, List<AgreementMasterMatch> agreementMasterMatchList){
//        List<Master> masterList = masterRepository.selectByMasterIdSet(masterIdSet);
//        Date currentTime = new Date();
//        if(CollectionUtils.isNotEmpty(masterList)){
//
//            Set<String> filterMasterIdSet = masterList.stream().filter(master -> Objects.nonNull(master.getNewMasterRestStartTime()) && Objects.nonNull(master.getNewMasterRestEndTime()) && currentTime.compareTo(master.getNewMasterRestStartTime()) >= 0
//                    && currentTime.compareTo(master.getNewMasterRestEndTime()) <= 0).map(Master::getMasterId).collect(Collectors.toSet());
//
//            agreementMasterMatchList.forEach(agreementMasterMatch -> {
//                if(filterMasterIdSet.contains(String.valueOf(agreementMasterMatch.getMasterId()))){
//                    agreementMasterMatch.setIsMatchSucc(0);
//                    agreementMasterMatch.setMatchFailReason("合约休息师傅");
//                }
//            });
//
//            masterIdSet.removeAll(filterMasterIdSet);
//        }
//    }

    /**
     * 根据每日接单上限过滤协议师傅
     * @param agreementMasterList
     * @return
     */
    private List<AgreementMaster> filterByDailyOrderCnt(List<AgreementMaster> agreementMasterList){

        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }


        String today = DateFormatterUtil.getNow();
        List<String> rowKeyList = agreementMasterList.stream().map(agreementMaster -> agreementMaster.getMasterId() + "_" + agreementMaster.getRecruitId() + "_" + today).collect(Collectors.toList());
        List<String> fieldColumnList = new ArrayList<>();
        fieldColumnList.add("master_id");
        fieldColumnList.add("recruit_id");
        fieldColumnList.add("order_cnt");
        JSONArray resultArray = hBaseClient.batchQuery(rowKeyList,fieldColumnList,"master_recruit_daily");
        Map<String,Integer> orderCntMap = new HashMap<>();

        if(Objects.nonNull(resultArray) && resultArray.size() > 0){
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) resultArray.get(i);
                String masterId = (String)jsonObject.get("master_id");
                String recruitId = (String)jsonObject.get("recruit_id");
                if(jsonObject.containsKey("order_cnt")){
                    String orderCnt = (String)jsonObject.get("order_cnt");
                    if(org.apache.commons.lang.StringUtils.isNotBlank(orderCnt)){
                        orderCntMap.put(masterId + "_" + recruitId,Integer.valueOf(orderCnt));
                    }
                }
            }
        }


        return agreementMasterList.stream().filter(agreementMaster -> agreementMaster.getMaxDailyOrder() == 0 ||  orderCntMap.getOrDefault(agreementMaster.getMasterId() + "_" + agreementMaster.getRecruitId(),0) < agreementMaster.getMaxDailyOrder()).collect(Collectors.toList());
    }




    /**
     * 匹配非电子围栏协议师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private List<AgreementMasterBase> matchAgreementMaster(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition,List<String> cooperationBusinessList){
        if(CollectionUtils.isEmpty(cooperationBusinessList)){
            return null;
        }
        List<AgreementMasterBase> agreementMasterList = matchAgreementMaster(orderDetailData, masterMatchCondition, String.valueOf(orderDetailData.getAppointType()),cooperationBusinessList);
        if (CollectionUtils.isEmpty(agreementMasterList)) {
            //匹配不到，再按全部交易模式匹配
            agreementMasterList = matchAgreementMaster(orderDetailData, masterMatchCondition, "0", cooperationBusinessList);
        }
        return agreementMasterList;
    }


    /**
     * 根据协议师傅标签优先级匹配协议师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @param appointType
     * @param cooperationBusinessList
     * @return
     */
    private List<AgreementMasterBase> matchAgreementMaster(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition,String appointType,List<String> cooperationBusinessList){

        List<AgreementMasterBase>  agreementMasterList = matchAgreementMaster(orderDetailData,masterMatchCondition,appointType, AgreementMasterLabel.NEW_CONTRACT.getCode(),cooperationBusinessList);
        if(CollectionUtils.isNotEmpty(agreementMasterList)){
            return agreementMasterList;
        }

        agreementMasterList = matchAgreementMaster(orderDetailData,masterMatchCondition,appointType,AgreementMasterLabel.BRAND.getCode(),cooperationBusinessList);

        if(CollectionUtils.isNotEmpty(agreementMasterList)){
            return agreementMasterList;
        }

        agreementMasterList = matchAgreementMaster(orderDetailData,masterMatchCondition,appointType,AgreementMasterLabel.OTHER.getCode(),cooperationBusinessList);

        return agreementMasterList;

    }


    /**
     * 根据交易模式优先级匹配协议师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @param appointType
     * @param tagName
     * @param cooperationBusinessList
     * @return
     */
    private List<AgreementMasterBase> matchAgreementMaster(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition, String appointType,String tagName,
                                                       List<String> cooperationBusinessList) {


        List<AgreementMasterBase>  agreementMasterList = matchAgreementMaster(orderDetailData,masterMatchCondition,appointType,tagName,Boolean.TRUE,cooperationBusinessList);
        if(CollectionUtils.isNotEmpty(agreementMasterList)){
            return agreementMasterList;
        }

        agreementMasterList = matchAgreementMaster(orderDetailData,masterMatchCondition,appointType,tagName,Boolean.FALSE,cooperationBusinessList);

        if(CollectionUtils.isNotEmpty(agreementMasterList)){
            return agreementMasterList;
        }


        return agreementMasterList;

    }

    /**
     * 根据交易模式，标签，是否指定用户匹配协议师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @param appointType
     * @param tagName
     * @param isAppointUser
     * @param cooperationBusinessList
     * @return
     */
    private List<AgreementMasterBase> matchAgreementMaster(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition, String appointType,String tagName,Boolean isAppointUser,
                                                       List<String> cooperationBusinessList) {

        List<Long> specialCityList = StringUtils.isNotBlank(agreementSpecialCityList) ? Arrays.stream(agreementSpecialCityList.split(",")).map(Long::valueOf).collect(Collectors.toList()) : new ArrayList<>();
        List<AgreementMasterBase> agreementMasterList = null;
        if(masterMatchCondition.getDivisionMatchLevel() == 4 || specialCityList.contains(orderDetailData.getSecondDivisionId())){
            //如果订单有四级地址，则按四级地址匹配协议师傅
            agreementMasterList = matchAgreementMasterByFourthDivisionId(orderDetailData,masterMatchCondition,appointType,tagName,isAppointUser,cooperationBusinessList);
        }else if(masterMatchCondition.getDivisionMatchLevel() == 3){
            //如果订单无四级地址，则匹配合作街道全覆盖订单三级区域的协议师傅
            agreementMasterList = matchAgreementMasterByThirdDivisionIdFullCover(orderDetailData,masterMatchCondition,appointType,tagName,isAppointUser,cooperationBusinessList);
            if(CollectionUtils.isEmpty(agreementMasterList) && StringUtils.isNotBlank(orderDetailData.getOrderLngLat()) && (!(AgreementMasterLabel.NEW_CONTRACT.getCode().equals(tagName)))){
                if("on".equals(agreementMasterOrderMasterDistanceSwitch)){
                    //按订单师傅距离匹配新合约师傅
                    agreementMasterList = matchAgreementMasterByOrderMasterDistance(orderDetailData,masterMatchCondition,appointType,tagName,isAppointUser,cooperationBusinessList);
                }
                if(CollectionUtils.isEmpty(agreementMasterList)){
                    //按协议师傅合作四级地址中心点距离匹配
                    if("on".equals(agreementMasterCenterPointSwitch)){
                        agreementMasterList = this.matchAgreementMasterByCenterPointDistance(orderDetailData,masterMatchCondition,appointType,tagName,isAppointUser,cooperationBusinessList);
                    }
                }
            }
        }
        return agreementMasterList;
    }


    private List<AgreementMasterBase> filterAgreementMaster(List<AgreementMaster> agreementMasterList,OrderDetailData orderDetailData,MasterMatchCondition masterCondition){



        log.info("filter hasPrice agreementMaster result:" + JSON.toJSONString(agreementMasterList));


        if(CollectionUtils.isEmpty(agreementMasterList)){
//            insertAgreementMasterMatch(orderDetailData,"无招募师傅");
            return null;
        }

        List<AgreementMasterMatch> agreementMasterMatchList = new ArrayList<>();
        agreementMasterList.forEach(agreementMaster -> {
            AgreementMasterMatch agreementMasterMatch = new AgreementMasterMatch();
            agreementMasterMatch.setMasterId(agreementMaster.getMasterId());
            agreementMasterMatch.setUserId(orderDetailData.getAccountId());
            agreementMasterMatch.setOrderVersion(orderDetailData.getOrderVersion());
            agreementMasterMatch.setOrderId(orderDetailData.getMasterOrderId());
            agreementMasterMatch.setAppointType(orderDetailData.getAppointType());
            agreementMasterMatch.setOrderNo(orderDetailData.getOrderNo());
            agreementMasterMatch.setOrderCreateTime(orderDetailData.getOrderCreateTime());
            agreementMasterMatch.setRecruitId(agreementMaster.getRecruitId());
            agreementMasterMatch.setRecruitSceneId(agreementMaster.getSceneId());
            agreementMasterMatch.setRecruitTagName(agreementMaster.getTagName());
            agreementMasterMatch.setMatchDivisionLevel((Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0) ? 4 : 3);
            agreementMasterMatch.setAgreementMasterId(agreementMaster.getId());
            agreementMasterMatch.setCreateTime(new Date());
            agreementMasterMatch.setUpdateTime(new Date());
            agreementMasterMatch.setMasterSourceType(agreementMaster.getMasterSourceType());
            agreementMasterMatchList.add(agreementMasterMatch);
        });


        agreementMasterList = filterCooperationBusinessEndMaster(orderDetailData,agreementMasterList,agreementMasterMatchList);

        if(CollectionUtils.isEmpty(agreementMasterList)){
            agreementMasterMatchRepository.insertList(agreementMasterMatchList);
            return null;
        }


        Map<String, AgreementMasterMatch> agreementMasterMatchMap = agreementMasterMatchList.stream()
                .collect(Collectors.toMap(AgreementMasterMatch::getAgreementMasterId, agreementMasterMatch -> agreementMasterMatch));


        if(AppointType.DEFINITE_PRICE.value.equals(orderDetailData.getAppointType())){
            agreementMasterList = filterByDailyOrderCnt(agreementMasterList);
        }



        Set<String> tempAgreementMasterIdSet = CollectionUtils.isNotEmpty(agreementMasterList) ? agreementMasterList.stream().map(AgreementMaster::getId).collect(Collectors.toSet()) : Collections.emptySet();

        for(String agreementMasterId : agreementMasterMatchMap.keySet()){
            agreementMasterMatchMap.get(agreementMasterId).setIsMatchSucc(tempAgreementMasterIdSet.contains(String.valueOf(agreementMasterId)) ? 1 : 0);
            agreementMasterMatchMap.get(agreementMasterId).setMatchFailReason(tempAgreementMasterIdSet.contains(String.valueOf(agreementMasterId)) ? "" : "达到每日接单上限");
        }

        log.info("after filterByPushCountDaily result:" + JSON.toJSONString(agreementMasterList));

        if(CollectionUtils.isEmpty(agreementMasterList)){
            log.info("after filterByPushCountDaily insert agreementMasterMatchList");
            agreementMasterMatchRepository.insertList(agreementMasterMatchList);
            return null;
        }

        Set<String> masterIdSet = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(masterIdSet)) {
            filterPushedMaster(masterIdSet,orderDetailData.getGlobalOrderId(),agreementMasterMatchList);
        }

        masterIdSet = filterStatusAbnormalMaster(masterIdSet,masterCondition,agreementMasterMatchList);

        if (CollectionUtils.isNotEmpty(masterIdSet)) {
            //黑名单
            filterBlacklist(masterIdSet,orderDetailData.getAccountType(),orderDetailData.getAccountId(),agreementMasterMatchList);
        }

        if(CollectionUtils.isEmpty(masterIdSet)){
            log.info("after filterBlacklist insert agreementMasterMatchList");
            agreementMasterMatchRepository.insertList(agreementMasterMatchList);
            return null;
        }

        final String orderPushExcludeMasterIds =
                StringUtils.trimToNull(orderDetailData.getOrderPushExcludeMasterIds());
        outerMasterFilter(masterIdSet,orderPushExcludeMasterIds);
        log.info("debug::agreementMasterMatcher orderPushExcludeMasterIds:{}:{}:{}", orderDetailData.getMasterOrderId(), masterIdSet, orderPushExcludeMasterIds);

        if(CollectionUtils.isEmpty(masterIdSet)){
            agreementMasterMatchRepository.insertList(agreementMasterMatchList);
            return null;
        }

        final Set<String> finalMasterIdSet = masterIdSet;
        agreementMasterList = agreementMasterList.stream().filter(agreementMasterBase -> finalMasterIdSet.contains(String.valueOf(agreementMasterBase.getMasterId()))).collect(Collectors.toList());

        List<AgreementMasterBase> agreementMasterBaseList = getAgreementMasterCooperationPrice(agreementMasterList,orderDetailData,agreementMasterMatchMap);





        if(CollectionUtils.isEmpty(agreementMasterBaseList)){
            agreementMasterMatchRepository.insertList(agreementMasterMatchList);
            return null;
        }


        Map<String, List<AgreementMasterBase>> agreementMasterBaseMap = agreementMasterBaseList.stream().collect(Collectors.groupingBy(item -> item.getMasterId()));
        final List<AgreementMasterBase> tempAgreementMasterBaseList = new ArrayList<>();
        agreementMasterBaseMap.keySet().stream().forEach(masterId ->
                tempAgreementMasterBaseList.add(agreementMasterBaseMap.get(masterId).stream().sorted().findFirst().orElse(null))
        );

        agreementMasterBaseList = tempAgreementMasterBaseList;

        Set<String> masterSet = agreementMasterBaseList.stream().map(AgreementMasterBase::getMasterId).collect(Collectors.toSet());

        PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData,masterSet);

        DefaultContext<String, Object> orderFeatureContext = pushFeature.getOrderFeature();


        //获取策略
        final OrderDistributor orderDistributor = distributeFactory
                    .matchAgreementDistributor(orderDetailData, orderFeatureContext);

        if(orderDistributor.isMatched()){

            try{
                //获取特征
//            featureRepository.orderFeatureReplenish(orderFeatureContext, orderDistributor.getOrderFeatureSet());
                featureRepository.getMasterFeatures(pushFeature,masterSet,orderDistributor.getMasterFeatureSet());

                DefaultContext<String, DefaultContext<String, Object>> masterFeatures = pushFeature.getMasterFeature();


                if(agreementMasterList.get(0).getSceneId() ==  4){
                    //新合约场景判断新合约请假状态，而不判断普通开工状态
                    masterFeatures.keySet().forEach(masterId -> masterFeatures.get(masterId).put("rest_status",1));
                }else{
                    masterFeatures.keySet().forEach(masterId -> masterFeatures.get(masterId).put("new_contract_rest_status",0));
                }


                log.info("masterFeatures:" + JSON.toJSONString(masterFeatures));


                //过滤排序
                final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                        .withOrderId(String.valueOf(orderDetailData.getGlobalOrderId()))
                        .withType(FieldConstant.RANK_DETAIL)
                        .build();


                final List<ScorerMaster> scorerMasterList = orderDistributor
                        .rank(masterSet, orderFeatureContext, masterFeatures, rankDetail);


                log.info(String.format("rankDetail:%s",JSON.toJSONString(rankDetail)));
                Map<Long,String> filterReasonMap = new HashMap<>();

                try{
                    if(Objects.nonNull(rankDetail) && StringUtils.isNotBlank(rankDetail.getDetailInfo())){
                        Map<String,Object> filterDetailsMap = (Map)JSON.parseObject(rankDetail.getDetailInfo()).get("filterDetails");
                        for(String key : filterDetailsMap.keySet()){
                            JSONArray jsonArray = (JSONArray)filterDetailsMap.get(key);
                            jsonArray.forEach(master -> {
                                filterReasonMap.put(Long.valueOf(String.valueOf(master)),key);
                            });
                        }
                    }

                }catch(Exception e){
                    log.error("rankDetail error",e);
                }


                log.info("after rank scorerMasterList:" + JSON.toJSONString(scorerMasterList));


                if(CollectionUtils.isEmpty(scorerMasterList)){
                    agreementMasterMatchList.forEach(agreementMasterMatch -> {
                        if(masterSet.contains(String.valueOf(agreementMasterMatch.getMasterId()))){
                            agreementMasterMatch.setIsFilter(1);
                            agreementMasterMatch.setFilterReason(filterReasonMap.getOrDefault(agreementMasterMatch.getMasterId(),""));
                        }
                    });
                    agreementMasterMatchRepository.insertList(agreementMasterMatchList);
                    return null;
                }

                List<String> masterList = scorerMasterList.stream().map(ScorerMaster::getMasterId).collect(Collectors.toList());
                List<AgreementMasterBase> finalAgreementMasterBaseList = agreementMasterBaseList.stream().filter(agreementMasterBase -> masterList.contains(agreementMasterBase.getMasterId())).collect(Collectors.toList());

                tempAgreementMasterIdSet = CollectionUtils.isNotEmpty(finalAgreementMasterBaseList) ? finalAgreementMasterBaseList.stream().map(AgreementMasterBase::getId).collect(Collectors.toSet()) : Collections.emptySet();

                for(String agreementMasterId : agreementMasterMatchMap.keySet()){
                    agreementMasterMatchMap.get(agreementMasterId).setIsFilter(tempAgreementMasterIdSet.contains(agreementMasterId) ? 0 : 1);
                    agreementMasterMatchMap.get(agreementMasterId).setFilterReason(tempAgreementMasterIdSet.contains(agreementMasterId) ? "" : filterReasonMap.getOrDefault(agreementMasterMatchMap.get(agreementMasterId).getMasterId(),""));
                }

                agreementMasterBaseList= distributeAgreementMaster(scorerMasterList,orderDistributor,agreementMasterBaseList);


                log.info("distributeAgreementMaster:" + JSON.toJSONString(agreementMasterBaseList));


                tempAgreementMasterIdSet = CollectionUtils.isNotEmpty(agreementMasterBaseList) ? agreementMasterBaseList.stream().map(AgreementMasterBase::getId).collect(Collectors.toSet()) : Collections.emptySet();

                for(String agreementMasterId : agreementMasterMatchMap.keySet()){
                    agreementMasterMatchMap.get(agreementMasterId).setIsDistribute(tempAgreementMasterIdSet.contains(agreementMasterId) ? 1 : 0);
                    agreementMasterMatchMap.get(agreementMasterId).setDistributeRule(OrderDistributeRule.asCode(orderDistributor.getDistributeRule()).getDesc());
                }



            }catch(Exception e){
                log.error("agreementMaster distribute error",e);
            }


        }


        if(CollectionUtils.isNotEmpty(agreementMasterBaseList) && (!((nightPush() && DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, nightPushEndTime))))){
            agreementMasterMatchRepository.insertList(agreementMasterMatchList);
        }
        return agreementMasterBaseList;

    }


    private List<AgreementMasterBase> matchAgreementMasterByFourthDivisionId(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition, String appointType,String tagName,Boolean isAppointUser,
                                                                         List<String> cooperationBusinessList) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus", 1));

        Long fourthDivisionId = orderDetailData.getFourthDivisionId();
        if(Objects.nonNull(fourthDivisionId) && fourthDivisionId > 0){
            boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(orderDetailData.getFourthDivisionId())));
        }else{
            boolQueryBuilder.must(QueryBuilders.termQuery("lv4DivisionIds", String.valueOf(orderDetailData.getThirdDivisionId())));
        }

        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));


        boolQueryBuilder.must(QueryBuilders.termQuery("appointType", appointType));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", orderDetailData.getPushExtraData().getMasterSourceType()));

        if (AgreementMasterLabel.OTHER.getCode().equals(tagName)) {
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.EXCLUSIVE_SAMPLE_PLATE.getCode()));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.NEW_CONTRACT.getCode()));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.BRAND.getCode()));
        } else {
            boolQueryBuilder.must(QueryBuilders.termQuery("tagName", tagName));
        }

        if (isAppointUser) {
            boolQueryBuilder.must(QueryBuilders.termQuery("userIds", String.valueOf(orderDetailData.getUserId())));
        } else {
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
        }

        boolQueryBuilder.mustNot(QueryBuilders.termQuery("pricingType", RecruitPricingType.NO_COOPERATION_PRICE.getCode()));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("recruitDistrictMethod", RecruitDistrictMethod.GEO_FENCE.getCode()));



        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
        if (CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())) {
            return null;
        }
        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));

        log.info("matchAgreementMasterByFourthDivisionId searchAgreementMaster request:" + boolQueryBuilder.toString());


        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }

        log.info("matchAgreementMasterByFourthDivisionId agreementMasterList:" + JSON.toJSONString(agreementMasterList));

        return filterAgreementMaster(agreementMasterList,orderDetailData,masterMatchCondition);

    }


    @Resource
    private MasterRepository masterRepository;


    /**
     * 获取地址子集集合
     * @return
     */
    private Set<String> getSubListByDivisionId(Long divisionId){
        final List<Address> subListByDivisionId = addressApi.getSubListByDivisionId(divisionId);
        final Set<String> divisionList =
                subListByDivisionId.stream().map(row -> String.valueOf(row.getDivisionId())).collect(Collectors.toSet());
        return divisionList;
    }


    private List<AgreementMaster> levelThreeDivisionFilter(List<AgreementMaster> agreementMasterList,Long orderThirdDivisionId){
        final Set<String> subDivisionIdList = getSubListByDivisionId(orderThirdDivisionId);
        if (CollectionUtils.isEmpty(subDivisionIdList)) {
          
            return null;
        }

        return agreementMasterList.stream().filter(agreementMaster -> thirdLevelDivisionFullMatch(subDivisionIdList, agreementMaster.getLv4DivisionIds())).collect(Collectors.toList());


    }


    /**
     * 三级地址全匹配
     */
    private boolean thirdLevelDivisionFullMatch(
            Set<String> orderFullFourthDivisions,
            String packageFourthLevelDivisions){
        if (org.apache.commons.lang3.StringUtils.isEmpty(packageFourthLevelDivisions)) {
            return false;
        }
        final Set<String> packageFourthDivisionSet
                = Arrays.stream(packageFourthLevelDivisions.split(SymbolConstant.COMMA)).collect(Collectors.toSet());

        return packageFourthDivisionSet.containsAll(orderFullFourthDivisions);
    }

    private List<AgreementMasterBase> matchAgreementMasterByThirdDivisionIdFullCover(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition, String appointType,String tagName,Boolean isAppointUser,
                                                                         List<String> cooperationBusinessList){
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus", 1));


        boolQueryBuilder.must(QueryBuilders.termQuery("lv3DivisionIds", String.valueOf(masterMatchCondition.getThirdDivisionId())));

        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));


        boolQueryBuilder.must(QueryBuilders.termQuery("appointType", appointType));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", orderDetailData.getPushExtraData().getMasterSourceType()));

        if(AgreementMasterLabel.OTHER.getCode().equals(tagName)){
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.EXCLUSIVE_SAMPLE_PLATE.getCode()));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.NEW_CONTRACT.getCode()));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.BRAND.getCode()));
        }else{
            boolQueryBuilder.must(QueryBuilders.termQuery("tagName", tagName));
        }

        if(isAppointUser){
            boolQueryBuilder.must(QueryBuilders.termQuery("userIds", String.valueOf(orderDetailData.getUserId())));
        }else{
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
        }
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("recruitDistrictMethod", RecruitDistrictMethod.GEO_FENCE.getCode()));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("pricingType", RecruitPricingType.NO_COOPERATION_PRICE.getCode()));


        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
        if (CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())) {
            return null;
        }
            orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));

        log.info("matchAgreementMasterByThirdDivisionIdFullCover searchAgreementMaster request:" + boolQueryBuilder.toString());


        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }


        if(CollectionUtils.isNotEmpty(agreementMasterList)){
            agreementMasterList = levelThreeDivisionFilter(agreementMasterList,orderDetailData.getThirdDivisionId());
        }


        log.info("matchAgreementMasterByThirdDivisionIdFullCover agreementMasterList:" + JSON.toJSONString(agreementMasterList));

        return filterAgreementMaster(agreementMasterList,orderDetailData,masterMatchCondition);



    }



    private List<AgreementMasterBase> matchAgreementMasterByOrderMasterDistance(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition, String appointType,String tagName,Boolean isAppointUser,
                                                                         List<String> cooperationBusinessList){
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus", 1));



        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));


        boolQueryBuilder.must(QueryBuilders.termQuery("appointType", appointType));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", orderDetailData.getPushExtraData().getMasterSourceType()));

        if(AgreementMasterLabel.OTHER.getCode().equals(tagName)){
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.EXCLUSIVE_SAMPLE_PLATE.getCode()));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.NEW_CONTRACT.getCode()));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.BRAND.getCode()));
        }else{
            boolQueryBuilder.must(QueryBuilders.termQuery("tagName", tagName));
        }

        if(isAppointUser){
            boolQueryBuilder.must(QueryBuilders.termQuery("userIds", String.valueOf(orderDetailData.getUserId())));
        }else{
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
        }

        boolQueryBuilder.mustNot(QueryBuilders.termQuery("pricingType", RecruitPricingType.NO_COOPERATION_PRICE.getCode()));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("recruitDistrictMethod", RecruitDistrictMethod.GEO_FENCE.getCode()));



        boolQueryBuilder.must(QueryBuilders.termQuery("masterCityDivisionId", orderDetailData.getSecondDivisionId()));



        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
        if (CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())) {
            return null;
        }
        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));


        //按照距离取有经纬度的师傅
        GeoDistanceQueryBuilder geoDistanceQueryBuilder = new GeoDistanceQueryBuilder("masterLocation");
        //设置中心点
        String[] latLngArray = orderDetailData.getOrderLngLat().split(",");
        geoDistanceQueryBuilder.point(new GeoPoint(Double.valueOf(latLngArray[1]),Double.valueOf(latLngArray[0])));
        //设置条件为到中心点的距离不超过10000米
        geoDistanceQueryBuilder.distance(agreementMasterOrderDistance * 1000, DistanceUnit.METERS);

        boolQueryBuilder.must(geoDistanceQueryBuilder);

        log.info("matchAgreementMasterByOrderMasterDistance searchAgreementMaster request:" + boolQueryBuilder.toString());


        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }


        log.info("matchAgreementMasterByOrderMasterDistance agreementMasterList:" + JSON.toJSONString(agreementMasterList));

        return filterAgreementMaster(agreementMasterList,orderDetailData,masterMatchCondition);

    }


    private List<AgreementMasterBase> matchAgreementMasterByCenterPointDistance(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition, String appointType,String tagName,Boolean isAppointUser,
                                                                            List<String> cooperationBusinessList){
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus", 1));


//        boolQueryBuilder.must(QueryBuilders.termQuery("lv3DivisionIds", String.valueOf(masterMatchCondition.getThirdDivisionId())));

        boolQueryBuilder.must(QueryBuilders.termQuery("masterCityDivisionId", orderDetailData.getSecondDivisionId()));


        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));


        boolQueryBuilder.must(QueryBuilders.termQuery("appointType", appointType));
        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType", orderDetailData.getPushExtraData().getMasterSourceType()));

        if(AgreementMasterLabel.OTHER.getCode().equals(tagName)){
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.EXCLUSIVE_SAMPLE_PLATE.getCode()));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.NEW_CONTRACT.getCode()));
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("tagName", AgreementMasterLabel.BRAND.getCode()));
        }else{
            boolQueryBuilder.must(QueryBuilders.termQuery("tagName", tagName));
        }

        if(isAppointUser){
            boolQueryBuilder.must(QueryBuilders.termQuery("userIds", String.valueOf(orderDetailData.getUserId())));
        }else{
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("userIds"));
        }

        boolQueryBuilder.mustNot(QueryBuilders.termQuery("pricingType", RecruitPricingType.NO_COOPERATION_PRICE.getCode()));
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("recruitDistrictMethod", RecruitDistrictMethod.GEO_FENCE.getCode()));



        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
        if (CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())) {
            return null;
        }
        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));


        log.info("matchAgreementMasterByCenterPointDistance searchAgreementMaster request:" + boolQueryBuilder.toString());


        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }


        if(CollectionUtils.isNotEmpty(agreementMasterList)){

            Map<String,Set<Long>> agreementFourthDivisionMap = new HashMap<>();
            Set<Long> fourthDivisionIdSet = new HashSet<>();
            agreementMasterList.forEach(agreementMaster -> {
                Set<Long> fourthDivisionSet = Arrays.asList(agreementMaster.getLv4DivisionIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toSet());
                fourthDivisionIdSet.addAll(fourthDivisionSet);
                agreementFourthDivisionMap.put(agreementMaster.getId(),fourthDivisionSet);
            });


            Set<Long> distanceFourthDivisionSet = new HashSet<>();
            for(Long fourthDivisionId : fourthDivisionIdSet){
                PositionConvert positionConvert = addressCommon.getPositionConvertByDivisionId(fourthDivisionId);
                if(Objects.nonNull(positionConvert) && Objects.nonNull(positionConvert.getLatitude()) && Objects.nonNull(positionConvert.getLongitude() )&&
                        positionConvert.getLatitude().compareTo(BigDecimal.ZERO) > 0 && positionConvert.getLongitude().compareTo(BigDecimal.ZERO) > 0){
                    String[] lntLatArray = orderDetailData.getOrderLngLat().split(",");
                    Long distance = DistanceUtil.distance(positionConvert.getLatitude().doubleValue(),positionConvert.getLongitude().doubleValue(),new BigDecimal(lntLatArray[1]).doubleValue(),new BigDecimal(lntLatArray[0]).doubleValue());
                    if(distance <= agreementMasterCenterPointDistance){
                        log.info(String.format("matchAgreementMasterByCenterPointDistance divisionId:%d,lat:%d,lng:%d,distance:%d",fourthDivisionId,positionConvert.getLatitude().doubleValue(),
                                positionConvert.getLongitude().doubleValue(),distance));
                        distanceFourthDivisionSet.add(fourthDivisionId);
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(distanceFourthDivisionSet)){
                agreementMasterList = agreementMasterList.stream().filter(agreementMaster -> {
                    Set<Long> fourthDivisionSet = agreementFourthDivisionMap.get(agreementMaster.getId());
                    return !Collections.disjoint(fourthDivisionSet,distanceFourthDivisionSet);
                }).collect(Collectors.toList());

                log.info("matchAgreementMasterByCenterPointDistance agreementMasterList:" + JSON.toJSONString(agreementMasterList));

                return filterAgreementMaster(agreementMasterList,orderDetailData,masterMatchCondition);
            }


        }


        return null;



    }




    /**
     * 过滤不可推单师傅
     * @param masterIdSet
     * @return
     */
    private Set<String> filterStatusAbnormalMaster(Set<String> masterIdSet,MasterMatchCondition masterMatchCondition,List<AgreementMasterMatch> agreementMasterMatchList){
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("isPushRestrictNormal",1));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("freezingTime").gte(0L).lt(masterMatchCondition.freezingRecoverTime()));
        boolQueryBuilder.must(QueryBuilders.termQuery("isSettleStatusNormal", 1));
        boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", masterIdSet));

        List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                masterBaseSearchList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        Set<String> result = new HashSet<>();
        if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
            result = masterBaseSearchList.stream().map(masterBaseSearch -> masterBaseSearch.getMasterId()).collect(Collectors.toSet());
        }

        Collection<String> removeMasterSet = CollectionUtils.subtract(masterIdSet,result);
        agreementMasterMatchList.forEach(agreementMasterMatch -> {
            if(removeMasterSet.contains(String.valueOf(agreementMasterMatch.getMasterId()))){
                agreementMasterMatch.setIsMatchSucc(0);
                agreementMasterMatch.setMatchFailReason("师傅被禁止推单");
            }
        });
        log.info("after filterPushedMaster " ,result);
        return result;
    }

    /**
     * 根据合作价格过滤师傅
     * @param agreementMasterList
     * @return
     */
    private List<AgreementMasterBase> getAgreementMasterCooperationPrice(List<AgreementMaster> agreementMasterList, OrderDetailData orderDetailData,Map<String, AgreementMasterMatch> agreementMasterMatchMap){


        List<AgreementMaster> noCalculatePriceAgreementMasterList = null;
        if(AppointType.DEFINITE_PRICE.value.equals(orderDetailData.getAppointType())){
            noCalculatePriceAgreementMasterList = agreementMasterList.stream().filter(agreementMaster -> "district".equals(agreementMaster.getDirectAppointMethod())).collect(Collectors.toList());
            agreementMasterList = agreementMasterList.stream().filter(agreementMaster -> StringUtils.isBlank(agreementMaster.getDirectAppointMethod()) || "district_price".equals(agreementMaster.getDirectAppointMethod()) || "district_price_allocation".equals(agreementMaster.getDirectAppointMethod())).collect(Collectors.toList());
        }

        List<AgreementMasterBase> agreementMasterBaseList = new ArrayList<>();


        if(CollectionUtils.isNotEmpty(noCalculatePriceAgreementMasterList)){
            noCalculatePriceAgreementMasterList.forEach(agreementMaster -> {
                AgreementMasterBase agreementMasterBase = new AgreementMasterBase();
                agreementMasterBase.setMasterId(String.valueOf(agreementMaster.getMasterId()));
                agreementMasterBase.setRecruitId(String.valueOf(agreementMaster.getRecruitId()));

                String agreementMasterId = agreementMaster.getRecruitId() + ":" + agreementMaster.getMasterId();
                agreementMasterBase.setId(agreementMasterId);
                agreementMasterBase.setDirectAppointMethod(agreementMaster.getDirectAppointMethod());
                agreementMasterBase.setRecruitTagName(agreementMaster.getTagName());
                agreementMasterBase.setMasterSourceType(agreementMaster.getMasterSourceType());
                agreementMasterBaseList.add(agreementMasterBase);
            });
        }

        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterBaseList;
        }

        Long orderId = orderDetailData.getMasterOrderId();
        OrderGrabByIdResp orderGrabByIdResp = appointedModuleResourceApi.getOrderGrabById(orderId);

        //不同招募活动师傅支持按城市区域计价

        //按师傅计价（街道）
        List<AgreementMaster> masterPriceMethodList = agreementMasterList.stream().filter(master -> "master".equals(master.getMasterPriceType())).collect(Collectors.toList());


        if (CollectionUtil.isNotEmpty(masterPriceMethodList)) {
            agreementMasterBaseList.addAll(getAgreementMasterCooperationPriceByPriceType(orderDetailData, agreementMasterMatchMap, orderGrabByIdResp, masterPriceMethodList, "master"));
        }

        //按招募计价
        List<AgreementMaster> recruitPriceMethodList = agreementMasterList.stream().filter(master -> !"master".equals(master.getMasterPriceType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(recruitPriceMethodList)) {
            //统一计价（全国）
            List<AgreementMaster> unitPriceMethodList = recruitPriceMethodList.stream().filter(master -> "unite".equals(master.getPricingType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(unitPriceMethodList)) {
                agreementMasterBaseList.addAll(getAgreementMasterCooperationPriceByPriceType(orderDetailData, agreementMasterMatchMap, orderGrabByIdResp, unitPriceMethodList, "unite"));
            }
            //按城市定价
            List<AgreementMaster> cityPriceMethodList = recruitPriceMethodList.stream().filter(master -> "city".equals(master.getPricingType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(cityPriceMethodList)) {
                agreementMasterBaseList.addAll(getAgreementMasterCooperationPriceByPriceType(orderDetailData, agreementMasterMatchMap, orderGrabByIdResp, cityPriceMethodList, "city"));
            }
            //按区域定价
            List<AgreementMaster> regionalPriceMethodList = recruitPriceMethodList.stream().filter(master -> "regional".equals(master.getPricingType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(regionalPriceMethodList)) {
                agreementMasterBaseList.addAll(getAgreementMasterCooperationPriceByPriceType(orderDetailData, agreementMasterMatchMap, orderGrabByIdResp, regionalPriceMethodList, "regional"));
            }
        }

        if(AppointType.DEFINITE_PRICE.value.equals(orderDetailData.getAppointType())){


            List<AgreementMasterBase> agreementMasterBases = agreementMasterBaseList.stream().filter(agreementMasterBase -> (!"district".equals(agreementMasterBase.getDirectAppointMethod())) && agreementMasterBase.getCooperationPrice().compareTo(orderDetailData.getDefiniteServeFee()) > 0).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(agreementMasterBases)){
                agreementMasterBases.forEach(agreementMasterBase -> {
                    agreementMasterMatchMap.get(agreementMasterBase.getId()).setIsFilter(1);
                    agreementMasterMatchMap.get(agreementMasterBase.getId()).setFilterReason("订单一口价金额小于协议师傅合作价");
                });

            }

            return agreementMasterBaseList.stream().filter(agreementMasterBase -> ("district".equals(agreementMasterBase.getDirectAppointMethod())) || ("district_price_allocation".equals(agreementMasterBase.getDirectAppointMethod())) || agreementMasterBase.getCooperationPrice().compareTo(orderDetailData.getDefiniteServeFee()) <= 0).collect(Collectors.toList());

        }

        return agreementMasterBaseList;
    }

    /**
     * 协议师傅按计价方式计价
     * @param orderDetailData
     * @param agreementMasterMatchMap
     * @param orderGrabByIdResp
     * @param agreementMasterList
     * @param pricingMethod  计价方式：master: 按师傅计价（街道），unite: 统一计价（全国）, city:按城市定价,regional:按区域定价
     * @return
     */
    private List<AgreementMasterBase> getAgreementMasterCooperationPriceByPriceType(OrderDetailData orderDetailData, Map<String, AgreementMasterMatch> agreementMasterMatchMap,
                                                                                    OrderGrabByIdResp orderGrabByIdResp, List<AgreementMaster> agreementMasterList,
                                                                                    String pricingMethod) {

        List<AgreementMasterBase> agreementMasterBaseList = new ArrayList<>();
        Map<String, AgreementMaster> agreementMasterMap = agreementMasterList.stream().collect(Collectors.toMap(AgreementMaster::getId, Function.identity()));

        Long orderId = orderDetailData.getMasterOrderId();
        Integer appointType = orderDetailData.getAppointType();

        List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = orderGrabByIdResp.getOrderServiceAttributeInfos();
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();

        //构建计价参数
        ApplyOrderCalculateBatchReq batchReq = new ApplyOrderCalculateBatchReq();
        ApplyOrderCalculateReq calculateReq = new ApplyOrderCalculateReq();


        com.wanshifu.fee.center.domain.dto.OrderBase orderBaseParam = new com.wanshifu.fee.center.domain.dto.OrderBase();
        orderBaseParam.setOrderId(orderId);
        orderBaseParam.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
        orderBaseParam.setCreateTime(orderBase.getCreateTime());
        calculateReq.setOrderBase(orderBaseParam);
        List<CalculateServiceInfo> serviceInfos = new ArrayList<>();
        orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> serviceInfos.add(JSON.parseObject(orderServiceAttributeInfo.getMasterServiceInfos(), CalculateServiceInfo.class)));
        calculateReq.setServiceInfos(serviceInfos);
        AccountInfo from = new AccountInfo();
        from.setAccountId(orderBase.getAccountId());
        from.setAccountType(orderBase.getAccountType());
        calculateReq.setFrom(from);

        batchReq.setApplyOrderCalculateReq(calculateReq);

        List<Long> specialCityList = StringUtils.isNotBlank(agreementSpecialCityList) ? Arrays.stream(agreementSpecialCityList.split(",")).map(Long::valueOf).collect(Collectors.toList()) : new ArrayList<>();

        List<ApplyOrderCalculateBatchResp> applyOrderCalculateBatchRespList = null;
        if (orderBase.getFourthDivisionId() > 0 || specialCityList.contains(orderDetailData.getSecondDivisionId())) {

            if ("master".equals(pricingMethod)) {
                //按师傅计价（街道）
                List<String> sceneCode = Collections.singletonList("contract_master");
                calculateReq.setSceneCode(sceneCode);

                Long fourthDivisionId = orderBase.getFourthDivisionId();
                AddressInfo addressInfo = new AddressInfo();
                addressInfo.setDivisionType(DivisionTypeEnum.STREET.code);
                Address address = addressApi.getDivisionInfoByDivisionId((Objects.nonNull(fourthDivisionId) && fourthDivisionId > 0) ? fourthDivisionId : orderDetailData.getThirdDivisionId());
                BeanUtils.copyProperties(address, addressInfo);
                calculateReq.setAddressInfo(addressInfo);

                if (AppointType.OPEN.value.equals(appointType)) {
                    calculateReq.setIsMappingPricing(false);
                } else if (AppointType.DEFINITE_PRICE.value.equals(appointType)) {
                    calculateReq.setIsMappingPricing(true);
                    calculateReq.setFromSceneCode("contract_master");
                }
            } else {
                //按招募计价
                List<String> sceneCode = Collections.singletonList("master_recruit_active_price");
                calculateReq.setSceneCode(sceneCode);

                DivisionTypeEnum divisionType = null;
                Long divisionId = 0L;
                if("unite".equals(pricingMethod)){
                    divisionType = DivisionTypeEnum.COUNTRY;
                    divisionId = orderDetailData.getSecondDivisionId();
                }else if("city".equals(pricingMethod)){
                    divisionType = DivisionTypeEnum.CITY;
                    divisionId = orderDetailData.getSecondDivisionId();
                }else if("regional".equals(pricingMethod)){
                    divisionType = DivisionTypeEnum.DISTRICT;
                    divisionId = orderDetailData.getThirdDivisionId();
                }
                if(Objects.isNull(divisionType)){
                    return agreementMasterBaseList;
                }

                AddressInfo addressInfo = new AddressInfo();
                addressInfo.setDivisionType(divisionType.code);
                Address address = addressApi.getDivisionInfoByDivisionId(divisionId);
                BeanUtils.copyProperties(address,addressInfo);
                calculateReq.setAddressInfo(addressInfo);

                if(AppointType.OPEN.value.equals(appointType)){
                    calculateReq.setIsMappingPricing(false);
                }else if(AppointType.DEFINITE_PRICE.value.equals(appointType)){
                    calculateReq.setIsMappingPricing(true);
                    calculateReq.setFromSceneCode("master_recruit_active_price");
                }
            }

            List<BizRuleBatchReq> bizRuleBatchReqList = new ArrayList<>();

            agreementMasterList.forEach(agreementMaster -> {
                BizRuleBatchReq bizRuleBatchReq = new BizRuleBatchReq();
                if ("master".equals(pricingMethod)) {
                    //构建师傅价才传师傅id
                    bizRuleBatchReq.setBizId(String.valueOf(agreementMaster.getMasterId()));
                }
                bizRuleBatchReq.setBizTag(String.valueOf(agreementMaster.getRecruitId()));
                bizRuleBatchReqList.add(bizRuleBatchReq);

            });


            batchReq.setBizRuleBatchReqList(bizRuleBatchReqList);
            log.info("applyOrderCalculateBatch req:" + JSON.toJSONString(batchReq));
            applyOrderCalculateBatchRespList = feeRuleApi.applyOrderCalculateBatch(batchReq);
            log.info("applyOrderCalculateBatch resp:" + JSON.toJSONString(applyOrderCalculateBatchRespList));

        } else {

            List<String> sceneCode = Collections.singletonList("contract_master");
            calculateReq.setSceneCode(sceneCode);
            if (AppointType.OPEN.value.equals(appointType)) {
                calculateReq.setIsMappingPricing(false);
            } else if (AppointType.DEFINITE_PRICE.value.equals(appointType)) {
                calculateReq.setIsMappingPricing(true);
                calculateReq.setFromSceneCode("contract_master");
            }
            Map<Long, List<AgreementMaster>> agreementFourthDivisionMap = new HashMap<>();
            agreementMasterList.forEach(agreementMaster -> {
                Set<Long> fourthDivisionSet = Arrays.asList(agreementMaster.getLv4DivisionIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toSet());
                fourthDivisionSet.forEach(fourthDivisionId -> {
                    if (!agreementFourthDivisionMap.containsKey(fourthDivisionId)) {
                        agreementFourthDivisionMap.put(fourthDivisionId, new ArrayList<>());
                    }
                    agreementFourthDivisionMap.get(fourthDivisionId).add(agreementMaster);
                });
            });


            for (Long fourthDivisionId : agreementFourthDivisionMap.keySet()) {


                AddressInfo addressInfo = new AddressInfo();
                addressInfo.setDivisionType(DivisionTypeEnum.STREET.code);
                Address address = addressApi.getDivisionInfoByDivisionId(fourthDivisionId);
                BeanUtils.copyProperties(address, addressInfo);
                calculateReq.setAddressInfo(addressInfo);

                List<AgreementMaster> agreementMasterList1 = agreementFourthDivisionMap.get(fourthDivisionId);
                List<BizRuleBatchReq> bizRuleBatchReqList = new ArrayList<>();

                agreementMasterList1.forEach(agreementMaster -> {
                    BizRuleBatchReq bizRuleBatchReq = new BizRuleBatchReq();
                    bizRuleBatchReq.setBizId(String.valueOf(agreementMaster.getMasterId()));
                    bizRuleBatchReq.setBizTag(String.valueOf(agreementMaster.getRecruitId()));
                    bizRuleBatchReqList.add(bizRuleBatchReq);

                });


                batchReq.setBizRuleBatchReqList(bizRuleBatchReqList);
                log.info("applyOrderCalculateBatch req:" + JSON.toJSONString(batchReq));
                applyOrderCalculateBatchRespList = feeRuleApi.applyOrderCalculateBatch(batchReq);
                log.info("applyOrderCalculateBatch resp:" + JSON.toJSONString(applyOrderCalculateBatchRespList));
            }


            //根据master_id + recruit_id去重
            applyOrderCalculateBatchRespList = applyOrderCalculateBatchRespList.stream().collect(
                    Collectors.collectingAndThen(Collectors.toCollection(
                            () -> new TreeSet<>(Comparator.comparing(
                                    o -> o.getBizId() + ";" + o.getBizTag() + ";")
                            )), ArrayList::new));


        }

        List<ApplyOrderCalculateBatchResp> feeList = applyOrderCalculateBatchRespList.stream().filter(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getApplyOrderCalculateResp().getSceneResultList().get(0).isSuccess()).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(feeList)) {
            if ("master".equals(pricingMethod)) {
                //按师傅计价
                Map<String, List<ApplyOrderCalculateBatchResp>> applyOrderCalculateBatchRespMap = feeList.stream().collect(Collectors.groupingBy(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getBizId()));
                applyOrderCalculateBatchRespMap.keySet().forEach(masterId -> {
                    AgreementMasterBase agreementMasterBase = new AgreementMasterBase();
                    ApplyOrderCalculateBatchResp resp = applyOrderCalculateBatchRespMap.get(masterId).stream().sorted(new AgreementMasterCooperationPriceComparator()).collect(Collectors.toList()).get(0);
                    agreementMasterBase.setMasterId(resp.getBizId());
                    agreementMasterBase.setRecruitId(resp.getBizTag());
                    agreementMasterBase.setCooperationPrice(this.getCost(resp));

                    String agreementMasterId = agreementMasterBase.getRecruitId() + ":" + agreementMasterBase.getMasterId();
                    agreementMasterBase.setId(agreementMasterId);
                    AgreementMaster agreementMaster = agreementMasterMap.get(agreementMasterId);
                    agreementMasterBase.setDirectAppointMethod(agreementMaster.getDirectAppointMethod());


                    if (AppointType.OPEN.value.equals(orderDetailData.getAppointType())) {
                        Long offerPriceIncreasePercent = agreementMaster.getOfferPriceIncreasePercent();
                        if (Objects.nonNull(offerPriceIncreasePercent) && offerPriceIncreasePercent > 0) {
                            BigDecimal cost = agreementMasterBase.getCooperationPrice();
                            cost = cost.add(cost.multiply(BigDecimal.valueOf(offerPriceIncreasePercent)).divide(BigDecimal.valueOf(100L)));
                            agreementMasterBase.setCooperationPrice(cost);
                        }
                        agreementMasterBase.setCooperationPrice(agreementMasterBase.getCooperationPrice().setScale(0, BigDecimal.ROUND_DOWN));
                    }
                    agreementMasterBase.setRecruitTagName(agreementMasterMap.get(agreementMasterId).getTagName());
                    agreementMasterBaseList.add(agreementMasterBase);

                });
            } else {
                //按招募活动计价
                Map<String, List<ApplyOrderCalculateBatchResp>> applyOrderCalculateBatchRespMap = feeList.stream().collect(Collectors.groupingBy(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getBizTag()));

                agreementMasterList.forEach(master -> {
                    ApplyOrderCalculateBatchResp resp = applyOrderCalculateBatchRespMap.get(master.getRecruitId().toString()).get(0);
                    if (Objects.nonNull(resp)) {
                        AgreementMasterBase agreementMasterBase = new AgreementMasterBase();

                        agreementMasterBase.setMasterId(master.getMasterId().toString());
                        agreementMasterBase.setRecruitId(resp.getBizTag());
                        agreementMasterBase.setCooperationPrice(this.getCost(resp));

                        String agreementMasterId = agreementMasterBase.getRecruitId() + ":" + agreementMasterBase.getMasterId();
                        agreementMasterBase.setId(agreementMasterId);

                        agreementMasterBase.setDirectAppointMethod(master.getDirectAppointMethod());


                        if (AppointType.OPEN.value.equals(orderDetailData.getAppointType())) {
                            Long offerPriceIncreasePercent = master.getOfferPriceIncreasePercent();
                            if (Objects.nonNull(offerPriceIncreasePercent) && offerPriceIncreasePercent > 0) {
                                BigDecimal cost = agreementMasterBase.getCooperationPrice();
                                cost = cost.add(cost.multiply(BigDecimal.valueOf(offerPriceIncreasePercent)).divide(BigDecimal.valueOf(100L)));
                                agreementMasterBase.setCooperationPrice(cost);
                            }
                            agreementMasterBase.setCooperationPrice(agreementMasterBase.getCooperationPrice().setScale(0, BigDecimal.ROUND_DOWN));
                        }
                        agreementMasterBase.setRecruitTagName(master.getTagName());
                        agreementMasterBaseList.add(agreementMasterBase);
                    }

                });
            }

        }


        List<ApplyOrderCalculateBatchResp> failFeeList = applyOrderCalculateBatchRespList.stream().filter(applyOrderCalculateBatchResp -> (!applyOrderCalculateBatchResp.getApplyOrderCalculateResp().getSceneResultList().get(0).isSuccess())).collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(failFeeList)) {
            if ("master".equals(pricingMethod)) {
                //按师傅计价
                failFeeList.forEach(resp -> {
                    String agreementMasterId = resp.getBizTag() + ":" + resp.getBizId();
                    agreementMasterMatchMap.get(agreementMasterId).setIsCalculatePriceSucc(0);
                    String calculatePriceReason = "";
                    if (Objects.nonNull(resp.getApplyOrderCalculateResp()) && CollectionUtils.isNotEmpty(resp.getApplyOrderCalculateResp().getSceneResultList())) {
                        calculatePriceReason = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getErrorInfo();

                    }
                    agreementMasterMatchMap.get(agreementMasterId).setCalculatePriceFailReason(calculatePriceReason);
                });
            } else {
                //按招募活动计价
                Map<String, List<ApplyOrderCalculateBatchResp>> failOrderCalculateBatchRespMap = failFeeList.stream().collect(Collectors.groupingBy(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getBizTag()));

                agreementMasterList.forEach(master -> {
                    ApplyOrderCalculateBatchResp resp = failOrderCalculateBatchRespMap.get(master.getRecruitId().toString()).get(0);
                    if (Objects.nonNull(resp)) {

                        agreementMasterMatchMap.get(master.getId()).setIsCalculatePriceSucc(0);
                        String calculatePriceReason = "";
                        if (Objects.nonNull(resp.getApplyOrderCalculateResp()) && CollectionUtils.isNotEmpty(resp.getApplyOrderCalculateResp().getSceneResultList())) {
                            calculatePriceReason = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getErrorInfo();

                        }
                        agreementMasterMatchMap.get(master.getId()).setCalculatePriceFailReason(calculatePriceReason);
                    }

                });
            }
        }


        if (CollectionUtils.isNotEmpty(agreementMasterBaseList)) {
            agreementMasterBaseList.forEach(agreementMasterBase -> {
                agreementMasterMatchMap.get(agreementMasterBase.getId()).setIsCalculatePriceSucc(1);
                if (Objects.nonNull(agreementMasterBase.getCooperationPrice())) {
                    agreementMasterMatchMap.get(agreementMasterBase.getId()).setCooperationPrice(agreementMasterBase.getCooperationPrice());
                }
            });
        }
        return agreementMasterBaseList;
    }





    private BigDecimal getCost(ApplyOrderCalculateBatchResp resp){
        BigDecimal cost = resp.getApplyOrderCalculateResp().getCost();
        BigDecimal basePrice = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getBasePrice();
        if(Objects.isNull(basePrice)){
            basePrice = BigDecimal.ZERO;
        }
        return cost.compareTo(basePrice) >= 0 ? cost : basePrice;
    }


    /**
     * 用户/总包 拉黑过滤
     */
    public void filterBlacklist(Set<String> masterIdSet,String accountType,Long accountId,List<AgreementMasterMatch> agreementMasterMatchList){
        Set<String> blackMasterList=new HashSet<>();
        try {
            String accountIdString=String.valueOf(accountId);
            if (FieldConstant.ENTERPRISE.equals(accountType)) {
                List<MasterEnterprise> masterEnterpriseList = masterEnterpriseRepository.selectByMasterIdAndEnterpriseId(accountIdString,masterIdSet);
                blackMasterList = masterEnterpriseList.stream().filter(masterUser -> isBlackListEffective(masterUser.getEnterpriseLastBlacklistTime())).map(MasterEnterprise::getMasterId).collect(Collectors.toSet());
            } else if(FieldConstant.USER.equals(accountType)) {
                List<MasterUser> masterUserList = masterUserRepository.selectByMasterIdAndUserId(accountIdString,masterIdSet);
                blackMasterList = masterUserList.stream().filter(masterUser -> isBlackListEffective(masterUser.getUserLastBlacklistTime())).map(MasterUser::getMasterId).collect(Collectors.toSet());
            }
            if (blackMasterList!=null) {

                for(AgreementMasterMatch agreementMasterMatch : agreementMasterMatchList) {
                    if (blackMasterList.contains(String.valueOf(agreementMasterMatch.getMasterId()))) {
                        agreementMasterMatch.setIsMatchSucc(0);
                        agreementMasterMatch.setMatchFailReason("师傅被拉黑");
                    }
                }
                masterIdSet.removeAll(blackMasterList);
            }
        } catch (Exception e) {
            log.error("blackListFilter failed :: accountId:{},e:{}",accountId,e);
        }

        log.info("after filterBlacklist " ,masterIdSet);

    }


    private boolean isBlackListEffective(String time){
        if (com.wanshifu.framework.utils.StringUtils.isBlank(time) || "unspecified".equals(time)) {
            return false;
        }
        return true;
    };




    /**
     * 匹配后续操作
     * @param masterCondition
     * @param matchMasterResult
     */
    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){
        return ;
    }


    private boolean nightPush(){
        if("on".equals(nightPushSwitch)){
            return true;
        }else{
            return false;
        }
    }


    /**
     * 执行推单
     * @param orderDetailData
     * @param matchMasterResult
     * @return
     */
    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){
        if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
            return false;
        }
        String orderVersion = orderDetailData.getOrderVersion();
        Long timeStamp = Long.parseLong(orderVersion);

        pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, matchMasterResult.getMasterIdSet().size(), new Date(timeStamp), PushMode.AGREEMENT_MASTER.code);

        log.info("nightSwitch::debug:{}{}",orderDetailData.getMasterOrderId(),nightPush());
        if (nightPush() && DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, nightPushEndTime)) {
            //夜间推单
            OrderMatchMasterRqt rqt = new OrderMatchMasterRqt();
            rqt.setMasterOrderId(orderDetailData.getMasterOrderId());
            rqt.setHandoffTag(orderDetailData.getPushExtraData().getHandoffTag());
            rqt.setMasterSourceType(orderDetailData.getPushExtraData().getMasterSourceType());
            pushQueueService.sendDelayPushMessage(this.getSecondDayTimestamp() - System.currentTimeMillis(), JSON.toJSONString(rqt));
            return true;
        }

        JSONObject commonFeature = new JSONObject();
        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.AGREEMENT_MASTER.code);
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());
        commonFeature.put("agreement_master_list", matchMasterResult.getExtraData().get("agreement_master_list"));
        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
        pushControllerFacade.agreementMasterPush(orderDetailData, orderDetailData.getOrderVersion(),timeMark, matchMasterResult.getMasterIdSet(), commonFeature);


        List<AgreementMasterMatch> agreementMasterMatchList =    (List<AgreementMasterMatch>)(matchMasterResult.getExtraData().get("agreement_master_match"));

        if(CollectionUtils.isNotEmpty(agreementMasterMatchList)){
            agreementMasterMatchRepository.insertList(agreementMasterMatchList);
        }


        if(!AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())){
            //非总包订单，匹配平台干预推荐师傅
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(orderDetailData.getMasterOrderId());
            orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
            orderDetailData.getPushExtraData().setPushMode("interfere_order_push");
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
        }


        return true;
    }

    public Long getSecondDayTimestamp(){
        Calendar calendar = Calendar.getInstance();
        if (DateFormatterUtil.isBetweenPeriodTime(nightPushStartTime, "23:59")) {
            calendar.add(Calendar.DATE,1);
        }else if (DateFormatterUtil.isBetweenPeriodTime("00:00",nightPushEndTime)) {
        }
        calendar.set(Calendar.HOUR_OF_DAY,8);

        Random random = new Random();
        int minute = random.nextInt(5);
        calendar.set(Calendar.MINUTE,minute);

        return calendar.getTimeInMillis();
    }

    /**
     * 过滤外部师傅
     */
    private void outerMasterFilter(Set<String> result,String masterIds){
        if (masterIds!=null) {
            List<String> outerMasterList= Arrays.asList(masterIds.split(","));
            result.removeAll(outerMasterList);
        }
    }


    @Resource
    private PushHandler pushHandler;

    public List<AgreementMaster> filterCooperationBusinessEndMaster(OrderDetailData orderDetailData,List<AgreementMaster> agreementMasterList,
                                                                    List<AgreementMasterMatch> agreementMasterMatchList){
        Set<String> masterSet = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        Set<String> finalMasterSet = pushHandler.filterByPushLimitRule(orderDetailData,null,masterSet,PushMode.AGREEMENT_MASTER.code,null);
        agreementMasterMatchList.forEach(agreementMasterMatch -> {
            if(finalMasterSet.contains(String.valueOf(agreementMasterMatch.getMasterId()))){
                agreementMasterMatch.setIsMatchSucc(1);
                agreementMasterMatch.setMatchFailReason("");
            }else{
                agreementMasterMatch.setIsMatchSucc(0);
                agreementMasterMatch.setMatchFailReason("关闭合作经营");
            }
        });
        return agreementMasterList.stream().filter(agreementMaster -> finalMasterSet.contains(String.valueOf(agreementMaster.getMasterId()))).collect(Collectors.toList());
    }


}
