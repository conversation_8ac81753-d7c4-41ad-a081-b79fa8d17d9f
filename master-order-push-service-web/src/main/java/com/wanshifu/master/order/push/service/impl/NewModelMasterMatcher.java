package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderDemandInfoByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.po.OrderDemandInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.exclusive.api.ExclusiveMasterOtherApi;
import com.wanshifu.master.exclusive.domains.api.request.other.GetRecruitIdsRqt;
import com.wanshifu.master.exclusive.domains.api.response.other.GetRecruitIdsResp;
import com.wanshifu.master.order.push.api.OrderDistributeStrategyApi;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.master.order.push.util.LocalCollectionsUtil;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.util.FeiShuRobotNotifyUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.geo.GeoDistance;
import org.elasticsearch.common.geo.GeoPoint;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.GeoDistanceQueryBuilder;
import org.elasticsearch.index.query.GeoValidationMethod;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.sort.GeoDistanceSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 品牌师傅匹配器
 * <AUTHOR>
 */
@Slf4j
@Component("new_model_master")
public class NewModelMasterMatcher extends AbstractOrderMasterMatcher{

    @Resource
    private InfoQueryApi infoQueryApi;

    @Resource
    private ExclusiveMasterOtherApi exclusiveMasterOtherApi;


    @Resource
    private OrderServeInfoEsRepository orderServeInfoEsRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushQueueService pushQueueService;


    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;

    @Resource
    private FeatureRepository featureRepository;

    @Resource
    private AgreementMasterEsRespository agreementMasterEsRespository;


    @Resource
    private OrderDistributeStrategyApi orderDistributeStrategyApi;

    @Resource
    private CompensateDistributeRepository compensateDistributeRepository;



    @Value("${new.model.master.onTheWay.distance}")
    private Integer oneTheWayDistance;


    @Value("${new.model.master.nearby.distance}")
    private Integer nearbyDistance;


    @Value("${new.model.master.appoint.orderCnt:7}")
    private Integer appointOrderCnt;


    @Value("${new.model.master.waitDoorIn.orderCnt:7}")
    private Integer waitDoorInOrderCnt;

    /**
     * 飞书群机器人签名
     */
    @Value("${buss_fei_shu_robot_notify_sign}")
    private String bussFeiShuRobotNotifySign;

    /**
     * 飞书通知群签名
     */
    @Value("${buss_fei_shu_robot_notify_url}")
    private String bussFeiShuRobotNotifyUrl;


    /**
     * 飞书通知群签名
     */
    @Value("${new.model.reserve.master.distance:20}")
    private Integer newModelReserveMasterDistance;



    @Resource
    private FeiShuRobotNotifyUtils feiShuRobotNotifyUtils;


    @Resource
    private OrderConfigCommon orderConfigCommon;

    @Resource
    private HBaseClient hBaseClient;

    @Resource
    private NewModelMatchDetailRepository newModelMatchDetailRepository;


    /**
     * 飞书通知群签名
     */
    @Value("${master.feature.query.switch:on}")
    private String masterFeatureQuerySwitch;

    @Resource
    private DistributeFactory distributeFactory;


    /**
     * 检查条件
     * @param orderDetailData
     * @return
     */
    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData){
//        if("normal".equals(orderDetailData.getPushExtraData().getPushMode())){
//            return false;
//        }
        final Integer emergencyOrderFlag = orderDetailData.getEmergencyOrderFlag();
        if (SymbolConstant.ONE.equals(String.valueOf(emergencyOrderFlag))) {
            return false;
        }
        if (orderDetailData.getBusinessLineId() != 2) {
            return false;
        }

        final List<Long> serveIdsArray = orderDetailData.getLv3ServeIdList();
        if (serveIdsArray==null||serveIdsArray.size()==0) {
            return false;
        }
        //总包指派接口
        if (AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())) {
            GetOrderDemandInfoByGlobalIdRqt rqt = new GetOrderDemandInfoByGlobalIdRqt();
            rqt.setGlobalOrderTraceId(rqt.getGlobalOrderTraceId());
            rqt.setEnterpriseId(orderDetailData.getAccountId());
            OrderDemandInfo orderDemandInfo =
                    infoQueryApi.getOrderServeMaster(rqt);
            if (Objects.nonNull(orderDemandInfo) && "master".equals(orderDemandInfo.getToAccountType()) &&
                    Objects.nonNull(orderDemandInfo.getToAccountId()) && orderDemandInfo.getToAccountId() > 0) {
                return false;
            }
        }


        if (!(
                AccountType.USER.code.equals(orderDetailData.getAccountType())
                        || AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())
        )) {
            return false;
        }
        if (orderDetailData.getUserId()==null) {
            return false;
        }

        if (OrderFrom.IKEA.valueEn.equals(orderDetailData.getOrderFrom())) {
            return false;
        }


        return true;
    }





    /**
     * 获取业务范围
     * @param orderDetailData
     * @return
     */
    public List<String> getCooperationBusiness(OrderDetailData orderDetailData) {

        String accountType = orderDetailData.getAccountType();
        if (StrUtil.isEmpty(accountType)) {
            return null;
        }
        if (OrderFrom.IKEA.valueEn.equals(orderDetailData.getAccountType())) {
            return null;
        }
        Integer businessLineId = orderDetailData.getBusinessLineId();
        if(businessLineId != 1 && businessLineId != 2){
            return null;
        }
        if (businessLineId == 1) {
            if(AccountType.USER.code.equals(accountType)){
                return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.FINISHED_PRODUCT.code);
            }else if(AccountType.ENTERPRISE.code.equals(accountType)){
                return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.ENTERPRISE.code);
            }
        }else if (businessLineId == 2) {
            return RecruitBusinessEnum.businessMap.get(RecruitBusinessEnum.FAMILY.code);
        }
        return null;
    }


    /**
     * 匹配样板城市师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private List<AgreementMaster> matchNewModelMasterByDivision(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition){

        List<String> cooperationBusinessList = getCooperationBusiness(orderDetailData);
        if(CollectionUtils.isEmpty(cooperationBusinessList)){
            return null;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 1.合作业务
//        boolQueryBuilder.must(QueryBuilders.termQuery("cooperationBusiness",cooperationBusinessList.get(0)));

        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus",1));
        List<String> tagNameList = new ArrayList<>();
        tagNameList.add("exclusive_sample_plate");
        tagNameList.add("similar_direct_operate");
        boolQueryBuilder.must(QueryBuilders.termsQuery("tagName",tagNameList));


        boolQueryBuilder.must(QueryBuilders.termQuery("lv3DivisionIds", String.valueOf(masterMatchCondition.getThirdDivisionId())));
        Set<Long> masterCategorySet = new HashSet<>();
        masterCategorySet.add(1L);
        masterCategorySet.add(2L);
        boolQueryBuilder.must(QueryBuilders.termsQuery("masterCategory", masterCategorySet));
//        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType",orderDetailData.getPushExtraData().getMasterSourceType()));




        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));

        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
        if(CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())){
            return null;
        }
//        boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", orderDetailData.getLv3ServeIdList()));

        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));


        log.info("search newModelMaster request:" + boolQueryBuilder.toString());

        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        return agreementMasterList;
    }




    private List<AgreementMaster> matchNewModelMaster(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition,Integer masterCategory){

        List<String> cooperationBusinessList = getCooperationBusiness(orderDetailData);
        if(CollectionUtils.isEmpty(cooperationBusinessList)){
            return null;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 1.合作业务
//        boolQueryBuilder.must(QueryBuilders.termQuery("cooperationBusiness",cooperationBusinessList.get(0)));

        boolQueryBuilder.must(QueryBuilders.termsQuery("cooperationBusiness", cooperationBusinessList));
        boolQueryBuilder.must(QueryBuilders.termQuery("agreementMasterStatus",1));
        List<String> tagNameList = new ArrayList<>();
        tagNameList.add("exclusive_sample_plate");
        tagNameList.add("similar_direct_operate");
        boolQueryBuilder.must(QueryBuilders.termsQuery("tagName",tagNameList));


        boolQueryBuilder.must(QueryBuilders.termsQuery("masterCategory", Collections.singleton(masterCategory)));

        if(masterCategory == 1){
            boolQueryBuilder.must(QueryBuilders.termQuery("lv3DivisionIds", String.valueOf(masterMatchCondition.getThirdDivisionId())));
        }


//        boolQueryBuilder.must(QueryBuilders.termQuery("masterSourceType",orderDetailData.getPushExtraData().getMasterSourceType()));




        //3.合作时间
        final Long nowTimeStamp = DateFormatterUtil.getNowTimeStamp();
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationStartTime").lte(nowTimeStamp));
        boolQueryBuilder.must(QueryBuilders.rangeQuery(
                "cooperationEndTime").gte(nowTimeStamp));

        //4.合作服务
        //TODO 二级服务，三级服务如何处理？新旧服务id？
        if(CollectionUtils.isEmpty(orderDetailData.getLv3ServeIdList())){
            return null;
        }
//        boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", orderDetailData.getLv3ServeIdList()));

        orderDetailData.getLv3ServeIdList().forEach(serveId -> boolQueryBuilder.must(QueryBuilders.termsQuery("serveIds", Collections.singletonList(serveId))));


        log.info("search newModelMaster request:" + boolQueryBuilder.toString());

        List<AgreementMaster> agreementMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<AgreementMaster> esResponse = agreementMasterEsRespository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                agreementMasterList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        return agreementMasterList;
    }




    /**
     * 匹配样板城市师傅
     * @param orderDetailData
     * @param masterMatchCondition
     * @return
     */
    private List<AgreementMaster> matchNewModelMasterByDistance(OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition){

        List<AgreementMaster> agreementMasterList = new ArrayList<>();

        //匹配主力师傅
        List<AgreementMaster> mainMasterList  = matchNewModelMaster(orderDetailData,masterMatchCondition,1);
        if(CollectionUtils.isNotEmpty(mainMasterList)){
            agreementMasterList.addAll(mainMasterList);
        }

        //匹配储备师傅
        List<AgreementMaster> reserveMasterList  = matchNewModelMaster(orderDetailData,masterMatchCondition,2);

        if(CollectionUtils.isNotEmpty(reserveMasterList)){
            //按距离筛选储备师傅
            reserveMasterList = filterReserveMasterByDistance(orderDetailData,reserveMasterList);
        }


        if(CollectionUtils.isNotEmpty(reserveMasterList)){
            agreementMasterList.addAll(reserveMasterList);
        }
        return agreementMasterList;
    }


    private List<AgreementMaster> filterReserveMasterByDistance(OrderDetailData orderDetailData,List<AgreementMaster> reserveMasterList){
        Set<Long> masterIdSet = reserveMasterList.stream().map(AgreementMaster::getMasterId).collect(Collectors.toSet());
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termsQuery("masterId",masterIdSet));
        String[] latLngArray = orderDetailData.getOrderLngLat().split(",");
        //按照距离取有经纬度的师傅
        GeoDistanceQueryBuilder geoDistanceQueryBuilder = new GeoDistanceQueryBuilder("latLng");
        //设置中心点
        geoDistanceQueryBuilder.point(new GeoPoint(Double.valueOf(latLngArray[1]), Double.valueOf(latLngArray[0])));
        //设置条件为到中心点的距离不超过10000米
        geoDistanceQueryBuilder.distance(newModelReserveMasterDistance * 1000, DistanceUnit.METERS);
        boolQueryBuilder.must(geoDistanceQueryBuilder);

        List<MasterBaseSearch> masterBaseList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while(true){
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
            if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                masterBaseList.addAll(esResponse.getDataList());
                pageNum++;
            }else{
                break;
            }
        }

        if(CollectionUtils.isNotEmpty(masterBaseList)){
            Set<Long> masterSet = masterBaseList.stream().map(MasterBaseSearch::getMasterId).map(Long::valueOf).collect(Collectors.toSet());
            return reserveMasterList.stream().filter(reserveMaster -> masterSet.contains(reserveMaster.getMasterId())).collect(Collectors.toList());
        }

        return null;
    }


    private DefaultContext<String,Object> initOrderFeatures(OrderDetailData orderDetailData){
        DefaultContext<String, Object> orderFeatures=new DefaultContext<>();
        orderFeatures.put("account_id",orderDetailData.getAccountId());
        orderFeatures.put("business_line_id",orderDetailData.getBusinessLineId());
        orderFeatures.put("category_id",orderDetailData.getOrderCategoryId());
        orderFeatures.put("appoint_type",orderDetailData.getAppointType());


//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());

        //TODO 校验经纬度先后顺序
        orderFeatures.put("order_lng_lat", orderDetailData.getOrderLngLat());
        orderFeatures.put("second_division_id", orderDetailData.getSecondDivisionId());
        orderFeatures.put("third_division_id", orderDetailData.getThirdDivisionId());
        orderFeatures.put("lv1_serve_id",orderDetailData.getLv1ServeIds());
        orderFeatures.put("lv2_serve_ids", orderDetailData.getLv2ServeIds());
        orderFeatures.put("lv3_serve_ids",orderDetailData.getLv3ServeIds());


//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());


        //订单包：如果是总包订单,需要把总包下单用户id传给智能推单 V7.7.2
        orderFeatures.put("user_id",orderDetailData.getUserId());

        orderFeatures.put(FieldConstant.ORDER_FROM,orderDetailData.getOrderFrom());

        orderFeatures.put(FieldConstant.TIME_LINESS_TAG,getTimeLinessTag(orderDetailData));

        orderFeatures.put(FieldConstant.CUSTOMER_PHONE,orderDetailData.getCustomerPhone());
        orderFeatures.put(FieldConstant.BUSINESS_LINE_TYPE, orderDetailData.getBusinessLineId() != null && orderDetailData.getBusinessLineId() == 1 ? MasterSourceType.TOB.code : MasterSourceType.TOC.code);
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE, orderDetailData.getExpectDoorInStartTimeString());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE, orderDetailData.getExpectDoorInEndTimeString());



        return orderFeatures;
    }


    private List<String> getTimeLinessTag(OrderDetailData orderDetailData){

        List<String> timeLinessTagList = new ArrayList<>();


        if(orderDetailData.getTimerFlag() != null && orderDetailData.getTimerFlag() == 1){
            timeLinessTagList.add("regular_time_order");
        }

        if(orderDetailData.getEmergencyOrderFlag() != null && orderDetailData.getEmergencyOrderFlag() == 1){
            timeLinessTagList.add("emergency_order");
        }

        if(orderDetailData.getOnTimeOrderFlag() != null && orderDetailData.getOnTimeOrderFlag() == 1){
            timeLinessTagList.add("on_time_order");
        }

        if(orderDetailData.getExpectDoorInStartDate() != null){
            timeLinessTagList.add("expect_time_order");
        }

        return timeLinessTagList;
    }


    /**
     * 匹配师傅
     * @param orderDetailData
     * @param masterCondition
     * @return
     */
    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition){


        List<AgreementMaster> agreementMasterList = StringUtils.isBlank(orderDetailData.getOrderLngLat()) ? matchNewModelMasterByDivision(orderDetailData,masterCondition) :
                matchNewModelMasterByDistance(orderDetailData,masterCondition);
        log.info("newModelMasterList:" + JSON.toJSONString(agreementMasterList));
        agreementMasterList = filterStatusAbnormalMaster(agreementMasterList);
        log.info("after filterStatusAbnormalMaster newModelMasterList:" + JSON.toJSONString(agreementMasterList));
        if(CollectionUtils.isEmpty(agreementMasterList)){
            feiShuRobotNotifyUtils.sendRichTextNotice(bussFeiShuRobotNotifyUrl,bussFeiShuRobotNotifySign,
                    "推单异常", String.format("【订单编号】：%s\n\n【订单上门地址】：%s\n\n【订单服务】：%s\n\n【预警内容】：%s",
                            orderDetailData.getOrderNo(),orderDetailData.getCustomerAddress(),
                            orderConfigCommon.getOrderServeName(orderDetailData),
                            "该订单无可用主力师傅和储备师傅，需人工跟进"));
            return null;
        }


        Long globalOrderId = orderDetailData.getGlobalOrderId();
        agreementMasterList = filterPushedMasterSet(globalOrderId,agreementMasterList);


        String pushMasterSourceType = orderDetailData.getPushExtraData().getMasterSourceType();
        String masterSourceType = StringUtils.isNotBlank(pushMasterSourceType) ? pushMasterSourceType : MasterSourceType.TOB.code;

        Set<String> mainMasterSet = agreementMasterList.stream().filter(agreementMaster -> masterSourceType.equals(agreementMaster.getMasterSourceType()) && agreementMaster.getMasterCategory() == 1).map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet());
        Set<String> reserveMasterSet = agreementMasterList.stream().filter(agreementMaster -> masterSourceType.equals(agreementMaster.getMasterSourceType()) && agreementMaster.getMasterCategory() == 2).map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet());

        Set<String> tobMainMasterSet = MasterSourceType.TOC.code.equals(masterSourceType) ? agreementMasterList.stream().filter(agreementMaster -> MasterSourceType.TOB.code.equals(agreementMaster.getMasterSourceType()) && agreementMaster.getMasterCategory() == 1).map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet()) : null;

        Set<String> tobReserveMasterSet = MasterSourceType.TOC.code.equals(masterSourceType) ? agreementMasterList.stream().filter(agreementMaster -> MasterSourceType.TOB.code.equals(agreementMaster.getMasterSourceType()) && agreementMaster.getMasterCategory() == 2).map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet()) : null;


        List<NewModelMatchDetail> matchDetailList = new ArrayList<>();

        agreementMasterList.forEach(agreementMaster -> {
            NewModelMatchDetail matchDetail = new NewModelMatchDetail();
            matchDetail.setOrderId(orderDetailData.getMasterOrderId());
            matchDetail.setOrderNo(orderDetailData.getOrderNo());
            matchDetail.setOrderCreateTime(orderDetailData.getOrderCreateTime());
            matchDetail.setMasterId(agreementMaster.getMasterId());
            matchDetail.setIsMainMaster(agreementMaster.getMasterCategory() == 1 ? 1 : 0);
            matchDetailList.add(matchDetail);
        });


//        Map<Long, NewModelMatchDetail> matchDetailMap = matchDetailList.stream()
//                .collect(Collectors.toMap(NewModelMatchDetail::getMasterId, detail -> detail));


        Map<Long,List<NewModelMatchDetail>> newModelMatchDetailMap = matchDetailList.stream().
                collect(Collectors.groupingBy(matchDetail -> matchDetail.getMasterId()));



        log.info("mainMasterSet:" + JSON.toJSONString(mainMasterSet) + ",reserveMasterSet:" + JSON.toJSONString(reserveMasterSet));

        if(CollectionUtils.isEmpty(mainMasterSet) && CollectionUtils.isEmpty(reserveMasterSet)
                && CollectionUtils.isEmpty(tobMainMasterSet)  && CollectionUtils.isEmpty(tobReserveMasterSet)){
            log.info("no newModelMaster to push");
            return null;
        }




        DefaultContext<String, Object> orderFeatureContext = initOrderFeatures(orderDetailData);

        //获取策略
        final OrderDistributor orderDistributor = distributeFactory.matchDistributor(orderDetailData, orderFeatureContext, DistributeType.NEW_MODEL.getCode());


        if(CollectionUtils.isEmpty(mainMasterSet)  && CollectionUtils.isNotEmpty(reserveMasterSet)){
            log.info("push only reserveMaster : " + JSON.toJSONString(reserveMasterSet));
            return newModelMatchResult(mainMasterSet,reserveMasterSet,null,null,orderDistributor,matchDetailList);
        }


        if(CollectionUtils.isEmpty(mainMasterSet)  && (CollectionUtils.isNotEmpty(tobMainMasterSet) || CollectionUtils.isNotEmpty(tobReserveMasterSet))){
            log.info("push only tobNewModelMaster : " + JSON.toJSONString(reserveMasterSet));
            return newModelMatchResult(mainMasterSet,reserveMasterSet,tobMainMasterSet,tobReserveMasterSet,orderDistributor,matchDetailList);
        }


        if(!checkPushMainNewModelPushCnt(globalOrderId)){
            return newModelMatchResult(mainMasterSet,reserveMasterSet,tobMainMasterSet,tobReserveMasterSet,orderDistributor,matchDetailList);
        }



        try {

            Set<String> masterIdSet = filterRestMaster(mainMasterSet,newModelMatchDetailMap);
            masterIdSet = filterCooperationBusinessEndMaster(orderDetailData,masterIdSet,matchDetailList);
            filterByOrderCnt(masterIdSet,orderDetailData,newModelMatchDetailMap);
            log.info("filterByWaitDoorInOrderCnt:" + JSON.toJSONString(masterIdSet));
            if(CollectionUtils.isEmpty(masterIdSet)){
                return newModelMatchResult(mainMasterSet,reserveMasterSet,null,null,orderDistributor,matchDetailList);
            }

            if (!orderDistributor.isMatched()) {
                log.info("no matchDistributor matched!:{}", globalOrderId);
                feiShuRobotNotifyUtils.sendRichTextNotice(bussFeiShuRobotNotifyUrl, bussFeiShuRobotNotifySign, "推单异常", String.format("【订单编号】：%s\n\n【异常描述】：%s", orderDetailData.getOrderNo(), "未匹配筛选策略"));
                return newModelMatchResult(mainMasterSet,reserveMasterSet,null,null,orderDistributor,matchDetailList);
            }

            DefaultContext<String, DefaultContext<String, Object>> masterFeatures;
            if("on".equals(masterFeatureQuerySwitch)){
                //获取特征
                featureRepository.orderFeatureReplenish(orderFeatureContext, orderDistributor.getOrderFeatureSet());
                PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData,mainMasterSet);
                featureRepository.getMasterFeatures(pushFeature,mainMasterSet,orderDistributor.getMasterFeatureSet());
                masterFeatures = pushFeature.getMasterFeature();
            }else{
                masterFeatures = featureRepository.masterFeatures(mainMasterSet, orderDistributor.getMasterFeatureSet(), orderFeatureContext);
            }

            log.info("master_order_id:{}-orderFeatures[{}] - masterFeatures:[{}]", globalOrderId, orderFeatureContext, masterFeatures);


            //过滤排序
            final RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                    .withOrderId(String.valueOf(globalOrderId))
                    .withType(FieldConstant.RANK_DETAIL)
                    .build();

            final List<ScorerMaster> scorerMasterList = orderDistributor
                    .rank(masterIdSet, orderFeatureContext, masterFeatures, rankDetail);

            log.info("rankDetail," + JSON.toJSONString(rankDetail));

            log.info("scorerMasterList," + JSON.toJSONString(scorerMasterList));


            if (CollectionUtils.isEmpty(scorerMasterList)) {
                masterIdSet.forEach(masterId -> {
                    newModelMatchDetailMap.get(Long.valueOf(masterId)).forEach(mathDetail -> mathDetail.setIsFilterOut(1));
                    newModelMatchDetailMap.get(Long.valueOf(masterId)).forEach(mathDetail -> mathDetail.setFilterRemark("筛选条件指标不满足"));
                });
                return newModelMatchResult(mainMasterSet,reserveMasterSet,null,null,orderDistributor,matchDetailList);
            }


            Set<String> availableMainMasterSet = scorerMasterList.stream().map(ScorerMaster::getMasterId).collect(Collectors.toSet());

            masterIdSet.forEach(masterId -> {
                if(!availableMainMasterSet.contains(masterId)){
                    newModelMatchDetailMap.get(Long.valueOf(masterId)).forEach(mathDetail -> mathDetail.setIsFilterOut(1));
                    newModelMatchDetailMap.get(Long.valueOf(masterId)).forEach(mathDetail -> mathDetail.setFilterRemark("筛选条件指标不满足"));
                }
            });

            Long onTheWayMaster = searchOnTheWayMaster(orderDetailData, availableMainMasterSet);
            log.info("searchOnTheWayMaster result: " + onTheWayMaster);

            if (Objects.nonNull(onTheWayMaster)) {
                //第一优先级，顺路主力师傅
                newModelMatchDetailMap.get(Long.valueOf(onTheWayMaster)).forEach(mathDetail -> mathDetail.setIsDistribute(1));
                newModelMatchDetailMap.get(Long.valueOf(onTheWayMaster)).forEach(mathDetail -> mathDetail.setDistributeRemark("顺路派单"));
                return newModelSingleMatchResult(String.valueOf(onTheWayMaster),orderDistributor,matchDetailList);
            }


            String nearbyMasterId = searchNearbyMaster(orderDetailData, availableMainMasterSet);
            log.info("searchNearbyMaster result: " + nearbyMasterId);
            if (StringUtils.isNotBlank(nearbyMasterId)) {
                //第二优先级，附近主力师傅
                newModelMatchDetailMap.get(Long.valueOf(nearbyMasterId)).forEach(mathDetail -> mathDetail.setIsDistribute(1));
                newModelMatchDetailMap.get(Long.valueOf(nearbyMasterId)).forEach(mathDetail -> mathDetail.setDistributeRemark("就近派单"));
                return newModelSingleMatchResult(nearbyMasterId,orderDistributor,matchDetailList);
            }

            String appointOrderMasterId = searchAppointOrderCntMaster(orderDetailData, availableMainMasterSet);
            log.info("searchAppointOrderCntMaster result: " + appointOrderMasterId);
            if (StringUtils.isNotBlank(appointOrderMasterId)) {
                //第三优先级，预约上门时间当天指派单量多主力师傅
                newModelMatchDetailMap.get(Long.valueOf(appointOrderMasterId)).forEach(mathDetail -> mathDetail.setIsDistribute(1));
                newModelMatchDetailMap.get(Long.valueOf(appointOrderMasterId)).forEach(mathDetail -> mathDetail.setDistributeRemark("可做多单者"));
                return newModelSingleMatchResult(appointOrderMasterId,orderDistributor,matchDetailList);
            }

        }catch(Exception e){
            log.error("调度主力师傅失败",e);
        }

        log.info("has no autoGrab mainMaster to push");

        return newModelMatchResult(mainMasterSet,reserveMasterSet,null,null,orderDistributor,matchDetailList);

    }


    public MatchMasterResult newModelMatchResult(Set<String> mainMasterSet,Set<String> reserveMasterSet,Set<String> tobMainMasterSet,Set<String> tobReserveMasterSet,OrderDistributor orderDistributor,
                                                 List<NewModelMatchDetail> matchDetailList){

        List<NewModelMaster> newModelMasterList = new ArrayList<>();
        Set<String> masterSet = new HashSet<>();

        if(CollectionUtils.isNotEmpty(mainMasterSet)){
            masterSet.addAll(mainMasterSet);
            mainMasterSet.forEach(mainMaster -> {
                NewModelMaster newModelMaster = new NewModelMaster(mainMaster,"main_master");
                newModelMasterList.add(newModelMaster);
            });
        }


        if(CollectionUtils.isNotEmpty(reserveMasterSet)){
            masterSet.addAll(reserveMasterSet);
            reserveMasterSet.forEach(reserveMaster -> {
                NewModelMaster newModelMaster = new NewModelMaster(reserveMaster,"reserve_master");
                newModelMasterList.add(newModelMaster);
            });
        }



        List<NewModelMaster> tobNewModelMasterList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(tobMainMasterSet)){
            tobMainMasterSet.forEach(mainMaster -> {
                NewModelMaster newModelMaster = new NewModelMaster(mainMaster,"main_master");
                tobNewModelMasterList.add(newModelMaster);
            });
        }


        if(CollectionUtils.isNotEmpty(tobReserveMasterSet)){
            tobReserveMasterSet.forEach(reserveMaster -> {
                NewModelMaster newModelMaster = new NewModelMaster(reserveMaster,"reserve_master");
                tobNewModelMasterList.add(newModelMaster);
            });
        }

        MatchMasterResult masterResult = new MatchMasterResult(masterSet);
        masterResult.putExtraData("push_mode","new_model");
        masterResult.putExtraData("new_model_master_list",newModelMasterList);
        masterResult.putExtraData("tob_new_model_master_list",tobNewModelMasterList);
        masterResult.putExtraData("compensate_distribute",orderDistributor.getCompensateDistributeList());
        masterResult.putExtraData("new_model_match_detail",matchDetailList);
        log.info("masterResult:" + JSON.toJSONString(masterResult));
        return masterResult;

    }


    public MatchMasterResult newModelSingleMatchResult(String masterId,OrderDistributor orderDistributor,List<NewModelMatchDetail> matchDetailList){

        List<NewModelMaster> newModelMasterList = new ArrayList<>();
        NewModelMaster newModelMaster = new NewModelMaster(masterId,"main_master");
        newModelMasterList.add(newModelMaster);


        MatchMasterResult masterResult = new MatchMasterResult(Collections.singleton(masterId));
        masterResult.putExtraData("push_mode","new_model_single");
        masterResult.putExtraData("new_model_master_list",newModelMasterList);
        masterResult.putExtraData("compensate_distribute",orderDistributor.getCompensateDistributeList());
        masterResult.putExtraData("new_model_match_detail",matchDetailList);
        log.info("masterResult:" + JSON.toJSONString(masterResult));
        return masterResult;

    }


    @Resource
    private PushHandler pushHandler;

    public Set<String> filterCooperationBusinessEndMaster(OrderDetailData orderDetailData,Set<String> masterIdSet,List<NewModelMatchDetail> newModelMatchDetailList){

        Set<String> finalMasterSet = pushHandler.filterByPushLimitRule(orderDetailData,null,masterIdSet, "new_model",null);

        newModelMatchDetailList.forEach(newModelMatchDetail -> {
            if(masterIdSet.contains(String.valueOf(newModelMatchDetail.getMasterId())) &&
                    (!finalMasterSet.contains(String.valueOf(newModelMatchDetail.getMasterId())))){
                newModelMatchDetail.setIsFilterOut(1);
                newModelMatchDetail.setFilterRemark("过滤关闭合作经营师傅");
            }
        });

        return finalMasterSet;
    }


    public Set<String> filterPushedMasterSet(Long getGlobalOrderTraceId,Set<String> masterIdSet,Map<Long,List<NewModelMatchDetail>> matchDetailMap){
        if(CollectionUtils.isEmpty(masterIdSet)){
            return masterIdSet;
        }
        String masterIdStr = hBaseClient.querySingle("order_push",String.valueOf(getGlobalOrderTraceId),"new_model_single");
        if(org.apache.commons.lang3.StringUtils.isNotBlank(masterIdStr)){
            Set<String> pushedMasterIdSet = Arrays.stream(masterIdStr.split(",")).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(pushedMasterIdSet)){
                pushedMasterIdSet.forEach(masterId -> {
                    List<NewModelMatchDetail> matchDetailList = matchDetailMap.get(Long.valueOf(masterId));
                    if(CollectionUtils.isNotEmpty(matchDetailList)){
                        matchDetailList.forEach(matchDetail -> {
                            matchDetail.setIsFilterOut(1);
                            matchDetail.setFilterRemark("已推送主力师傅");
                        });
                    }
                });
            }
            masterIdSet.removeAll(pushedMasterIdSet);
        }
        return masterIdSet;
    }


    public List<AgreementMaster> filterPushedMasterSet(Long getGlobalOrderTraceId,List<AgreementMaster> agreementMasterList){
        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }
        String masterIdStr = hBaseClient.querySingle("order_push",String.valueOf(getGlobalOrderTraceId),"new_model_single");
        if(org.apache.commons.lang3.StringUtils.isNotBlank(masterIdStr)){
            Set<Long> pushedMasterIdSet = Arrays.stream(masterIdStr.split(",")).map(Long::valueOf).collect(Collectors.toSet());
            return agreementMasterList.stream().filter(agreementMaster -> !pushedMasterIdSet.contains(agreementMaster.getMasterId())).collect(Collectors.toList());
        }
        return agreementMasterList;
    }


    public boolean checkPushMainNewModelPushCnt(Long globalOrderTraceId){
        String pushCntStr = hBaseClient.querySingle("order_push",String.valueOf(globalOrderTraceId),"new_model_single_cnt");
        if(org.apache.commons.lang.StringUtils.isNotBlank(pushCntStr)) {
            Integer pushCnt = Integer.valueOf(pushCntStr);
            return pushCnt <= 2;
        }
        return true;
    }


    private Long searchOnTheWayMaster(OrderDetailData orderDetailData,Set<String> availableMainMasterSet){
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", availableMainMasterSet));
        boolQueryBuilder.must(QueryBuilders.termQuery("serveStatus", "serving"));
        boolQueryBuilder.must(QueryBuilders.termQuery("nextServeNode", "serve_sign_position"));
        boolQueryBuilder.must(QueryBuilders.termQuery("reserveDoorInDate", orderDetailData.getExpectDoorInStartDate()));


        //按照距离取有经纬度的师傅
        GeoDistanceQueryBuilder geoDistanceQueryBuilder = new GeoDistanceQueryBuilder("orderLatLng");
        //设置中心点
        String[] latLngArray = orderDetailData.getOrderLngLat().split(",");
        geoDistanceQueryBuilder.point(new GeoPoint(Double.valueOf(latLngArray[1]),Double.valueOf(latLngArray[0])));
        //设置条件为到中心点的距离不超过10000米
        geoDistanceQueryBuilder.distance(oneTheWayDistance * 1000, DistanceUnit.METERS);

        boolQueryBuilder.must(geoDistanceQueryBuilder);


        GeoDistanceSortBuilder distanceSortBuilder = SortBuilders.geoDistanceSort("orderLatLng", Double.valueOf(latLngArray[1]), Double.valueOf(latLngArray[0]));
        distanceSortBuilder.order(SortOrder.ASC);
        distanceSortBuilder.geoDistance(GeoDistance.PLANE);
        distanceSortBuilder.unit(DistanceUnit.METERS);
        distanceSortBuilder.validation(GeoValidationMethod.STRICT);


        EsResponse<OrderServeInfo>  esResponse = orderServeInfoEsRepository.search(boolQueryBuilder,new Pageable(1,10),Collections.singletonList(distanceSortBuilder));
        if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
            return esResponse.getDataList().get(0).getMasterId();
        }
        return null;
    }


    private String searchAppointOrderCntMaster(OrderDetailData orderDetailData,Set<String> availableMainMasterSet){

        if(StringUtils.isBlank(orderDetailData.getExpectDoorInStartDate())){
            return null;
        }


        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(availableMainMasterSet,100);

        List<MasterOrderCnt> masterOrderCntList = new ArrayList<>();

        for(List<String> batch : batchList){
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

            boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", batch));
            boolQueryBuilder.must(QueryBuilders.termsQuery("nextServeNode", "serve_sign_position","reserve_customer","serve_complete"));
            boolQueryBuilder.must(QueryBuilders.termQuery("serveStatus", "serving"));
            boolQueryBuilder.must(QueryBuilders.termQuery("historyMark", 0L));
            boolQueryBuilder.must(QueryBuilders.termQuery("expectedDoorInDate", orderDetailData.getExpectDoorInStartDate()));


            //TODO 分页
            AggregationBuilder aggregationBuilder = AggregationBuilders.terms("appointOrderCnt").field("masterId").size(100);
            log.info("aggregationBuilder:" + aggregationBuilder.toString());
            SearchResponse searchResponse = orderServeInfoEsRepository.search(boolQueryBuilder,aggregationBuilder);

            //取出第一次聚合的terms
            Map<String,Long> resultMap = new HashMap<>();
            //在terms的buckets下，取出stats结果
            Terms terms = searchResponse.getAggregations().get("appointOrderCnt");
            for(Terms.Bucket bucket : terms.getBuckets()){
                resultMap.put(bucket.getKeyAsString(),bucket.getDocCount());
                MasterOrderCnt masterOrderCnt = new MasterOrderCnt();
                masterOrderCnt.setMasterId(bucket.getKeyAsString());
                masterOrderCnt.setOrderCnt(bucket.getDocCount());
                masterOrderCntList.add(masterOrderCnt);
            }
        }


        List<String> masterList = new ArrayList<>(availableMainMasterSet);
        if(CollectionUtils.isNotEmpty(masterOrderCntList)){
            Set<String> hasAppointOrderCntMasterSet = masterOrderCntList.stream().map(MasterOrderCnt::getMasterId).collect(Collectors.toSet());
            masterList.removeAll(hasAppointOrderCntMasterSet);
        }

        if(CollectionUtils.isNotEmpty(masterList)){
            for(String masterId : masterList){
                MasterOrderCnt masterOrderCnt = new MasterOrderCnt();
                masterOrderCnt.setMasterId(masterId);
                masterOrderCnt.setOrderCnt(0L);
                masterOrderCntList.add(masterOrderCnt);
            }
        }

        masterOrderCntList = masterOrderCntList.stream().filter(masterOrderCnt -> masterOrderCnt.getOrderCnt() < appointOrderCnt).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(masterOrderCntList)){
            return null;
        }
        Collections.sort(masterOrderCntList);
        return masterOrderCntList.get(0).getMasterId();
    }


    public List<AgreementMaster> filterStatusAbnormalMaster(List<AgreementMaster> agreementMasterList){

        if(CollectionUtils.isEmpty(agreementMasterList)){
            return agreementMasterList;
        }

        Set<String> masterSet = agreementMasterList.stream().map(AgreementMaster::getMasterId).map(String::valueOf).collect(Collectors.toSet());

        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,100);
        List<AgreementMaster> resultList = new ArrayList<>();

        for(List<String> masterList : batchList){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("masterId",masterList));
            boolQuery.must(QueryBuilders.termQuery("isAccountNormal", 1L));
            boolQuery.must(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
            boolQuery.must(QueryBuilders.termQuery("isRuleExamStatusNormal", 1L));
            boolQuery.must(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
            boolQuery.must(QueryBuilders.termQuery("isPushRestrictNormal", 1L));

            List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQuery,new Pageable(pageNum,pageSize),null);
                log.info("boolQueryBuilder:"+boolQuery.toString());
                if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    masterBaseSearchList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }

            if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
                Set<String> masterIdSet = masterBaseSearchList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());
                resultList.addAll(agreementMasterList.stream().filter(agreementMaster -> masterIdSet.contains(String.valueOf(agreementMaster.getMasterId()))).collect(Collectors.toList()));
            }
        }

        return resultList;
    }

    private Set<String> filterRestMaster(Set<String> masterSet,Map<Long,List<NewModelMatchDetail>> matchDetailMap){

        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,100);
        Set<String> resultSet = new HashSet<>();

        for(List<String> masterList : batchList){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("masterId",masterList));
            boolQuery.must(QueryBuilders.termQuery("restState", 1L));

            List<MasterBaseSearch> masterBaseSearchList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQuery,new Pageable(pageNum,pageSize),null);
                System.out.println("boolQueryBuilder:"+boolQuery.toString());
                if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    masterBaseSearchList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }
            if(CollectionUtils.isNotEmpty(masterBaseSearchList)){
                resultSet.addAll(masterBaseSearchList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet()));
            }

        }

        masterSet.forEach(masterId ->{
            if(CollectionUtils.isEmpty(resultSet) || (!resultSet.contains(masterId))){
                matchDetailMap.get(Long.valueOf(masterId)).forEach(matchDetail -> matchDetail.setIsFilterOut(1));
                matchDetailMap.get(Long.valueOf(masterId)).forEach(matchDetail -> matchDetail.setFilterRemark("休息状态"));
            }
        });


        return resultSet;

    }


    /**
     *
     * @param masterSet
     * @param orderDetailData
     */
    private void filterByOrderCnt(Set<String> masterSet,OrderDetailData orderDetailData,Map<Long,List<NewModelMatchDetail>> matchDetailMap){

        List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,100);

        for(List<String> masterList : batchList){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("masterId",masterList));
            boolQuery.must(QueryBuilders.termsQuery("nextServeNode", "serve_sign_position","reserve_customer","serve_complete"));
            boolQuery.must(QueryBuilders.termQuery("serveStatus", "serving"));
            boolQuery.must(QueryBuilders.termQuery("historyMark", 0L));
            boolQuery.must(QueryBuilders.termQuery("expectedDoorInDate", orderDetailData.getExpectDoorInStartDate()));


            List<OrderServeInfo> orderServeInfoList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                EsResponse<OrderServeInfo> esResponse = orderServeInfoEsRepository.search(boolQuery,new Pageable(pageNum,pageSize),null);
                log.info("filterByWaitDoorInOrderCnt boolQuery :"+boolQuery.toString());
                if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    orderServeInfoList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }
            if(CollectionUtils.isNotEmpty(orderServeInfoList)){
                Map<Long, List<OrderServeInfo>> orderServeInfoMap = orderServeInfoList.stream().collect(Collectors.groupingBy(item -> item.getMasterId()));
                for(Long masterId : orderServeInfoMap.keySet()){
                    if(orderServeInfoMap.get(masterId).size() >= waitDoorInOrderCnt){
                        matchDetailMap.get(masterId).forEach(matchDetail -> matchDetail.setIsFilterOut(1));
                        matchDetailMap.get(masterId).forEach(matchDetail -> matchDetail.setFilterRemark("单量已达上限"));
                        masterSet.remove(String.valueOf(masterId));
                    }
                }
            }
        }

    }


    private String searchNearbyMaster(OrderDetailData orderDetailData,Set<String> availableMainMasterSet){

        if(StringUtils.isBlank(orderDetailData.getOrderLngLat())){
            return null;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", availableMainMasterSet));

        //按照距离取有经纬度的师傅
        GeoDistanceQueryBuilder geoDistanceQueryBuilder = new GeoDistanceQueryBuilder("latLng");
        //设置中心点
        String[] latLngArray = orderDetailData.getOrderLngLat().split(",");
        geoDistanceQueryBuilder.point(new GeoPoint(Double.valueOf(latLngArray[1]),Double.valueOf(latLngArray[0])));
        //设置条件为到中心点的距离不超过10000米
        geoDistanceQueryBuilder.distance(nearbyDistance * 1000, DistanceUnit.METERS);
        boolQueryBuilder.must(geoDistanceQueryBuilder);

        GeoDistanceSortBuilder distanceSortBuilder = SortBuilders.geoDistanceSort("latLng", Double.valueOf(latLngArray[1]), Double.valueOf(latLngArray[0]));
        distanceSortBuilder.order(SortOrder.ASC);
        distanceSortBuilder.geoDistance(GeoDistance.PLANE);
        distanceSortBuilder.unit(DistanceUnit.METERS);
        distanceSortBuilder.validation(GeoValidationMethod.STRICT);

        EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder,new Pageable(1,100),Collections.singletonList(distanceSortBuilder));

        if(Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())){
            return esResponse.getDataList().get(0).getMasterId();
        }

        return null;

    }

    /**
     * 过滤外部师傅
     */
    private void outerMasterFilter(Set<String> result,String masterIds){
        if (masterIds!=null) {
            List<String> outerMasterList= Arrays.asList(masterIds.split(","));
            result.removeAll(outerMasterList);
        }
    }

    private static enum RestrictAction {
        PUSH{
            @Override
            public String getActionString(){
                return "1";
            };
        },LOGIN{
            @Override
            public String getActionString(){
                return "2";
            };
        },EXCLUSIVE{
            @Override
            public String getActionString(){
                return "14";
            };
        },NO_QUOTATION{
            @Override
            public String getActionString(){
                return "3";
            };
        },NO_APPOINT{
            @Override
            public String getActionString(){
                return "4";
            };
        };
        public String getActionString(){
            return null;
        };
    };


    /**
     * 获取招募Id
     * @param thirdDivisionId
     * @param fourthDivisionId
     * @param recruitBusiness
     * @param serveIdArray
     * @param userId
     * @return
     */
    private GetRecruitIdsResp getRecruitIds(
            Long masterOrderId,
            Long thirdDivisionId,
            Long fourthDivisionId,
            String recruitBusiness,
            List<Long> serveIdArray,
            Long userId
    ){

        GetRecruitIdsRqt getRecruitIdsRqt = new GetRecruitIdsRqt();
        getRecruitIdsRqt.setLv3DivisionId(thirdDivisionId);
        getRecruitIdsRqt.setLv4DivisionId(fourthDivisionId);
        getRecruitIdsRqt.setRecruitBusiness(recruitBusiness);
        getRecruitIdsRqt.setServeId(serveIdArray.get(0));
        getRecruitIdsRqt.setUserId(userId);
        getRecruitIdsRqt.setNowTime(new Date());
        final GetRecruitIdsResp getRecruitIdsResp = exclusiveMasterOtherApi.getRecruitIds(getRecruitIdsRqt);
        return getRecruitIdsResp;
    }


    /**
     * 匹配后续操作
     * @param masterCondition
     * @param matchMasterResult
     */
    @Override
    protected void afterPush(OrderDetailData orderDetailData,MasterMatchCondition masterCondition,MatchMasterResult matchMasterResult){
        return ;
    }


    /**
     * 执行推单
     * @param orderDetailData
     * @param matchMasterResult
     * @return
     */
    @Override
    protected boolean executePush(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){

        String orderVersion = orderDetailData.getOrderVersion();
        Long timeStamp = Long.parseLong(orderVersion);

        String pushMode = "";

        String matchSceneCode = orderDetailData.getPushExtraData().getMatchSceneCode();


        List<NewModelMaster> newModelMasterList = Objects.nonNull (matchMasterResult) ? (List<NewModelMaster>)matchMasterResult.getExtraData().get("new_model_master_list") : null;
        List<NewModelMaster> tobNewModelMasterList =  Objects.nonNull (matchMasterResult) ? (List<NewModelMaster>)matchMasterResult.getExtraData().get("tob_new_model_master_list") : null;


        if (CollectionUtils.isNotEmpty(newModelMasterList) || CollectionUtils.isNotEmpty(tobNewModelMasterList) ) {

            pushMode = matchMasterResult.getExtraData().getString(FieldConstant.PUSH_MODE);

            if ("afresh_new_model_single".equals(matchSceneCode)) {
                if(!"new_model_single".equals(pushMode)){
                    return true;
                }else{
                    pushMode = "afresh_new_model_single";
                }

            }


            JSONObject commonFeature = new JSONObject();

            //专属订单
            //	JSONObject commonFeature = new JSONObject();
            commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
            commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, 3);
            commonFeature.put(FieldConstant.PUSH_MODE, pushMode);
            commonFeature.put(FieldConstant.HAND_OFF_TAG, orderDetailData.getPushExtraData().getHandoffTag());
            String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
            commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,orderDetailData.getGlobalOrderId());

            int masterNum = 0;
            if(CollectionUtils.isNotEmpty(newModelMasterList)){
                commonFeature.put("new_model_master_list", newModelMasterList);
                Set<String> pushMasterSet = newModelMasterList.stream().map(NewModelMaster::getMasterId).collect(Collectors.toSet());
                masterNum  = masterNum + pushMasterSet.size();
                pushControllerFacade.newModelPush(orderDetailData, orderDetailData.getOrderVersion(),timeMark, pushMasterSet, commonFeature);
            }


            if(CollectionUtils.isNotEmpty(tobNewModelMasterList)){
                commonFeature.put("new_model_master_list", tobNewModelMasterList);
                Set<String> pushMasterSet = tobNewModelMasterList.stream().map(NewModelMaster::getMasterId).collect(Collectors.toSet());
                masterNum  = masterNum + pushMasterSet.size();
                pushControllerFacade.newModelPush(orderDetailData, orderDetailData.getOrderVersion(),timeMark, pushMasterSet, commonFeature);
            }


            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, masterNum, new Date(timeStamp), pushMode);


            Date operateTime = new Date(timeStamp);
            List<NewModelMatchDetail> matchDetailList = (List<NewModelMatchDetail>)matchMasterResult.getExtraData().get("new_model_match_detail");
            matchDetailList.forEach(matchDetail -> {
                matchDetail.setOperateTime(operateTime);
                matchDetail.setCreateTime(operateTime);
                matchDetail.setUpdateTime(operateTime);
                matchDetail.setOrderVersion(orderVersion);
            });

            newModelMatchDetailRepository.insertList(matchDetailList);

        }else{
            pushProgressRepository.insertBasePushProgress(orderDetailData.getGlobalOrderId(), orderVersion, 0, new Date(timeStamp), "new_model");

            log.info("new_model_transfor_normal");
            OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
            orderMatchMasterRqt.setMasterOrderId(orderDetailData.getMasterOrderId());
            orderMatchMasterRqt.setPushMode(PushMode.NORMAL.code);
            orderDetailData.getPushExtraData().setPushMode("new_model_transfor_normal");
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);

        }





        if("new_model".equals(pushMode)){
            sendCompensateDistributeMessage(orderDetailData,matchMasterResult);
            int pushNum = 0;
            if(CollectionUtils.isNotEmpty(newModelMasterList)){
                pushNum = pushNum + newModelMasterList.size();
            }

            if(CollectionUtils.isNotEmpty(tobNewModelMasterList)){
                pushNum = pushNum + tobNewModelMasterList.size();
            }
            portPushService.matchPushPortRule("new_model",orderDetailData,pushNum);

        }

        if (!"afresh_new_model_single".equals(matchSceneCode)) {
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.NORMAL);
            orderDetailData.getPushExtraData().setMatchSceneCode(MatchSceneCode.NEW_MODEL_RECOMMEND_MASTER_LIST.getCode());
            MasterMatchCondition masterMatchCondition = new MasterMatchCondition(orderDetailData);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);
        }


        return true;
    }


    @Resource
    private PortPushService portPushService;


    private void sendCompensateDistributeMessage(OrderDetailData orderDetailData,MatchMasterResult matchMasterResult){

        List<OrderDistributor.CompensateDistributeDTO> compensateDistributeDTOList = (List<OrderDistributor.CompensateDistributeDTO>)matchMasterResult.getExtraData().get("compensate_distribute");

        if(CollectionUtils.isNotEmpty(compensateDistributeDTOList)){
            compensateDistributeDTOList.forEach(compensateDistributeDTO -> {

                CompensateDistribute compensateDistribute = compensateDistributeRepository.selectByPrimaryKey(compensateDistributeDTO.getCompensateDistributeId());
                CompensateDistributeMessage message = new CompensateDistributeMessage();
                message.setOrderId(orderDetailData.getMasterOrderId());
                message.setCompensateDistribute(compensateDistribute);
                int intervalTime = compensateDistribute.getIntervalTime();
                long delayTime;
                if(intervalTime == 0){
                    delayTime = 5L;
                }else{
                    delayTime = intervalTime * 60 * 1000L;
                }
                message.setPushMode("new_model_transfor_normal");
                message.setOrderRoutingStrategyId(compensateDistributeDTO.getOrderRoutingStrategyId());
                pushQueueService.sendCompensateDistributeMessage(message,delayTime);
            });
        }
    }







    /**
     * 过滤师傅基础信息
     * @param masterIds
     * @param boolQueryBuilder
     * @return
     */
    private Set<String> matchMasterBaseInfoBySet(Set<String> masterIds,BoolQueryBuilder boolQueryBuilder){

        if (Objects.nonNull(boolQueryBuilder)) {
            return masterIds;
        }
        boolQueryBuilder.must(QueryBuilders.termsQuery("masterId", masterIds));
        EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder);
        if(Objects.isNull(esResponse) && CollectionUtils.isEmpty(esResponse.getDataList())){
            return masterIds;
        }
        return esResponse.getDataList().stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());
    }








}
