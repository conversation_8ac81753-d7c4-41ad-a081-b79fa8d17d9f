package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.master.order.push.domain.dto.OrderDistributeResultMessage;
import com.wanshifu.master.order.push.domain.enums.AppointDetailType;
import com.wanshifu.master.order.push.service.OrderDistributeResultNoticeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单调度结果通知
 * <AUTHOR>
 * @description
 * @date 2025/2/25 18:36
 */
@Service
public class OrderDistributeResultNoticeFacade{

    public int distributeResultNotice(OrderDistributeResultMessage orderDistributeResultMessage){
        List<Long> appointDetailTypeList = new ArrayList<>();
        appointDetailTypeList.add(AppointDetailType.AUTO_OFFER_AGREEMENT.getCode());
        appointDetailTypeList.add(AppointDetailType.AUTO_GRAB_AGREEMENT.getCode());
        appointDetailTypeList.add(AppointDetailType.AUTO_GRAB_NEW_MODEL.getCode());
        appointDetailTypeList.add(AppointDetailType.AUTO_GRAB_COOPERATION_BUSINESS.getCode());
        appointDetailTypeList.add(AppointDetailType.AUTO_GRAB_TECHNIQUE_VERIFY.getCode());
        appointDetailTypeList.add(AppointDetailType.AUTO_GRAB_AFTER_TECHNIQUE_VERIFY.getCode());
        appointDetailTypeList.add(AppointDetailType.AUTO_GRAB_FULL_TIME_MASTER.getCode());


        if(!appointDetailTypeList.contains(orderDistributeResultMessage.getAppointDetailType())){
            return 0;
        }
        return OrderDistributeResultNoticeContext.getInstance(orderDistributeResultMessage.getAppointDetailType()).distributeResultNotice(orderDistributeResultMessage);
    }
}
