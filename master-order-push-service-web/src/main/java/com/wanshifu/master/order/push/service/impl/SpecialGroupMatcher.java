package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.BigdataControlServiceApi;
import com.wanshifu.master.order.push.domain.api.response.GetGroupAccountsResp;
import com.wanshifu.master.order.push.domain.api.rqt.GetGroupAccountsRqt;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.repository.SpecialGroupStrategyRepository;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.util.JsonKeyExtractor;
import com.wanshifu.util.LongUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yong
 * @desc 特殊人群推单匹配
 * @date 2025/8/6 17:27
 */
@Slf4j
@Component("special_group")
public class SpecialGroupMatcher extends AbstractOrderMasterMatcher {

    @Resource
    private SpecialGroupStrategyRepository specialGroupStrategyRepository;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;
    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;
    @Resource
    private BigdataControlServiceApi bigdataControlServiceApi;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;
    @Resource
    private RedisHelper redisHelper;

    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData) {
        if (apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())) {
//            insertMatchLog(orderDetailData,"不推单的城市", orderDetailData.getOrderVersion());
            return false;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();
        if (StringUtils.isNotBlank(pushMode) && (!PushMode.SPECIAL_GROUP.code.equals(pushMode))) {
            return Boolean.FALSE;
        }

        List<String> exclusivePushModeList = orderDetailData.getPushExtraData().getExclusivePushModeList();
        if (CollectionUtil.isNotEmpty(exclusivePushModeList) && exclusivePushModeList.contains(PushMode.SPECIAL_GROUP.code)) {
            return Boolean.FALSE;
        }

        Long fourthDivisionId = orderDetailData.getFourthDivisionId();
        Long secondDivisionId = orderDetailData.getSecondDivisionId();
        if (Objects.isNull(fourthDivisionId) || fourthDivisionId <= 0 || Objects.isNull(secondDivisionId) || secondDivisionId <= 0) {
            return Boolean.FALSE;
        }

        Integer appointType = orderDetailData.getAppointType();
        if (appointType == null) {
            return Boolean.FALSE;
        }

        return orderDetailData.getBusinessLineId() == 2;
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition) {
        Long orderId = orderDetailData.getMasterOrderId();
        log.info("special group match orderId:{}", orderId);

        try {
            // 1. 获取匹配的策略
            List<SpecialGroupStrategy> strategies = getMatchingStrategies(orderDetailData);
            if (CollectionUtil.isEmpty(strategies)) {
                log.info("special group match orderId:{} no strategy found", orderId);
                return null;
            }

            log.info("special group match orderId:{} found strategyIds:{}",
                    orderId, strategies.stream().map(SpecialGroupStrategy::getStrategyId).collect(Collectors.toList()));

            // 2. 获取推送和过滤人群ID
            Set<Long> pushGroupIds = extractGroupIds(strategies, SpecialGroupStrategy::getPushGroups);
            Set<Long> filterGroupIds = extractGroupIds(strategies, SpecialGroupStrategy::getFilterGroups);

            // 3. 获取推送师傅ID集合
            Set<Long> pushMasterIds = getMasterIdsByGroups(pushGroupIds, "push");
            if (CollectionUtils.isEmpty(pushMasterIds)) {
                log.info("special group match orderId:{} no push masters found", orderId);
                return null;
            }

            // 4. 获取过滤师傅ID集合并从推送集合中移除
            Set<Long> filterMasterIds = getMasterIdsByGroups(filterGroupIds, "filter");
            pushMasterIds.removeAll(filterMasterIds);

            if (CollectionUtils.isEmpty(pushMasterIds)) {
                log.info("special group match orderId:{} no masters after filtering", orderId);
                return null;
            }

            // 5. 通过ES查询匹配符合条件的师傅
            Set<String> finalMasterIds = matchSpecialGroupMasterIds(orderDetailData, masterCondition, pushMasterIds);
            if (CollectionUtils.isEmpty(finalMasterIds)) {
                log.info("special group match orderId:{} no masters after ES filtering", orderId);
                return null;
            }

            // 6. 缓存延迟时间并构建结果
            return buildMatchResult(strategies, masterCondition, finalMasterIds, orderId);

        } catch (Exception e) {
            log.error("special group match failed for orderId:{}", orderId, e);
            return null;
        }
    }


    @Override
    protected void afterPush(OrderDetailData orderDetailData, MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult) {

    }

    @Override
    public boolean executePush(OrderDetailData orderDetailData, MatchMasterResult matchMasterResult) {
        if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
            return false;
        }
        String orderVersion = orderDetailData.getOrderVersion();
        long timeStamp = Long.parseLong(orderVersion);

        pushProgressRepository.insertBasePushProgress(
                orderDetailData.getGlobalOrderId(),
                orderVersion,
                matchMasterResult.getMasterIdSet().size(),
                new Date(timeStamp),
                PushMode.SPECIAL_GROUP.getCode());

        JSONObject commonFeature = new JSONObject();
        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.SPECIAL_GROUP.getCode());
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
        commonFeature.put("special_group_master_list", matchMasterResult.getExtraData().get("special_group_master_list"));
        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
        pushControllerFacade.specialGroupMasterPush(orderDetailData, orderDetailData.getOrderVersion(), timeMark, matchMasterResult.getMasterIdSet(), commonFeature);

        return true;
    }

    private Set<String> matchSpecialGroupMasterIds(OrderDetailData orderDetailData,
                                                   MasterMatchCondition masterMatchCondition,
                                                   Set<Long> masterIds) {
        Long orderId = orderDetailData.getMasterOrderId();
        log.info("订单id：{}，ES过滤前的师傅数量：{}", orderId, masterIds.size());

        // 检查四级地址
        if (masterMatchCondition.getDivisionMatchLevel() != 4) {
            log.warn("订单id：{}，无四级地址，不匹配特殊人群师傅", orderId);
            return Collections.emptySet();
        }

        try {
            // 构建ES查询条件
            // TODO 这里改为 masterIds 分批查询，searchAllMasters不再循环查，防止masterIds过长导致出错
            BoolQueryBuilder boolQueryBuilder = buildQuery(orderDetailData, masterMatchCondition, masterIds);
            log.info("订单id：{}，ES查询条件：{}", orderId, boolQueryBuilder);

            // 分页查询所有符合条件的师傅
            List<MasterBaseSearch> allMasters = searchAllMasters(boolQueryBuilder, orderId);

            if (CollectionUtil.isEmpty(allMasters)) {
                log.info("订单id：{}，ES过滤后无符合条件的师傅", orderId);
                return Collections.emptySet();
            }

            Set<String> resultMasterIds = allMasters.stream()
                    .map(MasterBaseSearch::getMasterId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            log.info("订单id：{}，ES过滤后的师傅数量：{}，师傅列表：{}",
                    orderId, resultMasterIds.size(), resultMasterIds);

            return resultMasterIds;

        } catch (Exception e) {
            log.error("订单id：{}，ES查询师傅失败", orderId, e);
            return Collections.emptySet();
        }
    }

    /**
     * 分页查询所有符合条件的师傅
     */
    private List<MasterBaseSearch> searchAllMasters(BoolQueryBuilder boolQueryBuilder, Long orderId) {
        List<MasterBaseSearch> allMasters = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        int maxPages = 100; // 防止无限循环

        while (pageNum <= maxPages) {
            try {
                EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(
                        boolQueryBuilder, new Pageable(pageNum, pageSize), null);

                if (esResponse == null || CollectionUtils.isEmpty(esResponse.getDataList())) {
                    log.debug("订单id：{}，第{}页无数据，结束查询", orderId, pageNum);
                    break;
                }

                allMasters.addAll(esResponse.getDataList());
                log.debug("订单id：{}，第{}页查询到{}个师傅", orderId, pageNum, esResponse.getDataList().size());

                // 如果返回的数据少于页大小，说明已经是最后一页
                if (esResponse.getDataList().size() < pageSize) {
                    break;
                }

                pageNum++;

            } catch (Exception e) {
                log.error("订单id：{}，第{}页查询失败", orderId, pageNum, e);
                break;
            }
        }

        if (pageNum > maxPages) {
            log.warn("订单id：{}，达到最大页数限制{}，可能有数据未完全获取", orderId, maxPages);
        }

        log.info("订单id：{}，共查询{}页，获取到{}个师傅", orderId, pageNum - 1, allMasters.size());
        return allMasters;
    }


    private BoolQueryBuilder buildQuery(OrderDetailData orderDetailData,
                                       MasterMatchCondition masterMatchCondition,
                                       Set<Long> masterIds) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        try {
            // 服务区域筛选
            if (masterMatchCondition.getFourthDivisionId() != null) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery("serveFourthDivisionIds",
                        Lists.newArrayList(masterMatchCondition.getFourthDivisionId())));
            }

            // 师傅状态筛选
            addMasterStatusFilters(boolQueryBuilder, masterMatchCondition);

            // 技能筛选
            addTechniqueFilters(boolQueryBuilder, orderDetailData);

            // 师傅ID筛选
            if (CollectionUtils.isNotEmpty(masterIds)) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery("masterId", masterIds));
            }

        } catch (Exception e) {
            log.error("构建ES查询条件失败，订单id：{}", orderDetailData.getMasterOrderId(), e);
            throw new RuntimeException("构建ES查询条件失败", e);
        }

        return boolQueryBuilder;
    }

    /**
     * 添加师傅状态过滤条件
     */
    private void addMasterStatusFilters(BoolQueryBuilder boolQueryBuilder, MasterMatchCondition masterMatchCondition) {
        // 休息状态：1-工作中
        boolQueryBuilder.filter(QueryBuilders.termQuery("restState", 1));

        // 账号状态：1-正常
        boolQueryBuilder.filter(QueryBuilders.termQuery("isAccountNormal", 1L));

        // 结算状态：1-正常
        boolQueryBuilder.filter(QueryBuilders.termQuery("isSettleStatusNormal", 1L));

        // 冻结时间：小于恢复时间
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("freezingTime")
                .gte(0L)
                .lt(masterMatchCondition.freezingRecoverTime()));

        // 黑名单状态：1-正常
        boolQueryBuilder.filter(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));

        // 推单限制状态：1-正常
        boolQueryBuilder.filter(QueryBuilders.termQuery("isPushRestrictNormal", 1L));
    }

    /**
     * 添加技能过滤条件
     */
    private void addTechniqueFilters(BoolQueryBuilder boolQueryBuilder, OrderDetailData orderDetailData) {
        String orderTechniques = orderDetailData.getOrderTechniques();
        if (StringUtils.isNotBlank(orderTechniques)) {
            try {
                Set<String> techniqueIdSet = Arrays.stream(orderTechniques.split("\\|"))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());

                if (!techniqueIdSet.isEmpty()) {
                    boolQueryBuilder.filter(QueryBuilders.termsQuery("masterTechniqueIds", techniqueIdSet));
                }
            } catch (Exception e) {
                log.warn("解析订单技能失败，订单id：{}，技能字符串：{}",
                        orderDetailData.getMasterOrderId(), orderTechniques, e);
            }
        }
    }

    /**
     * 获取匹配的策略
     */
    private List<SpecialGroupStrategy> getMatchingStrategies(OrderDetailData orderDetailData) {
        Integer appointType = orderDetailData.getAppointType();
        List<Long> serveIds = buildServeIdList(orderDetailData);

        return specialGroupStrategyRepository.selectByCityAndServe(
                orderDetailData.getSecondDivisionId().toString(),
                serveIds,
                appointType == null ? null : appointType.toString());
    }

    /**
     * 构建服务ID列表
     */
    private List<Long> buildServeIdList(OrderDetailData orderDetailData) {
        List<Long> serveIds = new ArrayList<>(orderDetailData.getLv3ServeIdList());

        // 添加1级服务ID
        String lv1ServeIds = orderDetailData.getLv1ServeIds();
        if (StringUtils.isNotBlank(lv1ServeIds)) {
            try {
                serveIds.addAll(Arrays.stream(lv1ServeIds.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toList()));
            } catch (NumberFormatException e) {
                log.warn("Invalid lv1ServeIds format: {}", lv1ServeIds, e);
            }
        }

        // 添加2级服务ID
        if (CollectionUtils.isNotEmpty(orderDetailData.getLv2ServeIdList())) {
            serveIds.addAll(orderDetailData.getLv2ServeIdList());
        }

        return serveIds;
    }

    /**
     * 从策略中提取人群ID
     */
    private Set<Long> extractGroupIds(List<SpecialGroupStrategy> strategies,
                                     Function<SpecialGroupStrategy, String> groupExtractor) {
        return strategies.stream()
                .map(groupExtractor)
                .filter(StringUtils::isNotBlank)
                .map(JsonKeyExtractor::extractKeys)
                .flatMap(Collection::stream)
                .map(LongUtil::parseLongSafely)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }



    /**
     * 根据人群ID获取师傅ID集合
     */
    private Set<Long> getMasterIdsByGroups(Set<Long> groupIds, String groupType) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new HashSet<>();
        }

        Set<Long> masterIds = new HashSet<>();
        for (Long groupId : groupIds) {
            try {
                Set<Long> groupMasterIds = getAllMasterIdsByGroup(groupId);
                masterIds.addAll(groupMasterIds);
                log.debug("Retrieved {} masters from {} group {}", groupMasterIds.size(), groupType, groupId);
            } catch (Exception e) {
                log.error("Failed to get master ids for {} group {}", groupType, groupId, e);
            }
        }

        log.info("Total {} masters retrieved from {} groups: {}", masterIds.size(), groupType, masterIds.size());
        return masterIds;
    }

    /**
     * 获取指定人群的所有师傅ID（处理分页）
     */
    private Set<Long> getAllMasterIdsByGroup(Long groupId) {
        Set<Long> allMasterIds = new HashSet<>();
        GetGroupAccountsRqt rqt = createGroupAccountsRequest(groupId);

        int maxPages = 50; // 防止无限循环
        int currentPage = 0;

        while (currentPage < maxPages) {
            try {
                GetGroupAccountsResp response = bigdataControlServiceApi.getGroupAccounts(rqt);
                if (response == null) {
                    log.warn("Null response for group {}", groupId);
                    break;
                }

                if (CollectionUtils.isNotEmpty(response.getAccountIds())) {
                    allMasterIds.addAll(response.getAccountIds());
                }

                // 检查是否还有下一页
                if (response.getHasNext() == null || response.getHasNext() != 1) {
                    break;
                }

                currentPage++;

            } catch (Exception e) {
                log.error("Error fetching masters for group {} at page {}", groupId, currentPage, e);
                break;
            }
        }

        if (currentPage >= maxPages) {
            log.warn("Reached max pages limit for group {}, may have incomplete data", groupId);
        }

        return allMasterIds;
    }

    /**
     * 创建获取人群账号的请求
     */
    private GetGroupAccountsRqt createGroupAccountsRequest(Long groupId) {
        GetGroupAccountsRqt rqt = new GetGroupAccountsRqt();
        rqt.setPersonaId(2);
        rqt.setGroupId(groupId);
        rqt.setPageSize(2000);
        rqt.setJobId(UUID.randomUUID().toString());
        return rqt;
    }

    /**
     * 构建匹配结果
     */
    private MatchMasterResult buildMatchResult(List<SpecialGroupStrategy> strategies,
                                             MasterMatchCondition masterCondition,
                                             Set<String> masterIds,
                                             Long orderId) {
        try {
            // 获取最小延迟时间
            Integer delayMinutes = strategies.stream()
                    .map(SpecialGroupStrategy::getDelayMinutes)
                    .filter(Objects::nonNull)
                    .min(Integer::compareTo)
                    .orElse(0);

            // 缓存延迟时间
            String redisKey = ApolloConfigUtils.APPLICATION_NAME + ":special_group_" + orderId;
            redisHelper.set(redisKey, delayMinutes.toString(), 24 * 60 * 60);

            // 构建结果
            MatchMasterResult masterResult = new MatchMasterResult();
            masterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL, masterCondition.getDivisionMatchLevel());
            masterResult.putExtraData("special_group_master_list", masterIds);
            masterResult.setMasterIdSet(masterIds);

            log.info("special group match orderId:{} success, found {} masters with delay {} minutes",
                    orderId, masterIds.size(), delayMinutes);

            return masterResult;

        } catch (Exception e) {
            log.error("Failed to build match result for orderId:{}", orderId, e);
            return null;
        }
    }


}
