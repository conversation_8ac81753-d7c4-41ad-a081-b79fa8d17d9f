package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.api.BigdataControlServiceApi;
import com.wanshifu.master.order.push.domain.api.response.GetGroupAccountsResp;
import com.wanshifu.master.order.push.domain.api.rqt.GetGroupAccountsRqt;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.MatchMasterResult;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.enums.PushMode;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.MasterBaseEsRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.repository.SpecialGroupStrategyRepository;
import com.wanshifu.master.order.push.service.PushControllerFacade;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.util.JsonKeyExtractor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yong
 * @desc 特殊人群推单匹配
 * @date 2025/8/6 17:27
 */
@Slf4j
@Component("special_group")
public class SpecialGroupMatcher extends AbstractOrderMasterMatcher {

    @Resource
    private SpecialGroupStrategyRepository specialGroupStrategyRepository;

    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private PushControllerFacade pushControllerFacade;
    @Resource
    private MasterBaseEsRepository masterBaseEsRepository;
    @Resource
    private BigdataControlServiceApi bigdataControlServiceApi;

    @Resource
    private ApolloConfigUtils apolloConfigUtils;
    @Resource
    private RedisHelper redisHelper;

    @Override
    protected boolean checkPreCondition(OrderDetailData orderDetailData) {
        if (apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())) {
//            insertMatchLog(orderDetailData,"不推单的城市", orderDetailData.getOrderVersion());
            return false;
        }

        String pushMode = orderDetailData.getPushExtraData().getPushMode();
        if (StringUtils.isNotBlank(pushMode) && (!PushMode.SPECIAL_GROUP.code.equals(pushMode))) {
            return Boolean.FALSE;
        }

        Long fourthDivisionId = orderDetailData.getFourthDivisionId();
        Long secondDivisionId = orderDetailData.getSecondDivisionId();
        if (Objects.isNull(fourthDivisionId) || fourthDivisionId <= 0 || Objects.isNull(secondDivisionId) || secondDivisionId <= 0) {
            return Boolean.FALSE;
        }

        Integer appointType = orderDetailData.getAppointType();
        if (appointType == null) {
            return Boolean.FALSE;
        }

        return orderDetailData.getBusinessLineId() == 2;
    }

    @Override
    public MatchMasterResult match(OrderDetailData orderDetailData, MasterMatchCondition masterCondition) {

        Long orderId = orderDetailData.getMasterOrderId();
        Integer appointType = orderDetailData.getAppointType();
        log.info("special group match orderId:{}", orderId.toString());
        List<Long> serveIds = new ArrayList<>(orderDetailData.getLv3ServeIdList());
        // 数据库中serve_ids字段存的值包含1/2/3级服务id，所以需要聚合入参
        String lv1ServeIds = orderDetailData.getLv1ServeIds();
        if (StringUtils.isNotBlank(lv1ServeIds)) {
            serveIds.addAll(Arrays.stream(lv1ServeIds.split(",")).map(Long::valueOf).collect(Collectors.toList()));
        }
        serveIds.addAll(orderDetailData.getLv2ServeIdList());
        List<SpecialGroupStrategy> strategies = specialGroupStrategyRepository.selectByCityAndServe(
                orderDetailData.getSecondDivisionId().toString(),
                serveIds,
                appointType == null ? null : appointType.toString());

        if (CollectionUtil.isEmpty(strategies)) {
            log.info("special group match orderId:{} no strategy", orderId);
            return null;
        }

        log.info("special group match orderId:{} strategies:{}", orderId, JSON.toJSONString(strategies));

        Set<Long> pushGroupIds = strategies.stream()
                .map(SpecialGroupStrategy::getPushGroups)
                .map(JsonKeyExtractor::extractKeys)
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .collect(Collectors.toSet());

        Set<Long> filterGroupIds = strategies.stream()
                .map(SpecialGroupStrategy::getFilterGroups)
                .map(JsonKeyExtractor::extractKeys)
                .flatMap(Collection::stream)
                .map(Long::valueOf)
                .collect(Collectors.toSet());

        final Set<Long> pushMasterIds = new HashSet<>();
        for (Long pushGroupId : pushGroupIds) {
            GetGroupAccountsRqt rqt = new GetGroupAccountsRqt();
            rqt.setPersonaId(2);
            rqt.setGroupId(pushGroupId);
            rqt.setPageSize(2000);
            rqt.setJobId(UUID.randomUUID().toString());
            getGroupMasterIds(rqt, pushMasterIds);
        }
        if (CollectionUtils.isEmpty(pushMasterIds)) {
            return null;
        }

        final Set<Long> filterMasterIds = new HashSet<>();
        for (Long filterGroupId : filterGroupIds) {
            GetGroupAccountsRqt rqt = new GetGroupAccountsRqt();
            rqt.setPersonaId(2);
            rqt.setGroupId(filterGroupId);
            rqt.setPageSize(2000);
            rqt.setJobId(UUID.randomUUID().toString());
            getGroupMasterIds(rqt, filterMasterIds);
        }

        pushMasterIds.removeAll(filterMasterIds);
        if (CollectionUtils.isEmpty(pushMasterIds)) {
            return null;
        }

        Set<String> masterIds = matchSpecialGroupMasterIds(orderDetailData, masterCondition, pushMasterIds);
        if (CollectionUtils.isEmpty(masterIds)) {
            return null;
        }

        Integer delayMinutes = strategies.stream().map(SpecialGroupStrategy::getDelayMinutes).min(Comparator.comparing(Integer::intValue)).orElse(0);
        redisHelper.set(ApolloConfigUtils.APPLICATION_NAME + ":special_group_" + orderId, delayMinutes.toString(), 24 * 60 * 60);

        MatchMasterResult masterResult = new MatchMasterResult();
        masterResult.putExtraData(FieldConstant.DIVISION_MATCH_LEVEL, masterCondition.getDivisionMatchLevel());
        masterResult.putExtraData("special_group_master_list", masterIds);
        masterResult.setMasterIdSet(masterIds);
        return masterResult;
    }


    @Override
    protected void afterPush(OrderDetailData orderDetailData, MasterMatchCondition masterCondition, MatchMasterResult matchMasterResult) {

    }

    @Override
    public boolean executePush(OrderDetailData orderDetailData, MatchMasterResult matchMasterResult) {
        if (matchMasterResult == null || CollectionUtils.isEmpty(matchMasterResult.getMasterIdSet())) {
            return false;
        }
        String orderVersion = orderDetailData.getOrderVersion();
        long timeStamp = Long.parseLong(orderVersion);

        pushProgressRepository.insertBasePushProgress(
                orderDetailData.getGlobalOrderId(),
                orderVersion,
                matchMasterResult.getMasterIdSet().size(),
                new Date(timeStamp),
                PushMode.SPECIAL_GROUP.getCode());

        JSONObject commonFeature = new JSONObject();
        commonFeature.put(FieldConstant.BUSINESS_LINE_ID, String.valueOf(orderDetailData.getBusinessLineId()));
        commonFeature.put(FieldConstant.DIVISION_MATCH_LEVEL, matchMasterResult.getExtraData().getInteger(FieldConstant.DIVISION_MATCH_LEVEL));
        commonFeature.put(FieldConstant.PUSH_MODE, PushMode.SPECIAL_GROUP.getCode());
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, orderDetailData.getGlobalOrderId());
        commonFeature.put("special_group_master_list", matchMasterResult.getExtraData().get("special_group_master_list"));
        String timeMark = DateFormatterUtil.timeStampToTime(timeStamp);
        pushControllerFacade.specialGroupMasterPush(orderDetailData, orderDetailData.getOrderVersion(), timeMark, matchMasterResult.getMasterIdSet(), commonFeature);

        return true;
    }

    private Set<String> matchSpecialGroupMasterIds(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition, Set<Long> masterIds) {

        log.info("订单id：{}，状态等过滤之前的师傅：{}", orderDetailData.getMasterOrderId(), masterIds);

        if (masterMatchCondition.getDivisionMatchLevel() != 4) {
            //订单无四级地址，不匹配
//            String matchFailReason = "订单无四级地址，不匹配技能验证后师傅！";
//            this.insertMatchLog(orderDetailData, matchFailReason, orderDetailData.getOrderVersion());
            return null;
        }

        BoolQueryBuilder boolQueryBuilder = buildQuery(orderDetailData, masterMatchCondition, masterIds);

        log.info("specialGroupMasterList match esQuery:" + boolQueryBuilder);

        List<MasterBaseSearch> specialGroupMasterList = new ArrayList<>();
        int pageNum = 1;
        int pageSize = 200;
        while (true) {
            EsResponse<MasterBaseSearch> esResponse = masterBaseEsRepository.search(boolQueryBuilder, new Pageable(pageNum, pageSize), null);
            if (Objects.nonNull(esResponse) && CollectionUtils.isNotEmpty(esResponse.getDataList())) {
                specialGroupMasterList.addAll(esResponse.getDataList());
                pageNum++;
            } else {
                break;
            }
        }



        if (CollectionUtil.isEmpty(specialGroupMasterList)) {
            log.info("订单id：{}，状态等过滤之前的师傅：{}", orderDetailData.getMasterOrderId(), Collections.emptyList());
            return Collections.emptySet();
        }

        log.info("订单id：{}，状态等过滤之前的师傅：{}", orderDetailData.getMasterOrderId(), specialGroupMasterList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toList()));

        return specialGroupMasterList.stream().map(MasterBaseSearch::getMasterId).collect(Collectors.toSet());
    }


    private BoolQueryBuilder buildQuery(OrderDetailData orderDetailData, MasterMatchCondition masterMatchCondition, Set<Long> masterIds) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        //服务区域筛选
        boolQueryBuilder.filter(QueryBuilders.termsQuery("serveFourthDivisionIds", Lists.newArrayList(masterMatchCondition.getFourthDivisionId())));

        boolQueryBuilder.filter(QueryBuilders.termQuery("restState", 1));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isAccountNormal", 1L));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isSettleStatusNormal", 1L));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("freezingTime").gte(0L).lt(masterMatchCondition.freezingRecoverTime()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isBlackListStatusNormal", 1L));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isPushRestrictNormal", 1L));

        //技能筛选
        Set<String> techniqueIdSet = Arrays.stream(orderDetailData.getOrderTechniques().split("\\|")).collect(Collectors.toSet());
        boolQueryBuilder.filter(QueryBuilders.termsQuery("masterTechniqueIds", techniqueIdSet));


        boolQueryBuilder.filter(QueryBuilders.termsQuery("masterId", masterIds));

        return boolQueryBuilder;
    }

    private void getGroupMasterIds(GetGroupAccountsRqt rqt, Set<Long> pushMasterIds) {
        GetGroupAccountsResp groupAccounts = bigdataControlServiceApi.getGroupAccounts(rqt);
        if (groupAccounts != null) {
            pushMasterIds.addAll(groupAccounts.getAccountIds());
            Integer hasNext = groupAccounts.getHasNext();
            if (hasNext == 1) {
                getGroupMasterIds(rqt, pushMasterIds);
            }
        }
    }


}
