package com.wanshifu.master.order.push.service;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.*;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.DynamicRoundsMaster;
import com.wanshifu.master.order.push.domain.dto.DynamicRoundsMasterList;
import com.wanshifu.master.order.push.domain.dto.PushRuleConfig;
import com.wanshifu.master.order.push.domain.dto.WheelRoundsPushMessage;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.po.PushConfig;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.DelayPushRqt;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.MasterPushRepository;
import com.wanshifu.master.order.push.repository.OrderDynamicRoundsPushRepository;
import com.wanshifu.master.order.push.repository.PushProgressRepository;
import com.wanshifu.master.order.push.repository.PushRecordFacade;
import com.wanshifu.master.order.push.util.DateFormatterUtil;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 首轮推送服务
 * 
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class PushControllerService {

	@Resource
	private PushQueueService pushQueueService;

	@Resource
	private MasterPushRepository masterPushRepository;

	@Resource
	private PushProgressRepository pushProgressRepository;

	@Resource
	private PushController pushController;


	@Value("${pushByRound.enable}")
	private Boolean pushByRoundEnable;


	@Value("${delay.night.push.time.start}")
	private String delayNightPushTimeStart;


	@Value("${delay.night.push.time.end}")
	private String delayNightPushTimeEnd;

	@Resource
	private PushRecordFacade pushRecordFacade;

	@Resource
	private DynamicRoundsPushService dynamicRoundsPushService;


	@Resource
	private OrderDynamicRoundsPushRepository orderDynamicRoundsPushRepository;


	@Value("${auto.punishment.push.restrict.switch:on}")
	private String autoPunishmentPushRestrictSwitch;

    public void exclusivePush(String orderVersion, String timeMark, PushParameter pushParameter,
                              List<PushMaster> listToBeHandle, JSONObject commonFeature, String masterSourceType,OrderDetailData orderDetailData) {
        push(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature, false, masterSourceType,orderDetailData);
    }



	/**
	 * 是否还有剩余可推师傅
	 */
	public boolean isHaveMoreMaster(PushProgress pushProgress,Long orderId,String orderVersion) {
		int pushedNum = pushProgress.getFilteredMasterNum() + pushProgress.getPushedMasterNum();
		// 总长度小于已推数量
		boolean isHaveMoreMaster=pushProgress.getListLength() > pushedNum;
		if (!isHaveMoreMaster) {
			//已经推完了
			pushProgressRepository.updatePushOverMessage(orderId,orderVersion, PushStatus.STOP.code, "PUSHED ALL MASTER");
		}
		return isHaveMoreMaster;
	}

    /**
     * 直接推送
     *
     * @param orderVersion
     * @param timeMark
     * @param pushParameter
     * @param listToBeHandle
     * @param commonFeature
     */
    public void directPush(String orderVersion, String timeMark, PushParameter pushParameter,
                           List<PushMaster> listToBeHandle, JSONObject commonFeature, boolean longTailPushFlag,
                           String masterSourceType,OrderDetailData orderDetailData) {
        push(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature, longTailPushFlag, masterSourceType,orderDetailData);

		String pushMode = (String)commonFeature.get(FieldConstant.PUSH_MODE);
		Set<String> masterSet = listToBeHandle.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());
		if(PushMode.FULL_TIME_MASTER_DISPATCH.code.equals(pushMode) || PushMode.TECHNIQUE_VERIFY_MASTER_DISPATCH.code.equals(pushMode)){
			pushRecordFacade.orderPush(pushParameter.getGlobalOrderId(), masterSet, pushMode);

		}
    }

    public void push(String orderVersion, String timeMark, PushParameter pushParameter,
                     List<PushMaster> listToBeHandle, JSONObject commonFeature, boolean longTailPushFlag,
                     String masterSourceType,OrderDetailData orderDetailData) {
        // 未开启直接推送
        pushController.directPush(orderVersion, pushParameter.getMasterOrderId(),
                listToBeHandle, commonFeature, longTailPushFlag, "1", masterSourceType,orderDetailData);
        PushProgress pushProgress = new PushProgress();
        // 新师傅列表offset
        pushProgress.setNewMasterOffset(0);
        // 老师傅列表offset
        pushProgress.setOldMasterOffset(0);
        // 师傅列表总长度
        pushProgress.setListLength(listToBeHandle.size());
        // 已推师傅数
        pushProgress.setPushedMasterNum(listToBeHandle.size());
        // 已过滤师傅数
        pushProgress.setFilteredMasterNum(0);
        // 更新推送进度
        pushProgress.setPushedRound(1);
        pushProgress.setPushStatus(PushStatus.STOP.code);
        pushProgress.setCurrentStopReason("PUSHED ALL MASTER");
        // 入库
        pushProgressRepository.updatePushProgressWithStatus(orderVersion, pushParameter.getGlobalOrderId(), pushProgress);
    }


	public boolean pushOrderToMaster(String orderVersion, List<OrderDetailData> orderDetailDataList,
								   PushMaster pushMaster, JSONObject commonFeature, Integer firstTimeValidPush,
								   String masterSourceType) {
		return pushController.pushOrderToMaster(orderVersion,orderDetailDataList,pushMaster,commonFeature,firstTimeValidPush,masterSourceType);
	}


    public void wheelRoundsPush(String orderVersion, String timeMark, PushParameter pushParameter,
                                List<PushMaster> listToBeHandle, JSONObject commonFeature, String masterSourceType,OrderDetailData orderDetailData) {

        // 未开启直接推送
        pushController.directPush(orderVersion, pushParameter.getMasterOrderId(),
                listToBeHandle, commonFeature, false, "0", masterSourceType, orderDetailData);

        PushProgress pushProgress = pushProgressRepository.getPushProgress(pushParameter.getGlobalOrderId(), orderVersion);

        if (pushProgress == null || PushStatus.STOP.code.equals(pushProgress.getPushStatus())) {
            return;
        }

        PushProgress updatePushProgress = new PushProgress();
        updatePushProgress.setPushedRound(pushProgress.getPushedRound() + 1);
        if (listToBeHandle.size() + pushProgress.getPushedMasterNum() >= pushProgress.getListLength()) {
            updatePushProgress.setPushStatus("STOP");
            updatePushProgress.setPushedMasterNum(pushProgress.getListLength());
        } else {
            updatePushProgress.setPushedMasterNum(pushProgress.getPushedMasterNum() + listToBeHandle.size());
        }
        pushProgressRepository.updatePushProgressWithStatus(orderVersion, pushParameter.getGlobalOrderId(), updatePushProgress);


    }


    /**
     * 首轮推送 多线程和同一个订单两个版本同时→订单锁
     *
     * @param listToBeHandle
     */
    public Integer firstRoundPush(String timeMark, PushParameter pushParameter,
                               List<PushMaster> listToBeHandle, JSONObject commonFeature,
							   PushCommonObject pushCommonObject, int firstTimeValidPush, int normalFirstTimeValidPush,
							   Integer normalFirstMatchMasterNum,OrderDetailData orderDetailData) {
        String orderVersion = pushCommonObject.getOrderVersion();
        // 获取订单锁x
//		synchronized (OrderLockPool.getInstance().getLocker(pushParameter.getGlobalOrderId())) {

		Integer pushNum = 0;

        // 推单开关判断
        if (pushByRoundEnable) {
            // 师傅列表排序
            Collections.sort(listToBeHandle);

            // 构建待推送列表
            PushMasterList toBePushedMasterList = new PushMasterList(listToBeHandle);
            // 推送
			pushNum = pushController.push(timeMark, orderVersion, pushParameter, toBePushedMasterList, commonFeature, pushCommonObject,
                    firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum);
            // 延迟推送
            if (this.isHaveMoreMaster(toBePushedMasterList.getPushProgress(), pushParameter.getGlobalOrderId(), orderVersion)) {
                pushQueueService.sendDelayOrder(pushParameter, orderVersion,
                        pushCommonObject.getPushConfig().getDelayMinutesBetweenRounds(), commonFeature, pushCommonObject);
            }

        } else {
            // 构建推送控制器
            PushController pushController = new PushController();
            // 未开启直接推送
            pushController.directPush(orderVersion, pushParameter.getMasterOrderId(),
                    listToBeHandle, commonFeature, false, "1",
                    pushCommonObject.getOrderDetailData().getPushExtraData().getMasterSourceType(),orderDetailData);

            pushNum = listToBeHandle.size();

        }
//		}

		if("on".equals(autoPunishmentPushRestrictSwitch)){
			String miToDay = DateFormatterUtil.miToDay(timeMark);
			String businessLine = "";
			if(pushCommonObject.getOrderDetailData().getBusinessLineId() == 1){
				businessLine = MasterSourceType.TOB.code;
			}else if(pushCommonObject.getOrderDetailData().getBusinessLineId() == 2){
				businessLine = MasterSourceType.TOC.code;
			}

			if(StringUtils.isNotBlank(businessLine)){
				pushRecordFacade.increasePushOrderCount(listToBeHandle,miToDay);
				pushRecordFacade.increasePushOrderCountByBusinessLine(listToBeHandle,miToDay,businessLine);
			}


		}
		return pushNum;

    }


    public Integer dynamicRoundsPush(String timeMark, PushParameter pushParameter,
                                  List<PushMaster> pushMasterList, JSONObject commonFeature, PushCommonObject pushCommonObject,
                                  int firstTimeValidPush, int normalFirstTimeValidPush, Integer normalFirstMatchMasterNum) {

    	log.info("dynamicRoundsPush");
        Long orderId = pushCommonObject.getOrderDetailData().getMasterOrderId();
        Long globalOrderId = pushCommonObject.getOrderDetailData().getGlobalOrderId();
        OrderDetailData orderDetailData = pushCommonObject.getOrderDetailData();

        String orderVersion = pushCommonObject.getOrderVersion();

        Integer appointType = pushCommonObject.getOrderDetailData().getAppointType();
        PushRuleConfig pushRuleConfig = pushCommonObject.getPushRuleConfig();


        DynamicRoundsMasterList dynamicRoundsMasterList;


        try {
            dynamicRoundsMasterList = dynamicRoundsPushService.getPushMasterList(pushMasterList, pushRuleConfig, appointType);
        } catch (Exception e) {
            //固定轮次兜底, 防止异常未推送师傅
            log.error("dynamicRoundsPush getPushMasterList error", e);
            PushConfig pushConfig = PushConfig.builder().setBestOfferNum(10)
                    .setDelayMinutesBetweenRounds(30)
                    .setFirstPushNewMasterNum(200)
                    .setFirstPushOldMasterNum(200)
                    .setDelayPushNewMasterNumPerRound(50)
                    .setDelayPushOldMasterNumPerRound(50)
                    .setFirstPushMasterType("master_old")
                    .setDelayPushMasterType("master_old")
                    .build();
            pushCommonObject.setPushConfig(pushConfig);
            pushCommonObject.getPushConfig().setPushMark(PushRound.FIRST_PUSH.getCode());
            return this.firstRoundPush(timeMark, pushParameter, pushMasterList, commonFeature, pushCommonObject,
                    firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum,orderDetailData);
        }

        //TODO 待优化，轮次没有师傅
        Integer pushNum = pushController.dynamicRoundsPush(timeMark, orderVersion, pushParameter,
                dynamicRoundsMasterList.getDynamicRoundsMasterList(), commonFeature, pushCommonObject,
                firstTimeValidPush, normalFirstTimeValidPush, normalFirstMatchMasterNum);
        pushRuleConfig.getDynamicRoundsRuleList().forEach(dynamicRoundsRule -> {
            if (CollectionUtils.isNotEmpty(dynamicRoundsRule.getWheelRoundRuleList())) {
                dynamicRoundsRule.getWheelRoundRuleList().forEach(wheelRoundRule -> {
                    wheelRoundRule.getTriggerParam().getItemList().forEach(item -> {
                        if ("push_time_interval".equals(item.getItemName())) {
                            Integer intervalTime = Integer.parseInt(item.getItemValue());
                            if (intervalTime == 0) {
                                intervalTime = 1;
                            }
                            WheelRoundsPushMessage message = new WheelRoundsPushMessage();
                            message.setGlobalOrderId(globalOrderId);
                            message.setOrderId(orderId);
                            message.setOrderVersion(orderVersion);
                            message.setPushRuleConfig(pushRuleConfig);
                            message.setDynamicRoundsRule(dynamicRoundsRule);
                            message.setWheelRoundRule(wheelRoundRule);
                            message.setCommonFeature(commonFeature);
                            pushQueueService.sendWheelRoundPush(message, intervalTime * 60 * 1000L);
                        }
                    });
                });
            }

        });


        if (pushRuleConfig.getBestOfferIntervalTime() != null && pushRuleConfig.getBestOfferIntervalTime() > 0) {
            //设置了最优报价人数，则达到时间推送剩余全部师傅
            WheelRoundsPushMessage message = new WheelRoundsPushMessage();
            message.setGlobalOrderId(globalOrderId);
            message.setOrderId(orderId);
            message.setOrderVersion(orderVersion);
            message.setPushRuleConfig(pushRuleConfig);
            message.setCommonFeature(commonFeature);
            pushQueueService.sendWheelRoundPush(message, pushRuleConfig.getBestOfferIntervalTime() * 60 * 1000L);

        }

        if (!dynamicRoundsMasterList.getIsMasterEnough()) {
            insertOrderDynamicRoundsPush(orderDetailData, dynamicRoundsMasterList);
        }

        return pushNum;

    }


	private void insertOrderDynamicRoundsPush(OrderDetailData orderDetailData,DynamicRoundsMasterList dynamicRoundsMasterList){
		OrderDynamicRoundsPush orderDynamicRoundsPush = new OrderDynamicRoundsPush();
		orderDynamicRoundsPush.setOrderId(orderDetailData.getMasterOrderId());
		orderDynamicRoundsPush.setGlobalOrderId(orderDetailData.getGlobalOrderId());
		orderDynamicRoundsPush.setAppointType(orderDetailData.getAppointType());
		orderDynamicRoundsPush.setSecondDivisionId(orderDetailData.getSecondDivisionId());
		orderDynamicRoundsPush.setThirdDivisionId(orderDetailData.getThirdDivisionId());
		orderDynamicRoundsPush.setFourthDivisionId(orderDetailData.getFourthDivisionId());
		orderDynamicRoundsPush.setCategoryId(orderDetailData.getOrderCategoryId());
		orderDynamicRoundsPush.setServeTypeId(orderDetailData.getOrderServeType());

		orderDynamicRoundsPush.setBusinessLineId(getBusinessLineId(orderDetailData));

		orderDynamicRoundsPush.setPushTime(new Date());
		orderDynamicRoundsPush.setOrderNo(orderDetailData.getOrderNo());
		orderDynamicRoundsPush.setLackType("total_master_lack");
		orderDynamicRoundsPush.setCustomerAddress(orderDetailData.getCustomerAddress());
		orderDynamicRoundsPush.setServeLevel1Ids(orderDetailData.getLv1ServeIds());

		List<Map<String,Object>> pushMasterDetailMapList = new ArrayList<>();
		Integer totalPushMasterNum = 0;
		for(DynamicRoundsMaster dynamicRoundsMaster : dynamicRoundsMasterList.getDynamicRoundsMasterList()) {
			Map<String, Object> pushMasterDetailMap = new HashMap<>();
			pushMasterDetailMap.put("roundsName", dynamicRoundsMaster.getRounds());
			Integer roundsPushNum = CollectionUtils.isNotEmpty(dynamicRoundsMaster.getMasterList()) ? dynamicRoundsMaster.getMasterList().size() : 0;
			pushMasterDetailMap.put("roundsPushNum", roundsPushNum);
			totalPushMasterNum = totalPushMasterNum + roundsPushNum;
			pushMasterDetailMap.put("roundsOfferRate", dynamicRoundsMaster.getOfferRate());
			pushMasterDetailMapList.add(pushMasterDetailMap);
		}

		orderDynamicRoundsPush.setPushMasterDetailList(JSON.toJSONString(pushMasterDetailMapList));
		orderDynamicRoundsPush.setPushMasterNum(totalPushMasterNum);
		orderDynamicRoundsPush.setOrderFromType(getOrderFrom(orderDetailData));
		orderDynamicRoundsPushRepository.insertSelective(orderDynamicRoundsPush);


	}

    private Integer getBusinessLineId(OrderDetailData orderDetailData){
        Integer businessLineId = 1;
        String pushMasterSourceType = orderDetailData.getPushExtraData().getMasterSourceType();
        if(orderDetailData.getBusinessLineId() == 1){
            businessLineId = 1;
        }else if(orderDetailData.getBusinessLineId() == 2 && OrderFrom.APPLET.valueEn.equals(orderDetailData.getOrderFrom())){
            //家庭小程序订单推单分流新师傅APP
            businessLineId = MasterSourceType.TOB.code.equals(pushMasterSourceType) ? 2 : 999;
        }else{
            businessLineId = orderDetailData.getBusinessLineId();
        }
        return businessLineId;
    }


	private String getOrderFrom(OrderDetailData orderDetailData){
		String orderFrom = orderDetailData.getOrderFrom();
		if("user".equals(orderDetailData.getAccountType())){
			if("site".equals(orderFrom) || "thirdpart".equals(orderFrom)){
				return "site";
			}else{
				return "family";
			}
		}else if("enterprise".equals(orderDetailData.getAccountType())){
			if("site".equals(orderFrom) || "applet".equals(orderFrom)){
				return "enterprise_inside";
			}else{
				return "enterprise_outside";
			}
		}

		return "";


	}




	/**
	 * 非首轮推送
	 * 
	 */
	public void delayRoundPush(PushParameter pushParameter, String orderVersion, JSONObject commonFeature, DelayPushRqt delayPushRqt) {

		PushConfig pushConfig = delayPushRqt.getPushConfig();

		String masterSourceType = delayPushRqt.getOrderDetailData().getPushExtraData().getMasterSourceType();

		if (MasterSourceType.TOB.code.equals(masterSourceType) && DateFormatterUtil.isBetweenPeriodTime(delayNightPushTimeStart,delayNightPushTimeEnd)) {
			PushCommonObject pushCommonObject = new PushCommonObject();
			pushCommonObject.setPushConfig(delayPushRqt.getPushConfig());
			pushCommonObject.setOrderDetailData(delayPushRqt.getOrderDetailData());
			pushCommonObject.setBaseSelect(delayPushRqt.getBaseSelect());
			pushQueueService.sendDelayOrder(pushParameter, orderVersion, pushConfig.getDelayMinutesBetweenRounds(), commonFeature,pushCommonObject);
			return ;
		}

		// 获取推送进度
		PushProgress pushProgress = pushProgressRepository.getPushProgress(pushParameter.getGlobalOrderId(), orderVersion);
		pushConfig.setPushMark(PushRound.DELAY_PUSH.getCode());

		//获取订单时间标记
		String timeMark = DateFormatterUtil.timeStampToTimed(Long.valueOf(orderVersion));



		PushMasterList pushMasterList = getDelayPushMasterList(pushParameter.getGlobalOrderId(),orderVersion,timeMark,pushProgress,pushConfig);

		// 推送
		boolean isSuccess = pushController.delayPush(orderVersion, pushParameter, pushMasterList,
				commonFeature,delayPushRqt);

		// 延迟推送
		if (isSuccess && this.isHaveMoreMaster(pushProgress,pushParameter.getGlobalOrderId(), orderVersion)) {
			PushCommonObject pushCommonObject = new PushCommonObject();
			pushCommonObject.setBaseSelect(delayPushRqt.getBaseSelect());
			pushCommonObject.setOrderDetailData(delayPushRqt.getOrderDetailData());
			pushCommonObject.setPushConfig(delayPushRqt.getPushConfig());
			pushQueueService.sendDelayOrder(pushParameter, orderVersion,
					pushConfig.getDelayMinutesBetweenRounds(), commonFeature,pushCommonObject);
		}
	}

	private PushMasterList getDelayPushMasterList(Long globalOrderId,String orderVersion,String timeMark,PushProgress pushProgress,PushConfig pushConfig){
		PushMasterList pushMasterList = new PushMasterList();
		if(!MasterType.ALL.code.equals(pushConfig.getDelayPushMasterType())){
// 验证开闭区间 →查询SQL为左闭右开,没问题
			//新师傅列表
			List<MasterPush> newMasterPushList = masterPushRepository.getMasterPushList(globalOrderId,orderVersion,timeMark,
					pushProgress.getNewMasterOffset(),
					pushConfig.getPushNewMasterNumPerRound(),MasterType.MASETR_NEW.code);

			List<PushMaster> newPushMasterList = new ArrayList<>();
			newMasterPushList.forEach(masterPush -> newPushMasterList.add(PushMaster.builder().setMasterId(String.valueOf(masterPush.getMasterId())).build()));


			//老师傅列表
			List<MasterPush> oldMasterPushList = masterPushRepository.getMasterPushList(globalOrderId,orderVersion,timeMark,
					pushProgress.getOldMasterOffset(),
					pushConfig.getPushOldMasterNumPerRound(), MasterType.MASTER_OLD.code);

			List<PushMaster> oldPushMasterList = new ArrayList<>();
			oldMasterPushList.forEach(masterPush -> oldPushMasterList.add(PushMaster.builder().setMasterId(String.valueOf(masterPush.getMasterId())).build()));

			// 构建待推送列表
			pushMasterList = new PushMasterList(pushProgress,newPushMasterList,oldPushMasterList);

		}else{
			List<MasterPush> masterPushList = masterPushRepository.getMasterPushList(globalOrderId,orderVersion,timeMark,
					pushProgress.getMasterOffset(),
					pushConfig.getPushOldMasterNumPerRound());
			List<PushMaster> masterList = new ArrayList<>();
			masterPushList.forEach(masterPush -> masterList.add(PushMaster.builder().setMasterId(String.valueOf(masterPush.getMasterId())).build()));
			pushMasterList = new PushMasterList(pushProgress,masterList);

		}
		return pushMasterList;

	}


	public void packageOrderPush(String orderVersion, String timeMark,PushParameter pushParameter,
								 List<PushMaster> listToBeHandle,JSONObject commonFeature,
                                 String masterSourceType,OrderDetailData orderDetailData) {
		final String miToDay = DateFormatterUtil.miToDay(timeMark);
		final String packageConfigId = commonFeature.getString(FieldConstant.PACKAGE_CONFIG_ID);

		//推送计数
		pushRecordFacade.packageOrderPushCount(
				miToDay,packageConfigId,
				String.valueOf(pushParameter.getMasterOrderId()),
				listToBeHandle.get(0).getMasterId());

		pushRecordFacade.orderPush(pushParameter.getGlobalOrderId(),Collections.singleton(listToBeHandle.get(0).getMasterId()),"order_package");

		// 总包订单包没有master_order_id
		commonFeature.put(FieldConstant.GLOBAL_ORDER_ID,pushParameter.getGlobalOrderId());

        push(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature, false, masterSourceType,orderDetailData);
	}


    public void agreementMasterPush(String orderVersion, String timeMark, PushParameter pushParameter,
                                    List<PushMaster> listToBeHandle, JSONObject commonFeature,
                                    String masterSourceType,OrderDetailData orderDetailData) {
        final String miToDay = DateFormatterUtil.miToDay(timeMark);
        final String packageConfigId = commonFeature.getString(FieldConstant.PACKAGE_CONFIG_ID);

        //推送计数
//		pushRecordFacade.packageOrderPushCount(
//				miToDay,packageConfigId,
//				String.valueOf(pushParameter.getMasterOrderId()),
//				listToBeHandle.get(0).getMasterId());
        // 总包订单包没有master_order_id
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, pushParameter.getGlobalOrderId());

        Set<String> masterSet = listToBeHandle.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());

        pushRecordFacade.orderPush(pushParameter.getGlobalOrderId(), masterSet, "agreement");

        push(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature, false, masterSourceType,orderDetailData);
    }

    public void cooperationBusinessMasterPush(String orderVersion, String timeMark, PushParameter pushParameter,
                                              List<PushMaster> listToBeHandle, JSONObject commonFeature, String masterSourceType,OrderDetailData orderDetailData) {

        Set<String> masterSet = listToBeHandle.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());

        pushRecordFacade.orderPush(pushParameter.getGlobalOrderId(), masterSet, PushMode.COOPERATION_BUSINESS_MASTER.getCode());

        push(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature, false, masterSourceType,orderDetailData);
    }

    public void afterTechniqueVerifyMasterPush(String orderVersion, String timeMark, PushParameter pushParameter,
                                              List<PushMaster> listToBeHandle, JSONObject commonFeature,
											   String masterSourceType,OrderDetailData orderDetailData) {

        Set<String> masterSet = listToBeHandle.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());

        pushRecordFacade.orderPush(pushParameter.getGlobalOrderId(), masterSet, PushMode.AFTER_TECHNIQUE_VERIFY_MASTER.getCode());

        push(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature, false, masterSourceType,orderDetailData);
    }

    public void familyAgreementMasterPush(String orderVersion, String timeMark, PushParameter pushParameter,
                                              List<PushMaster> listToBeHandle, JSONObject commonFeature, String masterSourceType,OrderDetailData orderDetailData) {

        Set<String> masterSet = listToBeHandle.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());

        pushRecordFacade.orderPush(pushParameter.getGlobalOrderId(), masterSet, PushMode.FAMILY_AGREEMENT_MASTER.getCode());

        push(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature, false, masterSourceType,orderDetailData);
    }


    public void newModelPush(String orderVersion, String timeMark, PushParameter pushParameter,
                             List<PushMaster> listToBeHandle, JSONObject commonFeature, String masterSourceType,OrderDetailData orderDetailData) {

        final String miToDay = DateFormatterUtil.miToDay(timeMark);

        //推送计数
//		pushRecordFacade.packageOrderPushCount(
//				miToDay,packageConfigId,
//				String.valueOf(pushParameter.getMasterOrderId()),
//				listToBeHandle.get(0).getMasterId());
        // 总包订单包没有master_order_id
        commonFeature.put(FieldConstant.GLOBAL_ORDER_ID, pushParameter.getGlobalOrderId());

        Set<String> masterSet = listToBeHandle.stream().map(PushMaster::getMasterId).collect(Collectors.toSet());


        push(orderVersion, timeMark, pushParameter, listToBeHandle, commonFeature, false, masterSourceType,orderDetailData);

        String pushMode = commonFeature.getString(FieldConstant.PUSH_MODE);
        if ("new_model_single".equals(pushMode)) {
            pushRecordFacade.orderPush(pushParameter.getGlobalOrderId(), masterSet, pushMode);
            pushRecordFacade.increasePushMainNewModelMasterCount(pushParameter.getGlobalOrderId());
        }
    }


	@Value("${interfereOrderPush.size:200}")
	private int interfereOrderPushSize;

	public void interfereOrderPush(List<PushMaster> pushMasterList,Long globalOrderId,Long orderId){
		Collections.sort(pushMasterList);
		List<PushMaster> interfereOrderPushList = pushMasterList.subList(0,pushMasterList.size() <= interfereOrderPushSize ? pushMasterList.size() : interfereOrderPushSize);
		pushQueueService.sendInterfereOrderPushMessage(interfereOrderPushList,globalOrderId,orderId);
	}


	}
