package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.wanshifu.adapter.api.ServiceApi;
import com.wanshifu.adapter.dto.service.Service4ReferenceIncreaseAttributeValueDto;
import com.wanshifu.adapter.dto.service.Service4ReferenceIncreaseByIdAndAttributeTagReq;
import com.wanshifu.adapter.dto.service.Service4ReferenceIncreaseQueryResp;
import com.wanshifu.base.address.domain.po.PositionConvert;
import com.wanshifu.common.enums.AttributeKeyEnum;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.api.OrderRetAccessoriesApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderBaseByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.GetOrderByGlobalIdRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.response.GetOrderBaseByGlobalIdRsp;
import com.wanshifu.enterprise.order.domain.order.api.request.OrderRetAccessoriesRqt;
import com.wanshifu.enterprise.order.domain.po.OrderRetAccessories;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.activity.api.OrderPackageApi;
import com.wanshifu.master.activity.domain.orderPackage.request.MatchAttributeSkuRqt;
import com.wanshifu.master.activity.domain.po.OrderPackageAttributeSku;
import com.wanshifu.master.order.domains.enums.ServeType;
import com.wanshifu.master.order.push.domain.dto.OrderMatchMasterRqt;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.PushExtraData;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.offer.api.NormalOrderListApi;
import com.wanshifu.order.offer.api.NormalOrderResourceApi;
import com.wanshifu.order.offer.api.appointed.AppointedModuleResourceApi;
import com.wanshifu.order.offer.api.infoorder.InfoOrderResourceApi;
import com.wanshifu.order.offer.domains.api.request.GetSubTaskStatusRqt;
import com.wanshifu.order.offer.domains.api.request.infoorder.GetOrderInfoRqt;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.order.offer.domains.api.response.GetSubTaskStatusResp;
import com.wanshifu.order.offer.domains.api.response.OrderBaseComposite;
import com.wanshifu.order.offer.domains.api.response.appointed.OrderGrabByIdResp;
import com.wanshifu.order.offer.domains.api.response.infoorder.GetOrderInfoResp;
import com.wanshifu.order.offer.domains.bo.service.ServiceAttribute;
import com.wanshifu.order.offer.domains.bo.service.ServiceInfo;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import com.wanshifu.order.offer.domains.enums.OrderFrom;
import com.wanshifu.order.offer.domains.po.*;
import com.wanshifu.util.LongUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tools.ant.util.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单数据构造器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderDataBuilder {

    @Resource
    private NormalOrderResourceApi normalOrderResourceApi;

    @Resource
    private AddressCommon addressCommon;

    @Resource
    private OrderConfigCommon orderConfigCommon;

//    @Resource
//    private ServeApi enterpriseServeApi;

    @Resource
    private InfoQueryApi infoQueryApi;

    @Resource
    private OrderServiceAttributeService orderServiceAttributeService;

    @Resource
    private OrderCommon orderCommon;

    @Resource
    private Tools tools;

    @Resource
    private OrderPackageApi orderPackageApi;

    @Resource
    private ServiceApi serviceApi;


    @Resource
    private InfoOrderResourceApi infoOrderResourceApi;

    @Resource
    private AppointedModuleResourceApi appointedModuleResourceApi;


    @Value("${push.toc.cityList:}")
    private String pushTocCityList;


    @Value("${agreement.special.city.list:441900}")
    private String agreementSpecialCityList;

    @Resource
    private BindingTechnologyCommon bindingTechnologyCommon;

    @Resource
    private OrderRetAccessoriesApi orderRetAccessoriesApi;

    @Resource
    private NormalOrderListApi normalOrderListApi;


    public OrderDetailData build(Long orderId){
        OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
        orderMatchMasterRqt.setMasterOrderId(orderId);
        return this.build(orderMatchMasterRqt);
    }


    public OrderDetailData build(OrderMatchMasterRqt orderMatchMasterRqt){

        Integer businessLineId = orderMatchMasterRqt.getBusinessLineId();

        if(Objects.nonNull(businessLineId) && businessLineId == 3){
            return buildInfoOrderDetailData(orderMatchMasterRqt);
        }

        Long masterOrderId = orderMatchMasterRqt.getMasterOrderId();
        OrderGrabByIdResp orderGrabByIdResp = tools.catchLogThrow(() -> appointedModuleResourceApi.getOrderGrabById(masterOrderId));
        if(Objects.isNull(orderGrabByIdResp)){
            log.error(String.format("查询订单数据为空,rqt:%s",JSON.toJSONString(orderMatchMasterRqt)));
            throw new BusException("查询订单为空!,masterOrderId:" + orderMatchMasterRqt.getMasterOrderId());
        }
        OrderBase orderBase = orderGrabByIdResp.getOrderBase();
        OrderGrab orderGrab = orderGrabByIdResp.getOrderGrab();

        if(AccountType.ENTERPRISE.code.equals(orderBase.getAccountType()) && Objects.isNull(orderGrab)){
            return null;
        }

        OrderExtraData orderExtraData = orderGrabByIdResp.getOrderExtraData();
        if(!checkOrderCondition(orderBase,orderGrab)){
            log.info(String.format("订单状态不满足，无需推单,orderMatchRqt:%s", JSON.toJSON(orderMatchMasterRqt)));
            return null;
        }
        List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = orderGrabByIdResp.getOrderServiceAttributeInfos();
        List<OrderGoods> orderGoodsList = orderGrabByIdResp.getOrderGoodsList();

        OrderDetailData orderDetailData = build(orderBase,orderGrab,orderExtraData,orderServiceAttributeInfoList,orderGoodsList);

        orderDetailData.getPushExtraData().setOrderPushEliminateMasterIds(orderMatchMasterRqt.getOrderPushEliminateMasterIds());
        orderDetailData.getPushExtraData().setMatchSceneCode(orderMatchMasterRqt.getMatchSceneCode());

        orderDetailData.setIsExclusiveTeamMaster(orderMatchMasterRqt.getIsExclusiveTeamMaster());
        orderDetailData.getPushExtraData().setPushMode(orderMatchMasterRqt.getPushMode());
        orderDetailData.getPushExtraData().setExclusivePushModeList(orderMatchMasterRqt.getExclusivePushModeList());
        orderDetailData.getPushExtraData().setHandoffTag(orderMatchMasterRqt.getHandoffTag());
        if(AccountType.USER .code.equals(orderDetailData.getAccountType()) && AppointType.NORMAL.value.equals(orderDetailData.getAppointType())){
            orderDetailData.getPushExtraData().setDirectAppointMasterId(orderGrab.getHireMasterId());
        }
        orderDetailData.setTagName(orderMatchMasterRqt.getTagName());
        orderDetailData.setRePushSource(orderMatchMasterRqt.getRePushSource());
        orderDetailData.setCancelAppoint(
                getCancelAppoint(
                        orderBase.getGlobalOrderTraceId(),
                        orderBase.getAccountType(),
                        orderBase.getAccountId()
                )
        );
        orderDetailData.setServiceIds(orderBase.getServiceIds());
        orderDetailData.setCustomerAddress(orderExtraData.getBuyerAddress());
        if(ServeType.isDeliveryTypeNonReturn(orderDetailData.getOrderServeType())){
            OrderBaseComposite orderBaseComposite = tools.catchLogThrow(() -> normalOrderResourceApi.getOrderBaseComposite(masterOrderId,null));

            OrderLogisticsInfo orderLogisticsInfo = orderBaseComposite.getOrderLogisticsInfo();
            if(Objects.nonNull(orderLogisticsInfo) && StringUtils.isNotBlank(orderLogisticsInfo.getPickupAddress())){
                PositionConvert positionConvert = addressCommon.getPositionConvertByDivisionId(orderBase.getThirdDivisionId(),orderLogisticsInfo.getPickupAddress());
                if(Objects.nonNull(positionConvert)){
                    orderDetailData.setPickupAddressLngLat(positionConvert.getLongitude() + "," + positionConvert.getLatitude());
                }
            }
        }


        if(CollectionUtils.isNotEmpty(orderMatchMasterRqt.getMasterAutoOfferPriceInfoList())){
            List<PushExtraData.MasterAutoOfferPriceInfo> masterAutoOfferPriceInfoList = orderMatchMasterRqt.getMasterAutoOfferPriceInfoList().stream().map(masterAutoOfferPriceInfo -> {
                PushExtraData.MasterAutoOfferPriceInfo autoOfferPriceInfo = new PushExtraData.MasterAutoOfferPriceInfo();
                BeanUtils.copyProperties(masterAutoOfferPriceInfo,autoOfferPriceInfo);
                return autoOfferPriceInfo;
            }).collect(Collectors.toList());
            orderDetailData.getPushExtraData().setMasterAutoOfferPriceInfoList(masterAutoOfferPriceInfoList);
        }
        orderDetailData.setOrderTags(getOrderTag(orderDetailData.getMasterOrderId()));
        orderDetailData.getPushExtraData().setMasterSourceType(StringUtils.isNotBlank(orderMatchMasterRqt.getMasterSourceType()) ? orderMatchMasterRqt.getMasterSourceType() : getMasterSourceType(orderDetailData));
        orderDetailData.setOrderPayStatus(orderGrab.getOrderPayStatus());
        log.info("orderDetailData:" + JSON.toJSONString(orderDetailData));
        return orderDetailData;
    }


    private List<String> getOrderTag(Long orderId){
        try{
            List<OrderExclusiveTagResp> orderExclusiveTagRespList = tools.catchLog(() -> normalOrderResourceApi.getOrderExclusiveTag(orderId,null),"normalOrderResourceApi.getOrderExclusiveTag",orderId);
            if(CollectionUtils.isNotEmpty(orderExclusiveTagRespList)){
                return orderExclusiveTagRespList.stream().map(OrderExclusiveTagResp::getTagName).collect(Collectors.toList());
            }
        }catch(Exception e){
            log.error("getOrderTag error" , e);
        }
        return new ArrayList<>();
    }


    private boolean isPushToc(OrderDetailData orderDetailData){
        Long cityDivisionId = orderDetailData.getSecondDivisionId();
        if(StringUtils.isBlank(pushTocCityList)){
            return false;
        }

        if ("all".equals(pushTocCityList)) {
            return true;
        }

        Set<Long> cityIdSet = Arrays.stream(pushTocCityList.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(cityIdSet)) {
            if (cityIdSet.contains(cityDivisionId)) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }


    private String getMasterSourceType(OrderDetailData orderDetailData){
        if(orderDetailData.getBusinessLineId() == 1){
            return MasterSourceType.TOB.code;
        }else if(orderDetailData.getBusinessLineId() == 2){
            if(isPushToc(orderDetailData)){
                if (CollectionUtil.isNotEmpty(orderDetailData.getOrderTags())
                        && orderDetailData.getOrderTags().contains("toc_meituan")
                        && Objects.nonNull(orderDetailData.getAppointType())
                        && orderDetailData.getAppointType() == 5) {
                    //美团预付款订单，推B端师傅
                    log.info("toc_meituan and prepay order,ready push tob master,orderId:{},appointType:{},orderTags:{},businessLineId:{},orderFrom:{}",
                            orderDetailData.getMasterOrderId(), orderDetailData.getAppointType(), orderDetailData.getOrderTags(), orderDetailData.getBusinessLineId(), orderDetailData.getOrderFrom());
                    return MasterSourceType.TOB.code;
                } else {
                    return MasterSourceType.TOC.code;
                }
            }else{
                return MasterSourceType.TOB.code;
            }
        }else{
            return MasterSourceType.TOB.code;
        }
    }







    public OrderDetailData build(OrderBase orderBase,OrderGrab orderGrab,OrderExtraData orderExtraData,List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,
                                 List<OrderGoods> orderGoodsList){



        OrderDetailData orderDetailData = new OrderDetailData();

        List<OrderIkeaGoods> ikeaGoodsList = Collections.emptyList();
        if(OrderFrom.IKEA.valueEn.equals(orderBase.getOrderFrom())){
            ikeaGoodsList = orderCommon.queryOrderIkeaGoods(orderBase.getOrderId(), orderBase.getGlobalOrderTraceId());
        }
        orderDetailData.setMasterOrderId(orderBase.getOrderId());
        orderDetailData.setGlobalOrderId(orderBase.getGlobalOrderTraceId());
        orderDetailData.setOrderNo(orderBase.getOrderNo());
        orderDetailData.setBusinessLineId(orderBase.getBusinessLineId());
        orderDetailData.setAccountType(orderBase.getAccountType());
        orderDetailData.setAccountId(orderBase.getAccountId());
        orderDetailData.setSecondDivisionId(addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId()));
        orderDetailData.setThirdDivisionId(orderBase.getThirdDivisionId());
        orderDetailData.setFourthDivisionId(orderBase.getFourthDivisionId());
        orderDetailData.setOrderCreateTime(orderBase.getOrderCreateTime());

        //TODO 补充商品数据
        setOrderGoodsId(orderDetailData,orderServiceAttributeInfoList,orderGoodsList,ikeaGoodsList);

        orderDetailData.setLv1ServeIds(orderBase.getServeLevel1Ids());
        if (StringUtils.isNotBlank(orderBase.getServeIds())) {
            List<ServeBaseInfoResp> serveBaseInfoRespList = orderConfigCommon.getServeList(orderBase.getServeIds(), orderBase.getBusinessLineId());
            if (CollectionUtils.isNotEmpty(serveBaseInfoRespList)) {
                List<Long> lv2ServeIdList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lv2ServeIdList)) {
                    orderDetailData.setLv2ServeIds(Joiner.on(",").join(lv2ServeIdList));
                }
            }
        }
        orderDetailData.setLv3ServeIds(orderBase.getServeIds());


        orderDetailData.setOrderTechniques(orderBase.getBindingTechnologyIds());
        Collections.addAll(orderDetailData.getOrderTechniqueSet(), orderBase.getBindingTechnologyIds().split("\\|"));

        orderDetailData.setOrderCategoryId(Long.valueOf(orderBase.getCategoryId()));
        orderDetailData.setOrderServeType(orderBase.getServeType());
        orderDetailData.setOrderFrom(orderBase.getOrderFrom());
        orderDetailData.setOrderLabel(orderBase.getOrderLabel());
        if(Objects.nonNull(orderExtraData.getExpectCompleteTime())){
            orderDetailData.setExpectCompleteTime(DateUtils.format(orderExtraData.getExpectCompleteTime(),"yyyy-MM-dd HH:mm:ss"));
        }
        orderDetailData.setOrderVersion(null);

        Date expectDoorInStartDate;
        Date expectDoorInEndDate = null;
        if (orderCommon.isIkeaOrder(orderBase)) {
            expectDoorInStartDate = orderExtraData.getExpectCompleteTime();
        } else {
            expectDoorInStartDate = orderExtraData.getExpectDoorInStartDate();
            expectDoorInEndDate = orderExtraData.getExpectDoorInEndDate();
        }


        if (Objects.nonNull(expectDoorInStartDate)) {
            orderDetailData.setExpectDoorInStartTimeString(DateUtil.formatDateTime(expectDoorInStartDate));
            orderDetailData.setExpectDoorInStartDate(DateUtil.formatDate(expectDoorInStartDate));
        }

        if (Objects.nonNull(expectDoorInEndDate)) {
            orderDetailData.setExpectDoorInEndTimeString(DateUtil.formatDateTime(expectDoorInEndDate));
        }

        orderDetailData.setOnTimeOrderFlag(orderExtraData.getOnTimeOrderFlag());
        orderDetailData.setTimerFlag(orderExtraData.getTimerFlag());
        //加急单
        orderDetailData.setEmergencyOrderFlag(orderExtraData.getEmergencyOrderFlag());
        if (AccountType.ENTERPRISE.code.equals(orderBase.getAccountType())) {
            GetOrderBaseByGlobalIdRqt getOrderByGlobalIdRqt = new GetOrderBaseByGlobalIdRqt();
            getOrderByGlobalIdRqt.setGlobalOrderTraceId(orderBase.getGlobalOrderTraceId());
            GetOrderBaseByGlobalIdRsp getOrderBaseByGlobalIdRsp = tools.catchLogThrow(() -> infoQueryApi.getOrderBaseByGlobalId(getOrderByGlobalIdRqt));
            if(Objects.isNull(getOrderBaseByGlobalIdRsp) || getOrderBaseByGlobalIdRsp.getOrderBase() == null){
                log.error(String.format("查询总包订单数据为空,globalOrderTraceId:%d",orderBase.getGlobalOrderTraceId()));
                throw new BusException("查询总包订单为空!,globalOrderTraceId:" + orderBase.getGlobalOrderTraceId());
            }
            orderDetailData.setUserId("user".equals(getOrderBaseByGlobalIdRsp.getOrderBase().getFromAccountType()) ? getOrderBaseByGlobalIdRsp.getOrderBase().getFromAccountId() : 0L);
        }
        orderDetailData.setAppointType(orderGrab.getAppointType());
        BigDecimal buyerAddressLongitude = orderExtraData.getBuyerAddressLongitude();
        BigDecimal buyerAddressLatitude = orderExtraData.getBuyerAddressLatitude();
        if(Objects.nonNull(buyerAddressLongitude) && buyerAddressLongitude.compareTo(BigDecimal.ZERO) > 0 &&
                Objects.nonNull(buyerAddressLatitude) && buyerAddressLatitude.compareTo(BigDecimal.ZERO) > 0){
            orderDetailData.setOrderLngLat(orderExtraData.getBuyerAddressLongitude() + "," + orderExtraData.getBuyerAddressLatitude());
        }


//        BigDecimal destLongitude = orderExtraData.getDestinationLongitude();
//        BigDecimal destLatitude = orderExtraData.getDestinationLatitude();
//        if(Objects.nonNull(destLongitude) && destLongitude.compareTo(BigDecimal.ZERO) > 0 &&
//                Objects.nonNull(destLatitude) && destLatitude.compareTo(BigDecimal.ZERO) > 0){
//            orderDetailData.setDestLngLat(destLongitude + "," + destLatitude);
//        }


        orderDetailData.setCustomerPhone(orderExtraData.getBuyerPhone());
//        orderDetailData.setIsExclusiveTeamMaster(orderMatchRqt.getIsExclusiveTeamMaster());
        orderDetailData.getPushExtraData().setOrderPushExcludeMasterIds(orderExtraData.getOrderPushExcludeMasterIds());
//        orderDetailData.getPushExtraData().setOrderPushEliminateMasterIds(orderMatchRqt.getOrderPushEliminateMasterIds());
        orderDetailData.getPushExtraData().setTeamMasterOrderPush(judgeTeamMasterOrderPush(orderBase,orderServiceAttributeInfoList,orderGoodsList,ikeaGoodsList));
        orderDetailData.setOrderVersion(String.valueOf(System.currentTimeMillis()));

        //一口价金额
        BigDecimal definiteServeFee = new BigDecimal(0);
        if(Objects.nonNull(orderBase.getOrderId()) && orderBase.getOrderId() > 0){
            OrderBaseComposite orderBaseComposite = tools.catchLogThrow(() -> normalOrderResourceApi.getOrderBaseComposite(orderBase.getOrderId(), null));
            if (orderBaseComposite != null && orderBaseComposite.getOrderInitFee() != null) {
                definiteServeFee = Optional.ofNullable(orderBaseComposite.getOrderInitFee()).map(OrderInitFee::getDefiniteServeFee).orElse(BigDecimal.ZERO);
            }
        }

        orderDetailData.setDefiniteServeFee(definiteServeFee);
        this.setAttributeKeys(orderBase,orderServiceAttributeInfoList,orderDetailData);
//        orderDetailData.getPushExtraData().setPushMode(orderMatchRqt.getPushMode());
        orderDetailData.setIsSendBackOld(orderExtraData.getIsSendBackOld());
        if(Objects.nonNull(orderDetailData.getIsSendBackOld()) && orderDetailData.getIsSendBackOld() == 1){
            GetSubTaskStatusRqt rqt = new GetSubTaskStatusRqt();
            rqt.setOrderIds(Collections.singletonList(orderBase.getOrderId()));
            rqt.setSubTaskName("send_back_old");
            List<GetSubTaskStatusResp> respList = normalOrderListApi.getSubTaskInfo(rqt);
            if(CollectionUtils.isNotEmpty(respList)){
                String subTaskContent = respList.get(0).getSubTaskContent();
                if(StringUtils.isNotBlank(subTaskContent)){
                    Map<String,Object> subTaskContentMap = JSON.parseObject(subTaskContent,Map.class);
                    orderDetailData.setSendBackOldPayMode((String)subTaskContentMap.get("returnPayMode"));
                }
            }
        }
        orderDetailData.setDemolishType(orderExtraData.getDemolishType());
        orderDetailData.setIsParts(orderExtraData.getIsParts());


        //商品属性列表1:临时方案,不发送完整对象
        final ArrayList<String> attributeListOne = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderServiceAttributeInfoList)) {
            orderServiceAttributeInfoList.stream().forEach(e -> {
                ServiceInfo serviceInfo = orderServiceAttributeService.getServiceInfo(e.getMasterServiceInfos());
                List<String> values = orderServiceAttributeService
                        .getOrderServiceInfoTagValues(AttributeKeyEnum.attached_goods, serviceInfo, true);
                if (CollectionUtils.isNotEmpty(values)) {
                    attributeListOne.addAll(values);
                }
            });
        }

        orderDetailData.setOrderAttachedGoods(attributeListOne);


        //商品属性列表2:临时方案,不发送完整对象
        final JSONArray objects = new JSONArray();
        if (CollectionUtils.isNotEmpty(orderGoodsList)) {
            orderGoodsList.stream().forEach(e -> {
                String ancillaryGoods = e.getAncillaryGoods();
                if (com.wanshifu.framework.utils.StringUtils.isNotEmpty(ancillaryGoods)) {
                    JSONObject jsonObject = JSONObject.parseObject(ancillaryGoods);
                    if (jsonObject != null) {
                        objects.add(jsonObject);
                    }
                }
            });
        }

        orderDetailData.setOrderAncillaryGoods(objects);

        if(AccountType.USER.code.equals(orderBase.getAccountType())){
            orderDetailData.setUserId(orderBase.getAccountId());
        }
        return orderDetailData;
    }

    private OrderDetailData buildInfoOrderDetailData(OrderMatchMasterRqt orderMatchMasterRqt){
        com.wanshifu.order.offer.domains.api.request.infoorder.GetOrderInfoRqt getOrderInfoRqt = new GetOrderInfoRqt();
        getOrderInfoRqt.setOrderId(orderMatchMasterRqt.getMasterOrderId());
        GetOrderInfoResp orderInfo = infoOrderResourceApi.getOrderInfo(getOrderInfoRqt);
        if(Objects.isNull(orderInfo)){
            return null;
        }

        OrderDetailData orderDetailData = new OrderDetailData();
        InfoOrderBase infoOrderBase = orderInfo.getInfoOrderBase();
        InfoOrderExtraData infoOrderExtraData = orderInfo.getInfoOrderExtraData();
        orderDetailData.setMasterOrderId(infoOrderBase.getOrderId());
        orderDetailData.setBusinessLineId(infoOrderBase.getBusinessLineId());
        orderDetailData.setCustomerPhone(infoOrderExtraData.getContactPhone());
        orderDetailData.setAccountId(infoOrderBase.getAccountId());
        orderDetailData.setAccountType(infoOrderBase.getAccountType());
        orderDetailData.setGlobalOrderId(infoOrderBase.getGlobalOrderTraceId());
        orderDetailData.setOrderCategoryId(Long.valueOf(infoOrderBase.getCategoryId()));

        if (StringUtils.isNotBlank(infoOrderBase.getServeIds())) {
            List<ServeBaseInfoResp> serveBaseInfoRespList = orderConfigCommon.getServeList(infoOrderBase.getServeIds(), infoOrderBase.getBusinessLineId());
            if (CollectionUtils.isNotEmpty(serveBaseInfoRespList)) {
                List<Long> lv2ServeIdList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(lv2ServeIdList)) {
                    orderDetailData.setLv2ServeIds(Joiner.on(",").join(lv2ServeIdList));
                }

            }
        }

        orderDetailData.setLv1ServeIds(infoOrderBase.getServeLevel1Ids());
        orderDetailData.setLv3ServeIds(infoOrderBase.getServeIds());
        orderDetailData.setOrderTechniques(infoOrderBase.getBindingTechnologyIds());
        Collections.addAll(orderDetailData.getOrderTechniqueSet(), infoOrderBase.getBindingTechnologyIds().split("\\|"));
        orderDetailData.setThirdDivisionId(infoOrderBase.getThirdDivisionId());
        orderDetailData.setFourthDivisionId(infoOrderBase.getFourthDivisionId());
        orderDetailData.setOrderVersion(String.valueOf(System.currentTimeMillis()));
        orderDetailData.setSecondDivisionId(addressCommon.getCityDivisionIdByDivisionId(infoOrderBase.getThirdDivisionId()));
        orderDetailData.getPushExtraData().setHandoffTag(orderMatchMasterRqt.getHandoffTag());
        //TODO 完善商品信息数据
        return orderDetailData;
    }


    /**
     * 订单是否取消指派
     * @param globalOrderId
     * @return
     */
    private int getCancelAppoint(Long globalOrderId,String accountType,Long accountId){
        final List<OrderCancelMasterLog> orderCancelMasterLogs = appointedModuleResourceApi.selectOrderCancelMasterLog(
                globalOrderId,accountType,accountId);
        if (orderCancelMasterLogs!=null&&orderCancelMasterLogs.size()!=0) {
            final boolean anyMatch = orderCancelMasterLogs.stream().anyMatch(row -> row.getIsDelete() == 0);
            if (anyMatch) {
                return 1;
            }
        }
        return 0;
    }


    /**
     * 判断订单是否可推团队师傅
     * @param orderBase
     * @return
     */
    private int judgeTeamMasterOrderPush(OrderBase orderBase,List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,List<OrderGoods> orderGoodsList,
                                         List<OrderIkeaGoods> orderIkeaGoodsList) {
        String orderFrom = orderBase.getOrderFrom();
        List<String> baseServe = Arrays.asList("1003106,2003106,3003106,4003106,1003133,2003133,3003133,4003133,1133144,2133144,3133144,4133144,1193144,2193144,3193144,4193144,1203144,2203144,3203144,4203144,1143144,2143144,3143144,4143144,1153144,2153144,3153144,4153144".split(","));
        String[] serveArray = Optional.ofNullable(orderBase.getServeIds()).orElse("").split(",");
        List<String> baseServeTypes = Arrays.asList("1,2,3,4,15".split(","));

        Integer categoryId = orderBase.getCategoryId();
        Integer serveType = orderBase.getServeType();
        BigDecimal goodsNum = orderServiceAttributeService.getGoodsNum(orderServiceAttributeInfoList,orderGoodsList,orderIkeaGoodsList);

        boolean conditionOne = OrderFrom.SITE.valueEn.equals(orderFrom) && Stream.of(serveArray).anyMatch(baseServe::contains);
        boolean conditionTwo = OrderFrom.SITE.valueEn.equals(orderFrom)
                && Objects.equals(categoryId, 1)
                && baseServeTypes.contains(String.valueOf(serveType))
                && goodsNum.compareTo(BigDecimal.TEN) > 0;

        return (conditionOne || conditionTwo) ? 1 : 0;
    }

    private boolean checkOrderCondition(OrderBase orderBase,OrderGrab orderGrab){
//        if(!OrderStatus.TRADING.code.equals(orderBase.getOrderStatus()) ||
//                !Arrays.asList(2,4,5).contains(orderGrab.getAppointType()) ||
//        orderGrab.getConfirmServeStatus() == 1){
//            return false;
//        }
        return true;
    }


    private void setOrderGoodsId(OrderDetailData orderDetailData,List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,List<OrderGoods> orderGoodsList,
                                 List<OrderIkeaGoods> orderIkeaGoodsList){
        Set<String> lv2GoodsIdSet =  new HashSet<>();
        Set<String> lv3GoodsIdSet =  new HashSet<>();
        if (CollectionUtils.isNotEmpty(orderServiceAttributeInfoList)) {
            orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> {
                ServiceInfo serviceInfo = orderServiceAttributeService.getServiceInfo(orderServiceAttributeInfo.getMasterServiceInfos());
                lv2GoodsIdSet.add(String.valueOf(serviceInfo.getGoodsCategoryId()));
                lv3GoodsIdSet.add(String.valueOf(serviceInfo.getGoodsCategoryChildId()));
            });
        } else {
            orderGoodsList.forEach(goods -> {
                lv2GoodsIdSet.add(String.valueOf(goods.getGoodsCategory()));
                lv3GoodsIdSet.add(String.valueOf(goods.getCategoryChild()));
            });
        }
        orderDetailData.setParentGoodsIdsSet(lv2GoodsIdSet);
        orderDetailData.setParentGoodsIds(CollectionUtils.isNotEmpty(lv2GoodsIdSet) ? String.join(",",lv2GoodsIdSet) : null);
        orderDetailData.setChildGoodsIds(CollectionUtils.isNotEmpty(lv3GoodsIdSet) ? String.join(",",lv3GoodsIdSet) : null);
        orderDetailData.setChildGoodsIdsSet(lv3GoodsIdSet);


    }


    public void setAttributeKeys(OrderBase orderBase,List<OrderServiceAttributeInfo> orderServiceAttributeInfoList,OrderDetailData orderDetailData){

        JSONArray goodsArray = new JSONArray();
        if (orderCommon.isDynamicConfiguration(orderBase)) {
            orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> {
                //TODO 使用serviceInfos
                ServiceInfo serviceInfo = orderServiceAttributeService.getServiceInfo(orderServiceAttributeInfo.getMasterServiceInfos());
                JSONObject jsonObject = new JSONObject();
                //组装订单包需要的服务属性
                if (orderServiceAttributeInfoList.size() == 1) {
                    this.buildGoodsAttribute(jsonObject, this.getServiceAttributeRetainAll(serviceInfo),serviceInfo.getServiceId());
                }
                goodsArray.add(jsonObject);
            });
        }
        this.setAttributeKeys(goodsArray,orderDetailData);
    }


    /**
     * 通过消息体解析订单商品信息
     * @return
     */
    private void setAttributeKeys(JSONArray jsonArray,OrderDetailData orderDetailData) {
        if (jsonArray == null) {
            return ;
        }
        Set<String> goodsAttributes = new HashSet<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject orderGoodRow = jsonArray.getJSONObject(i);
            final JSONArray goodsAttributeArray = orderGoodRow.getJSONArray("goods_attribute");
            if (goodsAttributeArray!=null) {
                for (Object attr : goodsAttributeArray) {
                    goodsAttributes.add(attr.toString());
                }
            }
            //只支持单商品属性-多商品会取最后一个-目前【只在订单包的尺寸匹配用到】
            final JSONObject goodsAttributeValue = orderGoodRow.getJSONObject("attribute_value");
            if (goodsAttributeValue!=null) {
                orderDetailData.setAttributeValue(goodsAttributeValue);
            }
        }

        if (goodsAttributes.size() != 0) {
            orderDetailData.setAttributes(goodsAttributes);
        }
    }

    private void buildGoodsAttribute(JSONObject goods, Map<String, String> attributeRetainAll,Long serviceId) {
        if (Objects.nonNull(attributeRetainAll) && !attributeRetainAll.isEmpty()) {

            Long attributeSkuId = this.matchAttributeSkuId(attributeRetainAll,serviceId);
            if (LongUtil.notEmpty(attributeSkuId)) {
                goods.put("goods_attribute", Collections.singletonList(attributeSkuId.toString()));
            } else {
                //兼容旧订单包, 旧订单包推送完可删除
                goods.put("goods_attribute", attributeRetainAll.keySet());
                goods.put("attribute_value", attributeRetainAll.entrySet().stream().filter((e) -> com.wanshifu.framework.utils.StringUtils.isNotEmpty(e.getValue())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
            }
        }
    }


    /**
     * 获取订单属性和订单包打标签交集
     * @param serviceInfo
     * @return
     */
    public Map<String, String> getServiceAttributeRetainAll(ServiceInfo serviceInfo) {
        Service4ReferenceIncreaseByIdAndAttributeTagReq attributeTagReq = new Service4ReferenceIncreaseByIdAndAttributeTagReq();
        attributeTagReq.setServiceId(serviceInfo.getServiceId());
        attributeTagReq.setAttributeTag("order_package");
        Service4ReferenceIncreaseQueryResp service4ReferenceIncreaseQueryResp = serviceApi.service4ReferenceIncreaseQueryByIdAndAttributeTag(attributeTagReq);

        if (Objects.nonNull(service4ReferenceIncreaseQueryResp) && CollectionUtils.isNotEmpty(service4ReferenceIncreaseQueryResp.getServiceAttributeValueList())) {
            HashMap<String, String> serviceAttributePathNoMap = this.getServiceAttributePathNo(serviceInfo.getRootAttributeDetailList());
            List<String> orderPackageTagList = service4ReferenceIncreaseQueryResp.getServiceAttributeValueList().stream().map(Service4ReferenceIncreaseAttributeValueDto::getAttributePathNo).collect(Collectors.toList());
            return serviceAttributePathNoMap.entrySet().stream().filter(e -> orderPackageTagList.contains(e.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        return null;
    }


    /**
     * 通过订单包含的ap值，匹配属性组合配置skuId
     *
     * @param attributeRetainAll
     * @return
     */
    public Long matchAttributeSkuId (Map<String, String> attributeRetainAll,Long serviceId) {

        if (!attributeRetainAll.isEmpty()) {

            List<MatchAttributeSkuRqt.AttributeInfo> list = new ArrayList<>();

            attributeRetainAll.forEach((key, value) -> {
                MatchAttributeSkuRqt.AttributeInfo attributeInfo = new MatchAttributeSkuRqt.AttributeInfo();
                attributeInfo.setAttributePathNo(key);
                attributeInfo.setAttributePathValue(value);
                list.add(attributeInfo);
            });

            MatchAttributeSkuRqt rqt = new MatchAttributeSkuRqt();
            rqt.setAttributePathList(list);
            rqt.setServiceId(serviceId);
            List<OrderPackageAttributeSku> attributeSkuList = tools.catchLog(() -> orderPackageApi.matchAttributeSku(rqt),"orderPackageApi.matchAttributeSku",rqt);

            if (CollectionUtils.isNotEmpty(attributeSkuList)) {
                return attributeSkuList.get(0).getAttributeSkuId();
            }
        }
        return null;
    }


    /**
     * 组装推单需要的子属性 AttributePathNo
     * @param serviceAttributeList
     * @return
     */
    public HashMap<String, String> getServiceAttributePathNo(List<ServiceAttribute> serviceAttributeList) {
        HashMap<String, String> attributePathNoList = new HashMap<>();
        if (CollUtil.isNotEmpty(serviceAttributeList)) {
            serviceAttributeList.forEach(row -> parseData(row, attributePathNoList));
        }
        return attributePathNoList;
    }


    private void parseData(ServiceAttribute serviceAttribute, HashMap<String, String> attributePathNoMap) {

        String attributePathNo = serviceAttribute.getAttributePathNo();

        if (StringUtils.isNotEmpty(attributePathNo)) {
            attributePathNoMap.put(attributePathNo, "");
        }

        if (CollUtil.isNotEmpty(serviceAttribute.getChildList())) {
            serviceAttribute.getChildList().forEach(row -> {
                if (StringUtils.isNotEmpty(row.getAttributePathNo())) {
                    attributePathNoMap.put(row.getAttributePathNo(), row.getAttributeValueName().contains("输入框") ? row.getValue() : "");
                }
                if (CollUtil.isNotEmpty(row.getChildList())) {
                    row.getChildList().forEach(child -> parseData(child, attributePathNoMap));
                }
            });
        }
    }


    public OrderDetailData buildEnterpriseOrder(Long globalOrderId){
        GetOrderByGlobalIdRqt getOrderByGlobalIdRqt = new GetOrderByGlobalIdRqt();
        getOrderByGlobalIdRqt.setGlobalOrderTraceId(globalOrderId);

        GetOrderBaseByGlobalIdRqt getOrderBaseByGlobalIdRqt = new GetOrderBaseByGlobalIdRqt();
        getOrderBaseByGlobalIdRqt.setGlobalOrderTraceId(globalOrderId);
        GetOrderBaseByGlobalIdRsp getOrderBaseByGlobalIdRsp = tools.catchLogThrow(() -> infoQueryApi.getOrderBaseByGlobalId(getOrderBaseByGlobalIdRqt));
        OrderDetailData orderDetailData = null;
        if (Objects.nonNull(getOrderBaseByGlobalIdRsp) && Objects.nonNull(getOrderBaseByGlobalIdRsp.getOrderBase())) {
            com.wanshifu.enterprise.order.domain.po.OrderBase orderBase = getOrderBaseByGlobalIdRsp.getOrderBase();
            com.wanshifu.enterprise.order.domain.po.OrderExtraData orderExtraData = getOrderBaseByGlobalIdRsp.getOrderExtraData();

            orderDetailData = new OrderDetailData();
            orderDetailData.setGlobalOrderId(globalOrderId);
            orderDetailData.setOrderNo(orderBase.getOrderNo());
            orderDetailData.setBusinessLineId(orderBase.getBussinessId().intValue());
            orderDetailData.setOrderCategoryId(Long.valueOf(orderBase.getCategoryId()));
            orderDetailData.setAppointType(2);
            orderDetailData.setAccountType(AccountType.ENTERPRISE.code);
            orderDetailData.setAccountId(orderBase.getEnterpriseId());
            orderDetailData.setEnterpriseOrderId(orderBase.getOrderId());
            orderDetailData.setLv3ServeIds(orderBase.getServeIds());
            orderDetailData.setThirdDivisionId(orderBase.getThirdDivisionId());
            orderDetailData.setFourthDivisionId(orderBase.getFourthDivisionId());
            orderDetailData.setOrderCreateTime(orderBase.getCreateTime());
            Long timeStamp = System.currentTimeMillis();
            String orderVersion = String.valueOf(timeStamp);
            orderDetailData.setOrderVersion(orderVersion);
            Long cityDivisionId = addressCommon.getCityDivisionIdByDivisionId(orderBase.getThirdDivisionId());
            orderDetailData.setSecondDivisionId(cityDivisionId);

            if (com.alibaba.excel.util.StringUtils.isNotBlank(orderBase.getServeIds())) {
                List<ServeBaseInfoResp> serveBaseInfoRespList = orderConfigCommon.getServeList(orderBase.getServeIds(), orderBase.getBussinessId().intValue());
                if (CollectionUtils.isNotEmpty(serveBaseInfoRespList)) {
                    orderDetailData.setLv1ServeIds(String.valueOf(serveBaseInfoRespList.get(0).getLevel1Id()));
                    List<Long> lv2ServeIdList = serveBaseInfoRespList.stream().map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(lv2ServeIdList)) {
                        orderDetailData.setLv2ServeIds(Joiner.on(",").join(lv2ServeIdList));
                    }

                }
            }

            orderDetailData.setLv3ServeIds(orderBase.getServeIds());


            PositionConvert positionConvert = addressCommon.getPositionConvertByDivisionId(orderBase.getThirdDivisionId(),orderExtraData.getBuyerAddress());

            if(Objects.nonNull(positionConvert) && Objects.nonNull(positionConvert.getLatitude()) && Objects.nonNull(positionConvert.getLongitude() )&&
                    positionConvert.getLatitude().compareTo(BigDecimal.ZERO) > 0 && positionConvert.getLongitude().compareTo(BigDecimal.ZERO) > 0){
                orderDetailData.setOrderLngLat(positionConvert.getLongitude().toString() + "," + positionConvert.getLatitude().toString());
            }


            orderDetailData.setUserId(AccountType.USER.code.equals(orderBase.getFromAccountType()) ? orderBase.getFromAccountId() : 0L);
            orderDetailData.getPushExtraData().setMasterSourceType(getMasterSourceType(orderDetailData));

            List<Long> specialCityList = StringUtils.isNotBlank(agreementSpecialCityList) ? Arrays.stream(agreementSpecialCityList.split(",")).map(Long::valueOf).collect(Collectors.toList()) : new ArrayList<>();
            if(specialCityList.contains(orderDetailData.getSecondDivisionId())){
                orderDetailData.setFourthDivisionId(orderDetailData.getThirdDivisionId());
            }

            String techniqueIds = bindingTechnologyCommon.getBindingTechnology(orderBase.getServeIds(),orderBase.getBussinessId(),orderBase.getOrderFrom(),orderBase.getFromAccountType());

            orderDetailData.setOrderTechniques(techniqueIds);
            Collections.addAll(orderDetailData.getOrderTechniqueSet(), orderDetailData.getOrderTechniques().split("\\|"));
            Integer emergencyStatus = orderBase.getEmergencyStatus();
            orderDetailData.setEmergencyOrderFlag((Objects.nonNull(emergencyStatus) && emergencyStatus == 2) ? 1 : 0);
            orderDetailData.setIsParts(orderExtraData.getIsAccessory());
            OrderRetAccessoriesRqt rqt = new OrderRetAccessoriesRqt();
            rqt.setOrderId(orderBase.getOrderId());
            OrderRetAccessories orderRetAccessories = orderRetAccessoriesApi.getRetAccessoriesByOrderIdOrOrderNo(rqt);
            if(Objects.nonNull(orderRetAccessories) && Objects.nonNull(orderRetAccessories.getIsNeedSendBack())){
                orderDetailData.setIsSendBackOld(orderRetAccessories.getIsNeedSendBack());
                orderDetailData.setSendBackOldPayMode(orderRetAccessories.getReturnPayMode());
            }else{
                orderDetailData.setIsSendBackOld(0);
            }
        }

        return orderDetailData;
    }



}
