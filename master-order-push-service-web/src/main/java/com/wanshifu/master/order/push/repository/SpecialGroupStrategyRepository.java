package com.wanshifu.master.order.push.repository;

import com.wanshifu.framework.persistence.base.impl.BaseRepository;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.mapper.SpecialGroupStrategyMapper;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 特殊人群策略Repository
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Repository
public class SpecialGroupStrategyRepository extends BaseRepository<SpecialGroupStrategy> {

    @Resource
    private SpecialGroupStrategyMapper specialGroupStrategyMapper;

    /**
     * 插入特殊人群策略
     */
    public int insert(String strategyName, String strategyDesc, String serveIds, String regionLevel,
                      String cityIds, String pushGroups, String pushGroupsExpression, Long serveModel,
                      Integer delayMinutes, String filterGroups, String filterGroupsExpression,
                      Long createAccountId) {
        SpecialGroupStrategy strategy = new SpecialGroupStrategy();
        strategy.setStrategyName(strategyName);
        strategy.setStrategyDesc(strategyDesc);
        strategy.setServeIds(serveIds);
        strategy.setRegionLevel(regionLevel);
        strategy.setCityIds(cityIds);
        strategy.setPushGroups(pushGroups);
        strategy.setPushGroupsExpression(pushGroupsExpression);
        strategy.setServeModel(serveModel);
        strategy.setDelayMinutes(delayMinutes);
        strategy.setFilterGroups(filterGroups);
        strategy.setFilterGroupsExpression(filterGroupsExpression);
        strategy.setStrategyStatus(0); // 默认禁用
        strategy.setCreateAccountId(createAccountId);
        strategy.setUpdateAccountId(createAccountId);
        return this.insertSelective(strategy);
    }

    /**
     * 更新特殊人群策略
     */
    public int update(Integer strategyId, String strategyName, String strategyDesc, String serveIds,
                      String regionLevel, String cityIds, String pushGroups, String pushGroupsExpression,
                      Long serveModel, Integer delayMinutes, String filterGroups, String filterGroupsExpression,
                      Long updateAccountId) {
        SpecialGroupStrategy strategy = new SpecialGroupStrategy();
        strategy.setStrategyId(strategyId);
        strategy.setStrategyName(strategyName);
        strategy.setStrategyDesc(strategyDesc);
        strategy.setServeIds(serveIds);
        strategy.setRegionLevel(regionLevel);
        strategy.setCityIds(cityIds);
        strategy.setPushGroups(pushGroups);
        strategy.setPushGroupsExpression(pushGroupsExpression);
        strategy.setServeModel(serveModel);
        strategy.setDelayMinutes(delayMinutes);
        strategy.setFilterGroups(filterGroups);
        strategy.setFilterGroupsExpression(filterGroupsExpression);
        strategy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(strategy);
    }

    /**
     * 更新策略状态
     */
    public int updateStatus(Integer strategyId, Integer strategyStatus, Long updateAccountId) {
        SpecialGroupStrategy strategy = new SpecialGroupStrategy();
        strategy.setStrategyId(strategyId);
        strategy.setStrategyStatus(strategyStatus);
        strategy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(strategy);
    }

    /**
     * 查询策略列表
     */
    public List<SpecialGroupStrategy> selectList(String strategyName, Long cityId, String serveIds,
                                                 Date createStartTime, Date createEndTime, Integer strategyStatus) {
        List<Long> serveIdList = null;
        if (StringUtils.isNotBlank(serveIds)) {
            serveIdList = Arrays.stream(serveIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        return specialGroupStrategyMapper.selectList(strategyName, cityId, serveIdList, createStartTime, createEndTime, strategyStatus);
    }

    /**
     * 软删除策略
     */
    public int softDelete(Integer strategyId) {
        SpecialGroupStrategy strategy = new SpecialGroupStrategy();
        strategy.setStrategyId(strategyId);
        strategy.setIsDelete(1);
        return this.updateByPrimaryKeySelective(strategy);
    }

    /**
     * 软删除策略（带更新人ID）
     */
    public int softDeleteWithUpdateAccountId(Integer strategyId, Long updateAccountId) {
        SpecialGroupStrategy strategy = new SpecialGroupStrategy();
        strategy.setStrategyId(strategyId);
        strategy.setIsDelete(1);
        strategy.setUpdateAccountId(updateAccountId);
        return this.updateByPrimaryKeySelective(strategy);
    }

    /**
     * 根据策略名称查询策略（用于重名校验）
     */
    public SpecialGroupStrategy selectByStrategyName(String strategyName, Integer strategyId) {
        Example example = new Example(SpecialGroupStrategy.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("strategyName", strategyName)
                .andEqualTo("isDelete", 0);
        if (strategyId != null) {
            criteria.andNotEqualTo("strategyId", strategyId);
        }
        return CollectionUtils.getFirstSafety(this.selectByExample(example));
    }

    /**
     * 根据城市和服务查询策略
     */
    public SpecialGroupStrategy selectByCityAndServe(String cityId, String serveId) {
        return CollectionUtils.getFirstSafety(specialGroupStrategyMapper.selectByCityAndServe(cityId, serveId));
    }
}
