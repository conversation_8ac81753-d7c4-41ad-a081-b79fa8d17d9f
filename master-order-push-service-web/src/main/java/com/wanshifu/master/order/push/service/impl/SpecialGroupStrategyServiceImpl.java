package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.lang.Assert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.annotation.FeishuNotice;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.resp.baseSelectStrategy.ListResp;
import com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy.*;
import com.wanshifu.master.order.push.mapper.SpecialGroupStrategyMapper;
import com.wanshifu.master.order.push.repository.SpecialGroupStrategyRepository;
import com.wanshifu.master.order.push.service.SpecialGroupStrategyService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 特殊人群策略服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Service
public class SpecialGroupStrategyServiceImpl implements SpecialGroupStrategyService {

    @Resource
    private SpecialGroupStrategyRepository specialGroupStrategyRepository;

    @Override
    @FeishuNotice(methodTypeName = "insert", level1MenuName = "推单设置", level2MenuName = "特殊人群策略",
            createAccountIdFieldName = "createAccountId",
            configNameFieldName = "strategyName")
    public int create(CreateRqt rqt) {
        // 校验策略名称是否重复
        this.checkStrategyName(rqt.getStrategyName(), null);
        
        return specialGroupStrategyRepository.insert(
                rqt.getStrategyName(),
                rqt.getStrategyDesc(),
                rqt.getServeIds(),
                rqt.getRegionLevel(),
                rqt.getCityIds(),
                rqt.getPushGroups(),
                rqt.getPushGroupsExpression(),
                rqt.getServeModel(),
                rqt.getDelayMinutes(),
                rqt.getFilterGroups(),
                rqt.getFilterGroupsExpression(),
                rqt.getCreateAccountId()
        );
    }

    @Override
    @FeishuNotice(methodTypeName = "update", level1MenuName = "推单设置", level2MenuName = "特殊人群策略",
            tableName = "special_group_strategy", mapperClass = SpecialGroupStrategyMapper.class,
            mapperBeanName = "specialGroupStrategyMapper", primaryKeyFieldName = "strategyId",
            updateAccountIdFieldName = "updateAccountId",
            configNameFieldNameFromEntity = "strategyName")
    public int update(UpdateRqt rqt) {
        // 校验策略名称是否重复
        this.checkStrategyName(rqt.getStrategyName(), rqt.getStrategyId());
        
        return specialGroupStrategyRepository.update(
                rqt.getStrategyId(),
                rqt.getStrategyName(),
                rqt.getStrategyDesc(),
                rqt.getServeIds(),
                rqt.getRegionLevel(),
                rqt.getCityIds(),
                rqt.getPushGroups(),
                rqt.getPushGroupsExpression(),
                rqt.getServeModel(),
                rqt.getDelayMinutes(),
                rqt.getFilterGroups(),
                rqt.getFilterGroupsExpression(),
                rqt.getUpdateAccountId()
        );
    }

    @Override
    public SpecialGroupStrategy detail(DetailRqt rqt) {
        return specialGroupStrategyRepository.selectByPrimaryKey(rqt.getStrategyId());
    }

    @Override
    public SimplePageInfo<SpecialGroupStrategy> list(ListRqt rqt) {
        Integer pageNum = rqt.getPageNum();
        Integer pageSize = rqt.getPageSize();
        String strategyName = rqt.getStrategyName();
        Long cityId = rqt.getCityId();
        String serveIds = rqt.getServeIds();
        Integer strategyStatus = rqt.getStrategyStatus();

        Page<ListResp> startPage = PageHelper.startPage(pageNum, pageSize);
        List<SpecialGroupStrategy> strategyList = specialGroupStrategyRepository.selectList(
                strategyName, cityId, serveIds, rqt.getCreateStartTime(), rqt.getCreateEndTime(), strategyStatus);

        SimplePageInfo<SpecialGroupStrategy> pageInfo = new SimplePageInfo<>();
        pageInfo.setPages(startPage.getPages());
        pageInfo.setPageNum(startPage.getPageNum());
        pageInfo.setTotal(startPage.getTotal());
        pageInfo.setPageSize(startPage.getPageSize());
        pageInfo.setList(strategyList);
        return pageInfo;
    }

    @Override
    @FeishuNotice(methodTypeName = "enable", level1MenuName = "推单设置", level2MenuName = "特殊人群策略",
            tableName = "special_group_strategy", mapperClass = SpecialGroupStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "specialGroupStrategyMapper", primaryKeyFieldName = "strategyId",
            configNameFieldNameFromEntity = "strategyName")
    public Integer enable(EnableRqt rqt) {
        Integer strategyId = rqt.getStrategyId();
        Integer strategyStatus = rqt.getStrategyStatus();
        return specialGroupStrategyRepository.updateStatus(strategyId, strategyStatus, rqt.getUpdateAccountId());
    }

    @Override
    @FeishuNotice(methodTypeName = "delete", level1MenuName = "推单设置", level2MenuName = "特殊人群策略",
            tableName = "special_group_strategy", mapperClass = SpecialGroupStrategyMapper.class,
            updateAccountIdFieldName = "updateAccountId",
            mapperBeanName = "specialGroupStrategyMapper", primaryKeyFieldName = "strategyId",
            configNameFieldNameFromEntity = "strategyName")
    public Integer delete(DeleteRqt rqt) {
        Integer strategyId = rqt.getStrategyId();
        // 设置更新人ID
        if (rqt.getUpdateAccountId() != null) {
            return specialGroupStrategyRepository.softDeleteWithUpdateAccountId(strategyId, rqt.getUpdateAccountId());
        }
        return specialGroupStrategyRepository.softDelete(strategyId);
    }

    /**
     * 校验策略名称是否重复
     */
    private void checkStrategyName(String strategyName, Integer strategyId) {
        SpecialGroupStrategy strategy = specialGroupStrategyRepository.selectByStrategyName(strategyName, strategyId);
        Assert.isNull(strategy, "已存在相同策略名称!");
    }
}
