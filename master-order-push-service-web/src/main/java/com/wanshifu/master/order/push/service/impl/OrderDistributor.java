package com.wanshifu.master.order.push.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ql.util.express.DefaultContext;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.ScorerItem;
import com.wanshifu.master.order.push.domain.dto.ScorerMaster;
import com.wanshifu.master.order.push.domain.po.OrderScoringStrategy;
import com.wanshifu.master.order.push.domain.po.OrderSelectStrategy;
import com.wanshifu.util.LoCollectionsUtil;
import com.wanshifu.util.QlExpressStatic;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 订单自动分配
 * <AUTHOR>
 */
@Slf4j
public class OrderDistributor {

    /**
     * 特征
     */
    private Set<String> admittanceOrderFeatureSet=new HashSet<>();
    private Set<String> orderFeatureSet=new HashSet<>();
    private Set<String> masterFeatureSet=new HashSet<>();
    private boolean matched=false;

    public boolean isMatched() {
        return matched;
    }

    public void setMatched(boolean matched) {
        this.matched = matched;
    }

    public Set<String> getAdmittanceOrderFeatureSet() {
        return admittanceOrderFeatureSet;
    }

    public void addAdmittanceOrderFeatureCode(String featureCode){
        admittanceOrderFeatureSet.add(featureCode);
    }

    public void addOrderFeatureCode(String featureCode){
        orderFeatureSet.add(featureCode);
    }

    public void addMasterFeatureCode(String featureCode){
        masterFeatureSet.add(featureCode);
    }

    public Set<String> getOrderFeatureSet() {
        return orderFeatureSet;
    }

    public Set<String> getMasterFeatureSet() {
        return masterFeatureSet;
    }

    public List<Filter> getFilterList() {
        return filterList;
    }

    @Data
    public static class CompensateDistributeDTO{

        private Integer compensateDistributeId;

        private Integer orderRoutingStrategyId;

        private DistributeStrategyDTO distributeStrategyList;
    }

    @Data
    public static class DistributeStrategyDTO{

        private Long orderSelectStrategyId;

        private Long orderScoringStrategyId;

        private String distributeRule;
    }

    /**
     * 筛选策略
     */
    private List<Filter> filterList = new ArrayList<>();
    /**
     * 评分策略
     */
    private List<Scorer> scorerList = new ArrayList<>();

    private List<CompensateDistributeDTO> compensateDistributeList;

    private String distributeRule;

    private Integer distributeNum;

    /**
     * 合作经营调度是否服务区域兜底
     * 1：是
     * 0：否
     */
    private Integer cooperationBusinessServeDivisionAtLast;

    public void setSelectStrategy(List<OrderSelectStrategy> orderSelectStrategies,DefaultContext<String,Object> orderFeatures) {
        orderSelectStrategies.stream().forEach(row->{
            try {
                final String selectRuleExpression = row.getSelectRuleExpression();
                final JSONObject selectRuleExpressionJSON = JSONObject.parseObject(selectRuleExpression);

                final String appointGroupParams = selectRuleExpressionJSON.getString(FieldConstant.APPOINT_GROUP_PARAMS);

                //TODO 待确认多个筛选策略，指定人群场景下如何筛选？
                if (StringUtils.isNotEmpty(appointGroupParams)) {
                    this.filterList.add(Filter.FilterBuilder.aFilter()
                            .withFilterId(String.valueOf(row.getStrategyId()))
                            .withAppointGroupExpression(selectRuleExpressionJSON.getString(FieldConstant.APPOINT_GROUP_EXPRESSION))
                            .build());
                    this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(appointGroupParams));
                }

                JSONArray selectRuleList = selectRuleExpressionJSON.getJSONArray("filterRuleList");

                for (int j = 0; j < selectRuleList.size(); j++) {
                    final JSONObject selectRule = selectRuleList.getJSONObject(j);
                    final String openConditionRuleExpression = selectRule.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
                    final boolean expressBoolean = QlExpressStatic.QlExpressBoolean(
                            openConditionRuleExpression, orderFeatures
                    );
                    if (expressBoolean) {

                        this.filterList.add(Filter.FilterBuilder.aFilter()
                                .withFilterId(String.valueOf(row.getStrategyId()))
                                .withRuleName(selectRule.getString(FieldConstant.FILTER_RULE_NAME))
                                .withExpression(selectRule.getString(FieldConstant.FILTER_RULE_EXPRESSION))
                                .build());

                        final String filterRuleParams = selectRule.getString(FieldConstant.FILTER_RULE_PARAMS);
                        this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(filterRuleParams));
                    }
                }


            }catch (Exception e){
                log.warn("setSelectStrategy exception:{},[{}]",orderSelectStrategies,e);
            }
        });
    }


    public void setSelectStrategy(List<OrderSelectStrategy> orderSelectStrategies) {
        orderSelectStrategies.stream().forEach(row->{
            try {
                final String selectStrategyExpression = row.getSelectStrategyExpression();
                final JSONObject selectStrategyExpressionJSON = JSONObject.parseObject(selectStrategyExpression);
                this.filterList.add(Filter.FilterBuilder.aFilter()
                        .withFilterId(String.valueOf(row.getStrategyId()))
                        .withAppointGroupExpression(selectStrategyExpressionJSON.getString(FieldConstant.APPOINT_GROUP_EXPRESSION))
                        .withExpression(selectStrategyExpressionJSON.getString(FieldConstant.SELECT_RULE_EXPRESSION))
                        .build());
                final String selectRuleParams = selectStrategyExpressionJSON.getString(FieldConstant.SELECT_RULE_PARAMS);
                final String appointGroupParams = selectStrategyExpressionJSON.getString(FieldConstant.APPOINT_GROUP_PARAMS);
                if (StringUtils.isNotEmpty(selectRuleParams)) {
                    this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(selectRuleParams));
                }
                if (StringUtils.isNotEmpty(appointGroupParams)) {
                    this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(appointGroupParams));
                }
            }catch (Exception e){
                log.warn("setSelectStrategy exception:{},[{}]",orderSelectStrategies,e);
            }
        });
    }

    public void setScoringStrategy(List<OrderScoringStrategy> orderScoringStrategies) {
        try {
            for (OrderScoringStrategy orderScoringStrategy : orderScoringStrategies) {
                final String scoringStrategyExpression = orderScoringStrategy.getScoringStrategyExpression();
                final JSONArray objects = JSONObject.parseArray(scoringStrategyExpression);
                for (int i = 0; i < objects.size(); i++) {
                    final JSONObject outerRow = objects.getJSONObject(i);
                    final String outerOpenConditionRuleExpression =outerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
                    final String outerOpenConditionRuleParams = outerRow.getString(FieldConstant.OPEN_CONDITION_RULE_PARAMS);
                    this.orderFeatureSet.addAll(LoCollectionsUtil.stringToSet(outerOpenConditionRuleParams));
                    final JSONArray scoreRuleList = outerRow.getJSONArray(FieldConstant.SCORE_RULE_LIST);
                    List<ScorerItem> scorerItemList=new ArrayList<>();
                    for (int j = 0; j < scoreRuleList.size(); j++) {
                        final JSONObject innerRow = scoreRuleList.getJSONObject(j);
                        final String openConditionRuleExpression = innerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
                        final String openConditionRuleParams = innerRow.getString(FieldConstant.OPEN_CONDITION_RULE_PARAMS);
                        final String ruleName = innerRow.getString(FieldConstant.RULE_NAME);
                        this.orderFeatureSet.addAll(LoCollectionsUtil.stringToSet(openConditionRuleParams));
                        final String scoreRuleParams = innerRow.getString(FieldConstant.SCORE_RULE_PARAMS);
                        this.masterFeatureSet.addAll(LoCollectionsUtil.stringToSet(scoreRuleParams));
                        final String scoreRuleExpression=innerRow.getString(FieldConstant.SCORE_RULE_EXPRESSION);
                        scorerItemList.add(
                                ScorerItem.ScorerItemBuilder.aScorerItem()
                                        .withRuleName(ruleName)
                                        .withAdmittance(openConditionRuleExpression)
                                        .withScoreRuleExpression(scoreRuleExpression)
                                        .build()
                        );
                    }
                    this.scorerList.add(
                            Scorer.ScorerBuilder.aScorer()
                                    .withAdmittance(outerOpenConditionRuleExpression)
                                    .withScorerId(String.valueOf(orderScoringStrategy.getStrategyId()))
                                    .withScorerItemList(scorerItemList)
                                    .build()
                    );
                }
            }
        }catch (Exception e){
            log.warn("OrderDistributor withScoringStrategy exception:[{}],[{}]",orderScoringStrategies,e);
        }
    }

    /**
     * 处理
     * @return
     */
    public List<ScorerMaster> rank(Set<String> masterSet, DefaultContext<String, Object> orderFeatures,
                                   DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
                                   RankDetail rankDetail){
        List<ScorerMaster> scorerMasterList=new ArrayList<>();

        for (String masterId : masterSet) {
            final DefaultContext<String, Object> masterContext = masterFeatures.get(masterId);
            /**
             * 过滤
             */
            if (filter(masterId,masterSet,masterContext,rankDetail)) {
                log.info("selectStrategyFilter,orderId:{},masterInfo:{}", orderFeatures.get(FieldConstant.MASTER_ORDER_ID), JSON.toJSONString(masterContext));
                continue;
            }    
            /**
             * 评分
             */
            scorer(masterId,scorerMasterList,orderFeatures,masterContext,rankDetail);
        }
        return scorerMasterList;
    }



    /**
     * 处理
     * @return
     */
    public Set<String> filter(Set<String> masterSet,
                                   DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
                                   RankDetail rankDetail){
        Set<String> finalMasterSet = new HashSet<>();

        for (String masterId : masterSet) {

            final DefaultContext<String, Object> masterContext = masterFeatures.get(masterId);
            /**
             * 过滤
             */
            if (filter(masterId,masterSet,masterContext,rankDetail)) {
                continue;
            }

            finalMasterSet.add(masterId);


        }
        return finalMasterSet;
    }


    /**
     * 处理
     * @return
     */
    public List<ScorerMaster> score(Set<String> masterSet, DefaultContext<String, Object> orderFeatures,
                                   DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
                                   RankDetail rankDetail){
        List<ScorerMaster> scorerMasterList=new ArrayList<>();

        for (String masterId : masterSet) {
            final DefaultContext<String, Object> masterContext = masterFeatures.get(masterId);
            scorer(masterId,scorerMasterList,orderFeatures,masterContext,rankDetail);
        }
        return scorerMasterList;
    }




    /**
     * 过滤
     * @param masterContext
     */
    private boolean filter(String masterId,Set<String> masterSet,DefaultContext<String, Object> masterContext,
                           RankDetail rankDetail){
        boolean result=false;
        if (CollectionUtils.isEmpty(filterList)) {
            return false;
        }
        for (Filter filter : filterList) {
            log.info("masterContext"+ JSON.toJSONString(masterContext));
            if(StringUtils.isNotBlank(filter.getExpression())){
                result=QlExpressStatic.QlExpressBoolean(filter.getExpression(),masterContext);
                if (result) {
                    rankDetail.addFilterDetail(filter.getFilterId(),filter.getRuleName(),masterId);
                    break;
                }
            }

            if (StringUtils.isNotEmpty(filter.getAppointGroupExpression())) {
                boolean groupResult=QlExpressStatic.QlExpressBoolean(filter.getAppointGroupExpression()
                        ,masterContext);
                if (!groupResult) {
//                    masterSet.remove(masterId);
                    rankDetail.addFilterDetail(filter.getFilterId(), filter.getRuleName(), masterId);
                    result=true;
                    break;
                }
            }
        }
        return result;
    }


//    /**
//     * 过滤
//     * @param masterContext
//     */
//    private boolean filter(String masterId,Set<String> masterSet,DefaultContext<String, Object> masterContext,
//                           RankDetail rankDetail){
//        boolean result=false;
//        if (selectStrategy==null) {
//            return false;
//        }
//        for (Filter selectExpression : selectStrategy) {
//            log.info("masterContext"+ JSON.toJSONString(masterContext));
//            result=QlExpressStatic.QlExpressBoolean(selectExpression.getExpression(),masterContext);
//            if (result) {
////                masterSet.remove(masterId);
//                rankDetail.addFilterDetail(selectExpression.getFilterId(),masterId);
//                continue;
//            }
//            if (StringUtils.isNotEmpty(selectExpression.getAppointGroupExpression())) {
//                boolean groupResult=QlExpressStatic.QlExpressBoolean(selectExpression.getAppointGroupExpression()
//                        ,masterContext);
//                if (!groupResult) {
////                    masterSet.remove(masterId);
//                    rankDetail.addFilterDetail(selectExpression.getFilterId(),masterId);
//                    result=true;
//                }
//            }
//        }
//        return result;
//    }
    /**
     * 评分
     * @param masterId
     * @param scorerMaster
     * @param masterContext
     */
    private void scorer(String masterId,List<ScorerMaster> scorerMaster,
                        DefaultContext<String, Object> orderFeatures,
                        DefaultContext<String, Object> masterContext,
                        RankDetail rankDetail){
        if (CollectionUtils.isEmpty(scorerList)) {
            scorerMaster.add(ScorerMaster.ScorerMasterBuilder.aScorerMaster()
                    .withMasterId(masterId)
                    .build());
            return;
        }
        BigDecimal score=BigDecimal.ZERO;
        for (Scorer scorer : scorerList) {
            score=score.add(scorer.mark(orderFeatures,masterContext,rankDetail,masterId));
        }
        final ScorerMaster build = ScorerMaster.ScorerMasterBuilder.aScorerMaster()
                .withMasterId(masterId)
                .withScore(score)
                .build();
        scorerMaster.add(build);
    }


    public List<Filter> getSelectStrategy() {
        return filterList;
    }

    public List<Scorer> getScoringStrategy() {
        return scorerList;
    }


    public static final class OrderDistributorBuilder {
        private List<Filter> filterList=new ArrayList<>();
        private List<Scorer> scoringList=new ArrayList<>();

        private OrderDistributorBuilder() {
        }

        public static OrderDistributorBuilder anOrderDistributor() {
            return new OrderDistributorBuilder();
        }

        public OrderDistributorBuilder withSelectStrategy(List<OrderSelectStrategy> orderSelectStrategies) {
            orderSelectStrategies.stream().forEach(row->{
                try {
                    final String selectStrategyExpression = row.getSelectStrategyExpression();
                    final JSONObject selectStrategyExpressionJSON = JSONObject.parseObject(selectStrategyExpression);
                    this.filterList.add(
                            Filter.FilterBuilder.aFilter()
                                    .withFilterId(String.valueOf(row.getStrategyId()))
                                    .withExpression(selectStrategyExpressionJSON.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION))
                                    .build()
                            );
                }catch (Exception e){
                }
            });
            return this;
        }

        public OrderDistributorBuilder withScoringStrategy(List<OrderScoringStrategy> orderScoringStrategies) {
            try {
                for (OrderScoringStrategy orderScoringStrategy : orderScoringStrategies) {
                    final String scoringStrategyExpression = orderScoringStrategy.getScoringStrategyExpression();
                    final JSONArray objects = JSONObject.parseArray(scoringStrategyExpression);
                    for (int i = 0; i < objects.size(); i++) {
                        final JSONObject outerRow = objects.getJSONObject(i);
                        final String outerOpenConditionRuleExpression =outerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
                        final JSONArray scoreRuleList = outerRow.getJSONArray(FieldConstant.SCORE_RULE_LIST);
                        List<ScorerItem> scorerItemList=new ArrayList<>();
                        for (int j = 0; j < scoreRuleList.size(); j++) {
                            final JSONObject innerRow = scoreRuleList.getJSONObject(j);
                            final String openConditionRuleExpression = innerRow.getString(FieldConstant.OPEN_CONDITION_RULE_EXPRESSION);
                            final String scoreRuleExpression=innerRow.getString(FieldConstant.SCORE_RULE_EXPRESSION);
                            scorerItemList.add(
                                    ScorerItem.ScorerItemBuilder.aScorerItem()
                                    .withAdmittance(openConditionRuleExpression)
                                    .withScoreRuleExpression(scoreRuleExpression)
                                    .build()
                            );
                        }
                        this.scoringList.add(
                                Scorer.ScorerBuilder.aScorer()
                                .withAdmittance(outerOpenConditionRuleExpression)
                                .withScorerItemList(scorerItemList)
                                .build()
                        );
                    }
                }
            }catch (Exception e){
                log.warn("OrderDistributor withScoringStrategy exception:[{}],[{}]",orderScoringStrategies,e);
            }
            return this;
        }

        public OrderDistributor build() {
            OrderDistributor orderDistributor = new OrderDistributor();
            orderDistributor.filterList = this.filterList;
            orderDistributor.scorerList = this.scoringList;
            return orderDistributor;
        }
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

    public String getDistributeRule() {
        return distributeRule;
    }

    public void setDistributeRule(String distributeRule) {
        this.distributeRule = distributeRule;
    }

    public Integer getCooperationBusinessServeDivisionAtLast() {
        return cooperationBusinessServeDivisionAtLast;
    }

    public void setCooperationBusinessServeDivisionAtLast(Integer cooperationBusinessServeDivisionAtLast) {
        this.cooperationBusinessServeDivisionAtLast = cooperationBusinessServeDivisionAtLast;
    }

    public Integer getDistributeNum() {
        return distributeNum;
    }

    public void setDistributeNum(Integer distributeNum) {
        this.distributeNum = distributeNum;
    }

    public void setCompensateDistributeList(List<CompensateDistributeDTO> compensateDistributeList) {
        this.compensateDistributeList = compensateDistributeList;
    }

    public List<CompensateDistributeDTO> getCompensateDistributeList() {
        return this.compensateDistributeList;
    }
}
