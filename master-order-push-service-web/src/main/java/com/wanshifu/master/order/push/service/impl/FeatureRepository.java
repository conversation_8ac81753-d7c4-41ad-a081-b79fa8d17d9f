package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ql.util.express.DefaultContext;
import com.wanshifu.base.address.domain.po.PositionConvert;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.api.MasterRestrictOrderServeApi;
import com.wanshifu.master.information.domain.api.request.common.GetMasterInfoListByIdsRqt;
import com.wanshifu.master.information.domain.api.request.masterRestrict.BatchGetMasterRestrictRqt;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoListByIdsResp;
import com.wanshifu.master.information.domain.api.response.masterRestrict.MasterOrderServeRestrictResp;
import com.wanshifu.master.order.domains.api.request.common.GetReserveCustomerPeriodRqt;
import com.wanshifu.master.order.domains.api.response.common.GetReserveCustomerPeriodMasterResp;
import com.wanshifu.master.order.push.api.BigdataOpenServiceApi;
import com.wanshifu.master.order.push.domain.api.response.AllTimeOrderMstStatResp;
import com.wanshifu.master.order.push.domain.api.response.GetMstCooperativeBusinessOrderStatResp;
import com.wanshifu.master.order.push.domain.api.response.GetMstLast1mServingCntByMstIdsResp;
import com.wanshifu.master.order.push.domain.api.rqt.AllTimeOrderMstStatRqt;
import com.wanshifu.master.order.push.domain.api.rqt.GetMstCooperativeBusinessOrderStatRqt;
import com.wanshifu.master.order.push.domain.api.rqt.GetMstLast1mServingCntByMstIdsRqt;
import com.wanshifu.master.order.push.domain.common.BaseFeatureConfigDimensionList;
import com.wanshifu.master.order.push.domain.common.PushCommonObject;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.constant.SymbolConstant;
import com.wanshifu.master.order.push.domain.dto.AgreementMasterBase;
import com.wanshifu.master.order.push.domain.dto.AgreementMasterExtra;
import com.wanshifu.master.order.push.domain.dto.EsResponse;
import com.wanshifu.master.order.push.domain.dto.Pageable;
import com.wanshifu.master.order.push.domain.enums.FeatureMapKey;
import com.wanshifu.master.order.push.domain.enums.MasterSourceType;
import com.wanshifu.master.order.push.domain.es.MasterBaseSearch;
import com.wanshifu.master.order.push.domain.po.AgreementMaster;
import com.wanshifu.master.order.push.domain.po.BaseFeature;
import com.wanshifu.master.order.push.domain.po.ComplexFeature;
import com.wanshifu.master.order.push.domain.po.OrderServeInfo;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.repository.BaseFeatureRepository;
import com.wanshifu.master.order.push.repository.ComplexFeatureRepository;
import com.wanshifu.master.order.push.service.FeatureQueryServiceImpl;
import com.wanshifu.master.order.push.service.HBaseClient;
import com.wanshifu.master.order.push.service.QLExpressHandler;
import com.wanshifu.master.order.push.util.*;
import com.wanshifu.master.order.service.api.OrderCommonServiceApi;
import com.wanshifu.master.recruit.api.RecruitMasterApi;
import com.wanshifu.master.recruit.domain.api.request.recruitMaster.GetMasterCurrentLeaveRequestRqt;
import com.wanshifu.master.recruit.domain.api.response.recruitMaster.GetMasterCurrentLeaveRequestResp;
import com.wanshifu.util.*;
import com.wanshifu.util.QlExpressStatic;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.util.Bytes;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class FeatureRepository {



    @Resource
    BaseFeatureRepository baseFeatureRepository;

    @Resource
    ComplexFeatureRepository complexFeatureRepository;

    @Resource
    MasterRestrictOrderServeApi masterRestrictOrderServeApi;

    @Resource
    OrderCommonServiceApi orderCommonServiceApi;

    @Resource
    private HBaseClient hBaseClient;


    
    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;

    @Resource
    private OrderServeInfoEsRepository orderServeInfoEsRepository;


    @Resource
    private AddressCommon addressCommon;

    @Resource
    private FeatureQueryServiceImpl featureQueryServiceImpl;


    @Resource
    private BigdataOpenServiceApi bigdataOpenServiceApi;

    @Resource
    private NavigationalDistanceCalculateService navigationalDistanceCalculateService;


    @Resource
    private Tools tools;


    @Value("${allTimeMaster.batchSize:100}")
    private Integer allTimeMasterBatchSize;


    public DefaultContext<String, DefaultContext<String, Object>> masterFeatures(
            Set<String> masterSet
            ,Set<String> featureSet
            ,DefaultContext<String, Object> orderFeatures
    ){
        DefaultContext<String, DefaultContext<String, Object>> masterFeatures= new DefaultContext<String, DefaultContext<String, Object>>();
        if (featureSet.size()!=0) {
            List<ComplexFeature> complexFeatures = complexFeatureRepository.selectByFeatureFor(FieldConstant.MASTER, featureSet);
            if (CollectionUtils.isNotEmpty(complexFeatures)) {
                addFeatureDependency(complexFeatures,featureSet);
            }
            final List<BaseFeature> baseFeatures =
                    baseFeatureRepository.selectByFeatureType(FieldConstant.MASTER, featureSet);
            masterFeatures=getMasterFeature(masterSet,baseFeatures,complexFeatures,orderFeatures);
            getInterfaceFeatureBatch(masterSet,complexFeatures,orderFeatures,masterFeatures);
        }else {
            for (String master : masterSet) {
                masterFeatures.put(master,new DefaultContext<>());
            }
        }

        if(featureSet.contains("serve_time_conflict")){
            getServeTimeConflict(orderFeatures,masterSet,masterFeatures);
        }

        return masterFeatures;
    }


    /**
     * 代理商特征
     * @return
     */
    public DefaultContext<String, DefaultContext<String, Object>> agentFeatures(
            Set<String> agentSet
            ,Set<String> featureSet
            ,DefaultContext<String, Object> orderFeatures
    ){
        DefaultContext<String, DefaultContext<String, Object>> agentFeatures= new DefaultContext<>();
        if (featureSet.size()!=0) {
            List<ComplexFeature> complexFeatures = complexFeatureRepository.selectByFeatureFor(FieldConstant.AGENT, featureSet);
            if (CollectionUtils.isNotEmpty(complexFeatures)) {
                addFeatureDependency(complexFeatures,featureSet);
            }
            final List<BaseFeature> baseFeatures =
                    baseFeatureRepository.selectByFeatureType(FieldConstant.AGENT, featureSet);
            agentFeatures= getAgentFeature(agentSet,baseFeatures,complexFeatures,orderFeatures);
            getInterfaceFeatureBatch(agentSet,complexFeatures,orderFeatures,agentFeatures);
        }else {
            for (String master : agentSet) {
                agentFeatures.put(master,new DefaultContext<>());
            }
        }
        return agentFeatures;
    }

    private DefaultContext<String, DefaultContext<String, Object>> getAgentFeature(
            Set<String> masterSet
            ,List<BaseFeature> baseFeatures
            ,List<ComplexFeature> complexFeatures
            ,DefaultContext<String, Object> orderFeatures
    ){
        /**
         * 获取基础特征
         */
        final DefaultContext<String, DefaultContext<String, Object>> masterBaseFeature
                = getAgentBaseFeatureByConfig(masterSet, baseFeatures, orderFeatures);
        /**
         * 获取复杂特征
         */
        if (complexFeatures!=null) {
            getComplexFeatureByConfig(complexFeatures,orderFeatures,masterBaseFeature);
        }
        return masterBaseFeature;
    }

    private DefaultContext<String, DefaultContext<String, Object>> getAgentBaseFeatureByConfig(
            Set<String> masterSet
            ,List<BaseFeature> baseFeatures
            ,DefaultContext<String, Object> orderFeatures){
        return getCollectionBaseFeatureByConfig(masterSet,baseFeatures,orderFeatures,FeatureMapKey.AGENT);
    }

    private void getServeTimeConflict(DefaultContext<String, Object> orderFeatures, Set<String> masterSet,DefaultContext<String, DefaultContext<String, Object>> masterFeatures){

        try{

            String expectDoorInStartTimeStr = (String)orderFeatures.get(FieldConstant.EXPECT_DOOR_IN_START_DATE);
            String expectDoorInEndTimeStr = (String)orderFeatures.get(FieldConstant.EXPECT_DOOR_IN_END_DATE);

            if(StringUtils.isBlank(expectDoorInStartTimeStr) || StringUtils.isBlank(expectDoorInEndTimeStr)){
                masterSet.forEach(masterId -> {
                    DefaultContext<String, Object> featureContext = masterFeatures.get(masterId);
                    if(featureContext != null){
                        featureContext.put("serve_time_conflict", 0);
                    }
                });
                return ;
            }



            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termsQuery("masterId",masterSet));
            boolQuery.must(QueryBuilders.termsQuery("nextServeNode", "serve_sign_position","reserve_customer","serve_complete"));
            boolQuery.must(QueryBuilders.termQuery("serveStatus", "serving"));



            Long expectDoorInStartTime = DateUtils.parseDate(expectDoorInStartTimeStr).getTime();
            Long expectDoorInEndTime = DateUtils.parseDate(expectDoorInEndTimeStr).getTime();


            BoolQueryBuilder busyTimeQueryBuilder = QueryBuilders.boolQuery();
            busyTimeQueryBuilder.should(QueryBuilders.rangeQuery("busyStartTime").gte(expectDoorInStartTime).lte(expectDoorInEndTime));
            busyTimeQueryBuilder.should(QueryBuilders.rangeQuery("busyEndTime").gte(expectDoorInStartTime).lte(expectDoorInEndTime));

            BoolQueryBuilder busyQueryBuilder =  QueryBuilders.boolQuery();
            busyQueryBuilder.must(QueryBuilders.rangeQuery("busyStartTime").lte(expectDoorInStartTime));
            busyQueryBuilder.must(QueryBuilders.rangeQuery("busyEndTime").gte(expectDoorInEndTime));

            busyTimeQueryBuilder.should(busyQueryBuilder);

            busyTimeQueryBuilder.minimumShouldMatch(1);

            boolQuery.must(busyTimeQueryBuilder);

            List<OrderServeInfo> orderServeInfoList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 200;
            while(true){
                EsResponse<OrderServeInfo> esResponse = orderServeInfoEsRepository.search(boolQuery,new Pageable(pageNum,pageSize),null);
                log.info("boolQueryBuilder:"+boolQuery.toString());
                if(Objects.nonNull(esResponse) && com.wanshifu.framework.utils.CollectionUtils.isNotEmpty(esResponse.getDataList())){
                    orderServeInfoList.addAll(esResponse.getDataList());
                    pageNum++;
                }else{
                    break;
                }
            }

            Set<String> serveTimeConflictMasterSet = orderServeInfoList.stream().map(OrderServeInfo::getMasterId).map(String::valueOf).collect(Collectors.toSet());

            masterSet.forEach(masterId -> {
                DefaultContext<String, Object> featureContext = masterFeatures.get(masterId);
                if(featureContext != null){
                    featureContext.put("serve_time_conflict",serveTimeConflictMasterSet.contains(masterId) ? 1 : 0);
                }
            });
        }catch(Exception e){
            log.info("getServeTimeConflict error",e);
        }



    }



    private void getWaitDoorInOrderDistance(DefaultContext<String,Object> orderFeatures,Set<String> masterSet,DefaultContext<String, DefaultContext<String, Object>> masterFeatures){

        try{


            String orderLngLat = (String)orderFeatures.get(FieldConstant.ORDER_LNG_LAT);
            String[] latLngArray = orderLngLat.split(",");

            List<List<String>> masterIdsPartition = Lists.partition(new ArrayList<>(masterSet), 200);
            masterIdsPartition.forEach(masterIdList -> {

                List<OrderServeInfo> totalOrderServeInfoList = new ArrayList<>();
                int pageNum = 1;
                int pageSize = 200;

                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                boolQueryBuilder.must(QueryBuilders.termsQuery("masterId",masterIdList));
                boolQueryBuilder.must(QueryBuilders.termQuery("nextServeNode", "serve_sign_position"));
                boolQueryBuilder.must(QueryBuilders.termQuery("serveStatus", "serving"));

                while(true){
                    EsResponse<OrderServeInfo> esResponse = orderServeInfoEsRepository.search(boolQueryBuilder,new Pageable(pageNum,pageSize),null);
                    if(Objects.nonNull(esResponse) && com.wanshifu.framework.utils.CollectionUtils.isNotEmpty(esResponse.getDataList())){
                        totalOrderServeInfoList.addAll(esResponse.getDataList());
                        pageNum++;
                    }else{
                        break;
                    }
                }


                if(CollectionUtils.isEmpty(totalOrderServeInfoList)){
                    return ;
                }

                Map<Long,List<OrderServeInfo>> orderServeInfoMap = totalOrderServeInfoList.stream().collect(Collectors.groupingBy(orderServeInfo -> orderServeInfo.getMasterId()));

                Map<Long,Double> distanceMap = new HashMap<>(orderServeInfoMap.size());

                orderServeInfoMap.keySet().forEach(masterId -> {
                    List<OrderServeInfo> orderServeInfoList = orderServeInfoMap.get(masterId);
                    if(CollectionUtils.isNotEmpty(orderServeInfoList)){
                        orderServeInfoList.forEach(orderServeInfo -> {
                            if(StringUtils.isNotBlank(orderServeInfo.getOrderLatLng())){
                                String[] orderLatLngArray = orderLngLat.split(",");
                                Long distance = DistanceUtil.distance(Double.valueOf(orderLatLngArray[1]),Double.valueOf(orderLatLngArray[0]),
                                        Double.valueOf(latLngArray[1]),Double.valueOf(latLngArray[0]));
                                if(!distanceMap.containsKey(orderServeInfo.getMasterId())){
                                    distanceMap.put(orderServeInfo.getMasterId(),Double.valueOf(distance));
                                }else if(distanceMap.get(orderServeInfo.getMasterId()) > distance){
                                    distanceMap.put(orderServeInfo.getMasterId(),Double.valueOf(distance));
                                }
                            }
                        });


                    }
                });


                masterIdList.forEach(masterId -> {
                    DefaultContext<String, Object> featureContext = masterFeatures.get(masterId);
                    if(featureContext != null){
                        featureContext.put("wait_door_in_order_distance",distanceMap.getOrDefault(Long.valueOf(masterId),Double.valueOf(9999)));
                    }
                });

            });

        }catch(Exception e){
            log.info("getWaitDoorInMinDistance error",e);
        }


        masterSet.forEach(masterId -> {
            if(!masterFeatures.get(masterId).containsKey("wait_door_in_order_distance")){
                masterFeatures.get(masterId).put("wait_door_in_order_distance",Double.valueOf(9999));
            }
        });


    }





    private void realTimeInServiceCnt(
            Set<String> masterSet,
            DefaultContext<String, Object> orderFeatures,
            DefaultContext<String, DefaultContext<String, Object>> masterFeatures){
        try {
            GetMstLast1mServingCntByMstIdsRqt rqt = new GetMstLast1mServingCntByMstIdsRqt();
            rqt.setMasterIds(masterSet);
            List<GetMstLast1mServingCntByMstIdsResp> respList = bigdataOpenServiceApi.getMstLast1mServingCntByMstIds(rqt);
            if(CollectionUtils.isNotEmpty(respList)){
                respList.forEach(resp -> {
                    LoCollectionsUtil.putToMapValue(
                            masterFeatures,resp.getMasterId()
                            ,"real_time_in_service_cnt",resp.getMstLast1mServingCnt()
                    );
                });
            }

            masterSet.forEach(masterId -> {
                Object featureValue = masterFeatures.get(masterId).get("real_time_in_service_cnt");
                if(Objects.isNull(featureValue)){
                    LoCollectionsUtil.putToMapValue(
                            masterFeatures,masterId
                            ,"real_time_in_service_cnt",0);
                }

            });
        }catch (Exception e){
            log.warn("ReserveCustomerPeriodConflictMaster:{}",e);
        }
    }



    private void addFeatureDependency(List<ComplexFeature> complexFeatures,Set<String> featureSet){
        if (CollectionUtils.isNotEmpty(complexFeatures)) {
            complexFeatures.stream().forEach(row->
                    featureSet.addAll(
                                    Arrays.stream(row.getFeatureDependency().split(SymbolConstant.COMMA))
                                    .filter(f->!f.startsWith("{"))
                                    .collect(Collectors.toList()))
            );
        }
    }


    /**
     * 获取接口特征
     * @param masterSet
     * @param complexFeatures
     * @param masterFeatures
     */
    private void getInterfaceFeatureBatch(
            Set<String> masterSet,
            List<ComplexFeature> complexFeatures,
            DefaultContext<String, Object> orderFeatures,
            DefaultContext<String, DefaultContext<String, Object>> masterFeatures){

        try{
            if (complexFeatures==null) {
                return;
            }
            final Set<ComplexFeature> interfaceFeatureSet = complexFeatures.stream()
                    .filter(row -> SymbolConstant.EMPTY_STRING.equals(row.getCalculateExpression()))
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(interfaceFeatureSet)) {
                for (ComplexFeature complexFeature : interfaceFeatureSet) {
                    final String featureCode = complexFeature.getFeatureCode();
                    switch(featureCode){
                        case "forbidden_offer_price_status":
                            //暂无批量接口,只能每个师傅调用一次接口 forbiddenOfferPriceStatus
                            break;
                        case "master_time_conflict":
                            //师傅时间冲突
                            reserveCustomerPeriodConflictMaster(
                                    masterSet,
                                    orderFeatures,
                                    masterFeatures,
                                    complexFeature
                            );
                            break;
                        default:
                            break;
                    }
                }
            }
        }catch(Exception e){
            log.info("getInterfaceFeatureBatch",e);
        }

    }



    /**
     * 预约冲突师傅查询 50个师傅调用一次接口
     */
    private void reserveCustomerPeriodConflictMaster(
            Set<String> masterSet,
            DefaultContext<String, Object> orderFeatures,
            DefaultContext<String, DefaultContext<String, Object>> masterFeatures,
            ComplexFeature complexFeature
            ){
        try {
            final Object doorStart = orderFeatures.get(FieldConstant.EXPECT_DOOR_IN_START_DATE);
            final Object doorEnd = orderFeatures.get(FieldConstant.EXPECT_DOOR_IN_END_DATE);
            if (doorStart==null||doorEnd==null) {
                return;
            }
            Date startTime=(Date)doorStart ;
            Date endTime=(Date)doorEnd;

            final List<Long> reserveCustomerConflictMaster = batchGetReserveCustomerPeriod(masterSet, startTime, endTime, 50);
            for (Long masterId : reserveCustomerConflictMaster) {
                String masterIdString=String.valueOf(masterId);
                LoCollectionsUtil.putToMapValue(
                        masterFeatures,masterIdString
                        ,complexFeature.getFeatureCode(), VariableCastUtil
                                .variableCast("1",complexFeature.getFieldType())
                );
            }
        }catch (Exception e){
            log.warn("ReserveCustomerPeriodConflictMaster:{}",e);
        }
    }



    /**
     * 分批查询预约时间
     * @return
     */
    private List<Long> batchGetReserveCustomerPeriod(
            Set<String> masterSet,Date startDate,Date endDate,int batchSize){
        final ArrayList<Long> result = new ArrayList<>();

        List<Long> batchMaster = new ArrayList<>();
        int i=0;
        for (String masterInset : masterSet) {
            batchMaster.add(Long.valueOf(masterInset));
            i++;
            if (i>=batchSize) {
                final GetReserveCustomerPeriodRqt customerPeriodRqt = new GetReserveCustomerPeriodRqt();
                customerPeriodRqt.setMasterIds(batchMaster);
                customerPeriodRqt.setStartTime(startDate);
                customerPeriodRqt.setEndTime(endDate);
                final GetReserveCustomerPeriodMasterResp reserveCustomerConflictMasterResp =
                        orderCommonServiceApi.getReserveCustomerPeriodMaster(customerPeriodRqt);
                final List<Long> masterIds = reserveCustomerConflictMasterResp.getMasterIds();
                if (CollectionUtils.isNotEmpty(masterIds)) {
                    result.addAll(masterIds);
                }
                i=0;
                batchMaster=new ArrayList<>();
            }
        }
        if (batchMaster.size()!=0) {
            final GetReserveCustomerPeriodRqt customerPeriodRqt = new GetReserveCustomerPeriodRqt();
            customerPeriodRqt.setMasterIds(batchMaster);
            customerPeriodRqt.setStartTime(startDate);
            customerPeriodRqt.setEndTime(endDate);
            final GetReserveCustomerPeriodMasterResp reserveCustomerConflictMasterResp =
                    orderCommonServiceApi.getReserveCustomerPeriodMaster(customerPeriodRqt);
            final List<Long> masterIds = reserveCustomerConflictMasterResp.getMasterIds();
            if (CollectionUtils.isNotEmpty(masterIds)) {
                result.addAll(masterIds);
            }
        }
        return result;
    }

    private String getInterfaceFeatureSingle(
            String masterId,
            ComplexFeature complexFeature,
            DefaultContext<String, Object> orderFeatures,
            DefaultContext<String, DefaultContext<String, Object>> masterFeatures){
                final String featureCode = complexFeature.getFeatureCode();
                switch(featureCode){
                    case "forbidden_offer_price_status":
                        return String.valueOf(
                                forbiddenOfferPriceStatus(
                                masterId, orderFeatures)
                        );
                    case "master_time_conflict":
                        break;
                    default:
                        break;
                }
        return null;
    }


    /**
     * 禁止报价状态
     */
    public void forbiddenOfferPriceStatus(
        Set<String> masterSet,
        DefaultContext<String, Object> orderFeatures,
        DefaultContext<String, DefaultContext<String, Object>> masterFeatures
            ){
        try{
            for(String masterId : masterSet){
                Integer result = forbiddenOfferPriceStatus(masterId,orderFeatures);
                masterFeatures.get(masterId).put("forbidden_offer_price_status",result);
            }
        }catch(Exception e){
            log.error("forbiddenOfferPriceStatus error",e);
        }

    }

    /**
     * 禁止报价状态
     */
    public int forbiddenOfferPriceStatus(
            String masterId,
            DefaultContext<String, Object> orderFeatures){
        final String accountId = String.valueOf(orderFeatures.get("account_id"));
        Integer appointType = (Integer)orderFeatures.get("appoint_type");
        String lv3ServeIds = String.valueOf(orderFeatures.get("lv3_serve_ids"));
        if (StringUtils.isEmpty(lv3ServeIds)) {
            return 0;
        }

        //订单模式
        BatchGetMasterRestrictRqt restrictRqt = new BatchGetMasterRestrictRqt();
        restrictRqt.setMasterIdList(Collections.singletonList(Long.valueOf(masterId)));
        List<MasterOrderServeRestrictResp> masterRestricts = masterRestrictOrderServeApi.getMasterRestricts(restrictRqt);
        if (CollUtil.isEmpty(masterRestricts)) {
            return 0;
        }
        //订单服务
        List<String> baseServes = Arrays.asList(lv3ServeIds.split(","));

        List<MasterOrderServeRestrictResp> masterPermanentRestrict = masterRestricts.stream().filter(Objects::nonNull)
                .filter(el -> Objects.equals(0, el.getLimitFrom()) && Objects.equals(1, el.getPermanentRestrict())).collect(Collectors.toList());
        //违规处罚限制天数的优先
        if (CollUtil.isEmpty(masterPermanentRestrict)) {
            List<Integer> limitFromList = new ArrayList<>(Arrays.asList(5, 6, 7));
            masterPermanentRestrict = masterRestricts.stream().filter(Objects::nonNull)
                    .filter(el -> limitFromList.contains(el.getLimitFrom()) && el.getExamRelateId() == 0).sorted(Comparator.comparingLong(MasterOrderServeRestrictResp::getId)).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(masterPermanentRestrict)) {
            if (doMasterRestrict(masterPermanentRestrict, accountId, baseServes, String.valueOf(appointType))) {
                return 1;
            }
        }

        if (CollUtil.isNotEmpty(masterRestricts)) {
            if (doMasterRestrict(masterRestricts, accountId, baseServes, String.valueOf(appointType))) {
                return 1;
            }
        }
        return 0;
    }

    private boolean doMasterRestrict(List<MasterOrderServeRestrictResp> masterRestricts,
                                                 String accountId,
                                                 List<String> baseServes,
                                                 String appointType) {
//        Map<String, Object> resultMap = new HashMap<>();

        boolean checkMasterRestrict = false;
        String regex = "[，]|[,]";
//        String reason = "";
//        Integer isPermanent = 0;
//        long remainderDays = 0;
//        String alertBoxType = "";
        for (MasterOrderServeRestrictResp serveRestrictResp : masterRestricts) {
            if (serveRestrictResp == null) {
                continue;
            }

            String appointTypes = serveRestrictResp.getAppointTypes();
            String serveIds = serveRestrictResp.getServeIds();
            List<String> userList = com.wanshifu.framework.utils.StringUtils.isNotBlank(serveRestrictResp.getUserIds()) ? Arrays.asList(serveRestrictResp.getUserIds().split(regex)) : Collections.emptyList();
            boolean serveIdsFlag = com.wanshifu.framework.utils.StringUtils.isNotBlank(serveIds) && (Stream.of(serveIds.split(",")).anyMatch(baseServes::contains) || serveIds.contains("all"));
            boolean appointTypeFlag = com.wanshifu.framework.utils.StringUtils.isNotBlank(appointTypes) && (appointTypes.contains("all") || appointTypes.contains(appointType));
            boolean userIdsFlag = CollUtil.isEmpty(userList) || userList.contains(accountId);
            if (appointTypeFlag && serveIdsFlag && userIdsFlag) {
                checkMasterRestrict = true;
//                isPermanent = serveRestrictResp.getPermanentRestrict();
//                remainderDays = serveRestrictResp.getRestrictEndTime() == null ? 0
//                        : DateUtil.between(new Date(), serveRestrictResp.getRestrictEndTime(), DateUnit.DAY, false);
////                alertBoxType = ""Objects.equals(serveRestrictResp.getLimitFrom(), 1) ? "exam_need_to_finish" : "order_offer_restrict""";
//                switch (serveRestrictResp.getLimitFrom()) {
//                    case 1:
//                        alertBoxType = "exam_need_to_finish";
//                        break;
//                    case 5:
//                    case 6:
//                    case 7:
//                        alertBoxType = serveRestrictResp.getExamRelateId() != 0 ? "violation_punish_need_exam" : "order_offer_restrict";
//                        break;
//                    case 0:
//                    default:
//                        alertBoxType = "order_offer_restrict";
//                }
//                reason = serveRestrictResp.getReason();
                break;
            }
        }

//        resultMap.put("checkMasterRestrict", checkMasterRestrict);
//        resultMap.put("isPermanent", isPermanent);
//        resultMap.put("remainderDays", Math.max(remainderDays, 0));
//        resultMap.put("alertBoxType", alertBoxType);
//        resultMap.put("tipContent", reason);
        return checkMasterRestrict;
    }



    /**
     * 补全订单特征
     * @param orderFeatures
     * @param orderFeatureSet
     */
    public void orderFeatureReplenish(DefaultContext<String, Object> orderFeatures,Set<String> orderFeatureSet){
        final Set<String> currentFeatures = orderFeatures.keySet();
        if (currentFeatures.containsAll(orderFeatureSet)) {
            return;
        }

        final HashSet<String> needSet = new HashSet<>(orderFeatureSet);
        needSet.removeAll(currentFeatures);
        if (needSet.size()!=0) {
            final List<ComplexFeature> complexFeatures = complexFeatureRepository.selectByFeatureFor(FieldConstant.ORDER, needSet);
            if (CollectionUtils.isNotEmpty(complexFeatures)) {
                addFeatureDependency(complexFeatures,needSet);
            }
            final List<BaseFeature> baseFeatures =
                    baseFeatureRepository.selectByFeatureType(FieldConstant.ORDER, needSet);
            getSingleRowFeature(baseFeatures,complexFeatures,orderFeatures);
        }
    }


    /**
     * 获取多行特征
     * @param baseFeatures
     * @param complexFeatures
     * @param orderFeatures
     * @return
     */
    private DefaultContext<String, DefaultContext<String, Object>> getMasterFeature(
            Set<String> masterSet
            ,List<BaseFeature> baseFeatures
            ,List<ComplexFeature> complexFeatures
            ,DefaultContext<String, Object> orderFeatures
    ){
        /**
         * 获取基础特征
         */
        final DefaultContext<String, DefaultContext<String, Object>> masterBaseFeature
                = getMasterBaseFeatureByConfig(masterSet, baseFeatures, orderFeatures);
        /**
         * 获取复杂特征
         */
        if (complexFeatures!=null) {
            getComplexFeatureByConfig(complexFeatures,orderFeatures,masterBaseFeature);
        }
        return masterBaseFeature;
    }



    /**
     * 获取单行特征
     * @param baseFeatures
     * @param complexFeatures
     * @param orderFeatures
     * @return
     */
    private DefaultContext<String, Object> getSingleRowFeature(
            List<BaseFeature> baseFeatures
            ,List<ComplexFeature> complexFeatures
            ,DefaultContext<String, Object> orderFeatures
    ){
        final DefaultContext<String, Object> result = new DefaultContext<>();
        /**
         * 获取基础特征
         */
        getSingleRowFeatureByConfig(baseFeatures,orderFeatures);
        /**
         * 获取复杂特征
         */
        if (complexFeatures!=null) {
            getComplexFeatureByConfig(complexFeatures,orderFeatures);
        }
        return result;
    }

    private DefaultContext<String, DefaultContext<String, Object>> getMasterBaseFeatureByConfig(
            Set<String> masterSet
            ,List<BaseFeature> baseFeatures
            ,DefaultContext<String, Object> orderFeatures){
        return getCollectionBaseFeatureByConfig(masterSet,baseFeatures,orderFeatures,FeatureMapKey.MASTER);
    }




    /**
     * 生成batch condition查询条件
     *
     * @return
     */
    private List<Get> generateHbaseQueryGets(Collection<String> dimensionColumns,
                                             Map<String, Object> dimensionData, String batchDimensionColumn, Collection<String> batchColumnValue) {
        List<Get> getList = new ArrayList<>();
        for(String masterId : batchColumnValue){
            StringBuilder key = new StringBuilder(masterId);
            for (String column : dimensionColumns) {
                if (column.equals(batchDimensionColumn)) {
                    continue;
                }
                if(column.contains("{")){
                    String[] dimensions = StrUtil.removeAll(column, '{','}').split("=");
                    column = dimensions[1];
                }


                Object dimensionValueObject = dimensionData.get(column);
                if (dimensionValueObject == null) {
                    log.error(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
                    throw new RuntimeException(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
                }
                String dimensionValue = dimensionValueObject.toString();
                key.append("_").append(dimensionValue);
            }
            getList.add(new Get(Bytes.toBytes(key.toString())));
        }
        return getList;
    }


    /**
     * 生成batch condition查询条件
     *
     * @return
     */
    private Get generateHbaseQueryGet(Collection<String> dimensionColumns, Map<String, Object> dimensionData) {
        StringBuilder key = new StringBuilder();
        for (String column : dimensionColumns) {
            Object dimensionValueObject = dimensionData.get(column);
            if (dimensionValueObject == null) {
                log.error(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
                throw new RuntimeException(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
            }
            String dimensionValue = dimensionValueObject.toString();
            key.append(dimensionValue);
        }
        return new Get(Bytes.toBytes(key.toString()));
    }


    private DefaultContext<String, DefaultContext<String, Object>> getCollectionBaseFeatureByConfig(
            Set<String> masterSet
            , List<BaseFeature> baseFeatures
            , DefaultContext<String, Object> orderFeatures, FeatureMapKey featureMapKey){

        final DefaultContext<String, DefaultContext<String, Object>> result = new DefaultContext<>();
        Map<String,List<BaseFeature>> baseFeatureMap =
                baseFeatures.stream().collect(
                        Collectors.groupingBy(
                                baseFeature ->
                                        baseFeature.getDbType() + ";" + baseFeature.getTableName()
                        )
                );
        for (Map.Entry<String, List<BaseFeature>> stringListEntry : baseFeatureMap.entrySet()) {
            try {
                final List<BaseFeature> baseFeatureList = stringListEntry.getValue();
                List<String> indexColumns = baseFeatureList.stream().map(BaseFeature::getFeatureField).collect(Collectors.toList());
                final BaseFeature baseFeature = baseFeatureList.get(0);
                final String tableName = baseFeature.getTableName();
                List<String> dimensionColumns = Arrays.asList(baseFeature.getFeatureDimension().split(","));
                Map<String, Map<String, Object>> multiRowFeature = null;
                if("hbase".equals(baseFeature.getDbType())){
                    final Set<String> columnsToGet =
                            baseFeatureList.stream().map(row -> row.getFeatureField()).collect(Collectors.toSet());


                    if(featureMapKey == FeatureMapKey.MASTER){
                        columnsToGet.add("master_id");
                        List<Get> getList = generateHbaseQueryGets(dimensionColumns, orderFeatures,
                                "master_id", masterSet);
                        multiRowFeature = hBaseClient.queryMap(columnsToGet,getList,tableName);
                    }else if(featureMapKey == FeatureMapKey.AGENT){
                        columnsToGet.add("agent_id");
                        List<Get> getList = generateHbaseQueryGets(dimensionColumns, orderFeatures,
                                "agent_id", masterSet);
                        multiRowFeature = hBaseClient.queryMap(columnsToGet,getList,tableName);
                    }


                }else{
//                    String conditions = generateBatchQueryConditions(dimensionColumns, indexColumns, orderFeatures,
//                            "master_id", masterSet);
//                    mySqlQuery.query(dimensionColumns,indexColumns,tableName,conditions);
                    if("t_master".equals(tableName)){
                        multiRowFeature = getMasterFeatures(masterSet);
                    }else if("t_master_enterprise".equals(tableName)){
                        multiRowFeature = getMasterEnterpriseFeatures(baseFeature,orderFeatures,masterSet);
                    }

                }


                if(multiRowFeature == null || CollectionUtils.isEmpty(multiRowFeature.keySet())){
                    continue;
                }

                for (Map.Entry<String, Map<String, Object>> masterRow : multiRowFeature.entrySet()) {
                    final String masterId = masterRow.getKey();
                    final Map<String, Object> masterResult = masterRow.getValue();
                    final DefaultContext<String, Object> masterFeature = new DefaultContext<>();
                    baseFeatureList.stream().forEach(row->{
                        {
                            Object featureValue = masterResult.get(row.getFeatureField());
                            if (featureValue instanceof Collection) {
                                /**
                                 * 多值
                                 */
                                final String calculateType = row.getCalculateType();
                                if (FieldConstant.MIN.equals(calculateType)) {
                                    featureValue=LoCollectionsUtil
                                            .collectionCompare((Collection) featureValue, LoCollectionsUtil.Method.MIN);
                                }else if(FieldConstant.MAX.equals(calculateType)){
                                    featureValue=LoCollectionsUtil
                                            .collectionCompare((Collection) featureValue, LoCollectionsUtil.Method.MAX);
                                }else {
                                }
                            }

                            if (featureValue==null) {
                                featureValue=row.getDefaultValue();
                            }

                            if (StringUtils.isNotEmpty(row.getListType())) {
                                List listValue=Arrays.asList(
                                                featureValue.toString().split(SymbolConstant.COMMA))
                                        .stream()
                                        .map(singleValue->
                                                VariableCastUtil.variableCast(singleValue,
                                                        row.getListType())
                                        ).collect(Collectors.toList());
                                masterFeature.put(
                                        row.getFeatureCode(),
                                        listValue
                                );
                            }else {
                                masterFeature.put(
                                        row.getFeatureCode(),
                                        VariableCastUtil.variableCast(featureValue.toString(),row.getFieldType()
                                        ));
                            }
                        }
                    });
                    masterFeature.put("serve_time_conflict",masterResult.getOrDefault("serve_time_conflict",0) );
                    masterFeature.put("account_freeze_status",masterResult.getOrDefault("account_freeze_status",0));
                    masterFeature.put("account_status",masterResult.getOrDefault("account_freeze_status",0));
                    LoCollectionsUtil.putMapToMap(result,masterId,masterFeature);
                }
            }catch (Exception e){
                log.warn("stringListEntry search error:[{}],e",stringListEntry,e);
            }
        }

        fill(result,masterSet,baseFeatures);
        return result;
    }


    private Map<String,Map<String,Object>> getMasterFeatures(Set<String> masterSet){
        Map<String,Map<String,Object>> resultMap = new HashMap<>();
        GetMasterInfoListByIdsRqt rqt = new GetMasterInfoListByIdsRqt();
        List<Long> masterIdList = masterSet.stream().map(Long::parseLong).collect(Collectors.toList());
        rqt.setMasterIds(masterIdList);
        List<GetMasterInfoListByIdsResp> respList = commonQueryServiceApi.getMasterInfoListByIds(rqt);
        if(CollectionUtils.isNotEmpty(respList)){
            respList.forEach(resp -> {
                Map<String,Object> map = new HashMap<>();
                map.put("master_id",String.valueOf(resp.getMasterId()));
                map.put("rest_state",resp.getRestState());
                map.put("lng_lat",resp.getLocationLongitude() + "," + resp.getLocationLatitude());
                map.put("master_current_location",resp.getCurrentLongitude() + "," + resp.getCurrentLatitude());
                map.put("account_status","on".equals(resp.getStatus()) ? 0 : 1);
                long freezingRecoverTime = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
                map.put("account_freeze_status", (resp.getFreezeTime() != null && resp.getFreezeTime() > 0 && resp.getFreezeTime() > freezingRecoverTime) ? 1 : 0);
                resultMap.put(String.valueOf(resp.getMasterId()),map);
            });
        }
        return resultMap;
    }



    private Map<String,Map<String,Object>> getMasterEnterpriseFeatures(BaseFeature baseFeature,DefaultContext<String, Object> orderFeatures,
                                                                       Set<String> masterSet){
        Map<String,Map<String,Object>> masterFeatureMap = new HashMap<>();

        try{
            List<String> dimensionColumns = Arrays.asList(baseFeature.getFeatureDimension().split(","));
            JSONArray result = new JSONArray();
            featureQueryServiceImpl.queryMasterFeatureBatch(dimensionColumns,Collections.singletonList(baseFeature.getFeatureField()),orderFeatures,masterSet,baseFeature.getDbType(),baseFeature.getTableName(),result);
            Map<String, List<JSONObject>> featureFieldMap = FeatureQueryServiceImpl.getMasterFeatureMap(result);

            if(CollectionUtils.isNotEmpty(featureFieldMap.keySet())){
                featureFieldMap.keySet().forEach(masterId -> {
                    if(CollectionUtils.isNotEmpty(featureFieldMap.get(masterId))){
                        JSONObject masterFeatureObject = featureFieldMap.get(masterId).get(0);
                        Map<String,Object> singleMasterFeature = new HashMap<>();
                        singleMasterFeature.put(baseFeature.getFeatureCode(),masterFeatureObject.get(baseFeature.getFeatureField()));
                        masterFeatureMap.put(masterId,singleMasterFeature);
                    }

                });
            }
        }catch(Exception e){
            log.error("getMasterEnterpriseFeatures",e);
        }


        return masterFeatureMap;
    }


    /**
     * 生成conditon查询条件
     *
     * @return
     */
    /**
     * 生成batch condition查询条件
     *
     * @return
     */
    private String generateBatchConditionsByIndexColumns(Collection<String> dimensionColumns,
                                                         Map<String, Object> dimensionData, String batchDimensionColumn, Collection<String> batchColumnValue) {
        StringBuilder conditonString = new StringBuilder();
        for (String column : dimensionColumns) {
            if (column.equals(batchDimensionColumn)) {
                continue;
            }
            Object dimensionValueObject = dimensionData.get(column);
            if (dimensionValueObject == null) {
                log.error(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
                throw new RuntimeException(String.format("维度缺失,查询构建失败,dimensionColumn:%s",column));
            }
            String dimensionValue = dimensionValueObject.toString();
            conditonString.append(column);
            conditonString.append(" = ");
            conditonString.append("'");
            conditonString.append(dimensionValue);
            conditonString.append("'");
            conditonString.append(" and ");
        }
        String batchValue = StringUtils.join(batchColumnValue, "','");
        batchValue = "'" + batchValue + "'";
        conditonString.append(batchDimensionColumn).append(" in (").append(batchValue).append(")");
        return conditonString.toString();
    }

    /**
     * 生成主键列表
     * @return
     */
//    private ArrayList<KeyValueList> generateKeyValues(
//            Set<String> masterSet,
//            LinkedHashMap<String, PrimaryKeyType> primaryKeyByDB,
//            List<BaseFeature> baseFeatures,
//            DefaultContext<String, Object> orderFeatures,
//            Set<String> multiValueColumnSet){
//
//        final ArrayList<KeyValueList> result = new ArrayList<>();
//        final HashMap<String, Object> transMap = new HashMap<>(orderFeatures);
//        featureTrans(orderFeatures,transMap,multiValueColumnSet,baseFeatures);
//        for (Map.Entry<String, PrimaryKeyType> entry : primaryKeyByDB.entrySet()) {
//            final String columnName = entry.getKey();
//            final KeyValueList keyValueList = new KeyValueList(columnName, new ArrayList<>());
//            //WORKFLOW
//            if (FieldConstant.MASTER_ID.equals(columnName)||FieldConstant.AGENT_ID.equals(columnName)) {
//                keyValueList.addValueList(masterSet);
//                result.add(keyValueList);
//                continue;
//            }
//            final Object o = transMap.get(columnName);
//            if (o==null) {
//                return null;
//            }
//            if (multiValueColumnSet.contains(columnName)) {
//                String multi=o.toString();
//                keyValueList.addValueList(Arrays.asList(multi.split(SymbolConstant.COMMA)));
//                result.add(keyValueList);
//                continue;
//            }
//
//            keyValueList.addValue(o.toString());
//            result.add(keyValueList);
//        }
//        return result;
//    }

    /**
     * 暂时修改特征名称
     */
    private void featureTrans(DefaultContext<String, Object> orderFeatures,
                              HashMap<String, Object> transMap,
                              Set<String> multiValueColumnSet,
                              List<BaseFeature> baseFeatures){
        baseFeatures.stream().forEach(row->{
            final String featureDimension = row.getFeatureDimension();
            final String[] split = featureDimension.split(SymbolConstant.COMMA);
            for (String dimensionName : split) {
                if (dimensionName.contains("{")) {
                    final String pair = dimensionName.replaceAll("\\{|\\}", "");
                    final String[] pairArray = pair.split("=");
                    transMap.put(pairArray[0],orderFeatures.get(pairArray[1]));
                }
            }
            final String multiValueDimension = row.getMultiValueDimension();
            if (StringUtils.isNotEmpty(multiValueDimension)) {
                multiValueColumnSet.addAll(Arrays.asList(multiValueDimension.split(SymbolConstant.COMMA)));
            }
        });
    }

    private void fill(DefaultContext<String, DefaultContext<String, Object>> result
            ,Set<String> masterSet,List<BaseFeature> baseFeatures){
        if (masterSet.size()!=0) {
            for (String master : masterSet) {
                final DefaultContext<String, Object> defaultFeature = new DefaultContext<>();
                baseFeatures.stream().forEach(row->{
                    final String featureCode = row.getFeatureCode();
                    final String defaultValue = row.getDefaultValue();
                    final DefaultContext<String, Object> masterCurrentFeature = result.get(master);
                    if (masterCurrentFeature!=null) {
                        final Object currentFeatureCode = masterCurrentFeature.get(featureCode);
                        if (currentFeatureCode==null) {
                            defaultFeature.put(featureCode,VariableCastUtil.variableCast(
                                    defaultValue,row.getFieldType()
                            ));
                        }
                    }else {
                        defaultFeature.put(featureCode,VariableCastUtil.variableCast(
                                defaultValue,row.getFieldType()
                        ));
                    }
                });
                LoCollectionsUtil.putMapToMap(result,master,defaultFeature);
            }
        }
    }

    private void getSingleRowFeatureByConfig(
            List<BaseFeature> baseFeatures,
            DefaultContext<String, Object> orderFeatures){

        Map<String,List<BaseFeature>> baseFeatureMap =
                baseFeatures.stream().collect(
                        Collectors.groupingBy(
                                baseFeature ->
                                        baseFeature.getInstanceName() + ";" + baseFeature.getTableName()
                        )
                );
        for (Map.Entry<String, List<BaseFeature>> stringListEntry : baseFeatureMap.entrySet()) {
            final List<BaseFeature> baseFeatureList = stringListEntry.getValue();
            final BaseFeature baseFeature = baseFeatureList.get(0);
            final String tableName = baseFeature.getTableName();
            final Set<String> columnsToGet =
                    baseFeatureList.stream().map(row -> row.getFeatureField()).collect(Collectors.toSet());
            final HashMap<String, Object> transMap = new HashMap<>(orderFeatures);
            Set<String> multiValueColumnSet=new HashSet<>();
            featureTrans(orderFeatures,transMap,multiValueColumnSet,baseFeatures);
//            final Map<String, String> featureByTableStore = getSingleRowFeatureByTableStore(tableName, transMap, columnsToGet);
            List<String> dimensionColumns = Arrays.asList(baseFeature.getFeatureDimension().split(","));
            Get get = generateHbaseQueryGet(dimensionColumns,orderFeatures);
            Map<String,Object> resultMap = hBaseClient.querySingle(columnsToGet,get,tableName);
            baseFeatureList.stream().forEach(row->{
                {
                    Object featureValue = resultMap.get(row.getFeatureField());
                    if (featureValue==null) {
                        featureValue=row.getDefaultValue();
                    }
                    orderFeatures.put(row.getFeatureCode(),
                            VariableCastUtil.variableCast(featureValue.toString(),row.getFieldType()));
                }
            });
        }
    }

    /**
     *  获取复杂特征
     * @param complexFeatures
     * @param orderFeatures
     */
    private void getComplexFeatureByConfig(
            List<ComplexFeature> complexFeatures,
            DefaultContext<String, Object> orderFeatures){
        final DefaultContext<String, Object> result = new DefaultContext<>();
        for (ComplexFeature complexFeature : complexFeatures) {
            final String calculateExpression = complexFeature.getCalculateExpression();
            final String calculateValue = QlExpressStatic
                    .QlExpressString(calculateExpression, orderFeatures, complexFeature.getDefaultValue());
            orderFeatures.put(complexFeature.getFeatureCode(),
                    VariableCastUtil.variableCast(calculateValue,complexFeature.getFieldType())
            );
        }
    }

    private void getComplexFeatureByConfig(
            List<ComplexFeature> complexFeatures,
            DefaultContext<String, Object> orderFeatures,
            DefaultContext<String, DefaultContext<String, Object>> masterBaseFeature){

        final DefaultContext<String, Object> extractor = featureExtractor(orderFeatures, complexFeatures);
        for (Map.Entry<String, DefaultContext<String, Object>> stringDefaultContextEntry : masterBaseFeature.entrySet()) {
            final String masterId = stringDefaultContextEntry.getKey();
            final DefaultContext<String, Object> masterFeature = stringDefaultContextEntry.getValue();
            masterFeature.putAll(extractor);

            for (ComplexFeature complexFeature : complexFeatures) {
                final String calculateExpression = complexFeature.getCalculateExpression();

                if (SymbolConstant.EMPTY_STRING.equals(calculateExpression)) {
                    /**
                     * 此处调用接口特征需要每个师傅调用一次接口，
                     */
                    masterFeature.put(
                            complexFeature.getFeatureCode(),
                            VariableCastUtil.variableCast(
                                    getInterfaceFeatureSingle(
                                    masterId,complexFeature,orderFeatures,masterBaseFeature),
                                    complexFeature.getFieldType())
                    );
                }else {
                    final String calculateValue = QlExpressStatic
                            .QlExpressString(calculateExpression, masterFeature, complexFeature.getDefaultValue());
                    masterFeature.put(complexFeature.getFeatureCode(),
                            VariableCastUtil.variableCast(calculateValue,complexFeature.getFieldType())
                    );
                }
            }
        }
    }

    private DefaultContext<String, Object> featureExtractor(
                            DefaultContext<String, Object> orderFeatures,
                            List<ComplexFeature> complexFeatures){
        final DefaultContext<String, Object> result = new DefaultContext<>();
        complexFeatures.stream().forEach(row->{{
            row.setOrderFeatureRelaSet();
            final Set<String> orderFeatureRela = row.getOrderFeatureRela();
            for (String featureName : orderFeatureRela) {
                result.put(featureName,orderFeatures.get(featureName));
            }
        }});
        return result;
    }




    public void getAgreementCoopStreetNum(Map<String,DefaultContext<String,Object>> masterFeatureContext, List<AgreementMasterExtra> agreementMasterList){
        //TODO 处理一个师傅参加多个招募的场景
        Map<Long, AgreementMaster> agreementMasterMap = agreementMasterList.stream()
                .collect(Collectors.toMap(AgreementMaster::getMasterId, agreementMaster -> agreementMaster));
        masterFeatureContext.keySet().forEach(masterId -> masterFeatureContext.get(masterId).put("agreement_street_num",agreementMasterMap.containsKey(Long.valueOf(masterId)) ?
                Arrays.asList(agreementMasterMap.get(Long.valueOf(masterId)).getLv4DivisionIds().split(",")).size()  : 0));

    }


    public void getLowestAgreementPrice(Map<String,DefaultContext<String,Object>> masterFeatureContext, List<AgreementMasterBase> agreementMasterList){
        if(CollectionUtils.isNotEmpty(agreementMasterList)){
            AgreementMasterBase agreementMasterBase = CollectionUtils.isNotEmpty(agreementMasterList) ? agreementMasterList.stream().sorted().collect(Collectors.toList()).get(0) : null;
            masterFeatureContext.keySet().forEach(masterId -> masterFeatureContext.get(masterId).put("lowest_agreement_price",agreementMasterBase != null && masterId.equals(agreementMasterBase.getMasterId()) ? 1 : 0));
        }else{
            masterFeatureContext.keySet().forEach(masterId -> masterFeatureContext.get(masterId).put("lowest_agreement_price", 0));
        }

    }



    public Map<Long,Long> getAgreementMasterOrderDistance(OrderDetailData orderDetailData,
                                 List<AgreementMasterExtra> agreementMasterList){

        if(StringUtils.isBlank(orderDetailData.getOrderLngLat())){
//            masterFeatureContext.keySet().forEach(masterId -> {
//                masterFeatureContext.get(masterId).put("agreement_order_master_distance",0);
//            });
            return null;
        }


        Set<Long> fourthDivisionIdSet = new HashSet<>();
        agreementMasterList.forEach(agreementMaster -> fourthDivisionIdSet.addAll(
                Arrays.asList(agreementMaster.getLv4DivisionIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toSet())
        ));


        Map<Long,Long> fourthDivisionDistanceMap = new HashMap<>();
        for(Long fourthDivisionId : fourthDivisionIdSet){
            PositionConvert positionConvert = addressCommon.getPositionConvertByDivisionId(fourthDivisionId);
            if(Objects.nonNull(positionConvert) && Objects.nonNull(positionConvert.getLatitude()) && Objects.nonNull(positionConvert.getLongitude() )&&
                    positionConvert.getLatitude().compareTo(BigDecimal.ZERO) > 0 && positionConvert.getLongitude().compareTo(BigDecimal.ZERO) > 0){
                String[] lntLatArray = orderDetailData.getOrderLngLat().split(",");
                Long distance = DistanceUtil.distance(positionConvert.getLatitude().doubleValue(),positionConvert.getLongitude().doubleValue(),new BigDecimal(lntLatArray[1]).doubleValue(),new BigDecimal(lntLatArray[0]).doubleValue());
                fourthDivisionDistanceMap.put(fourthDivisionId,distance);
            }
        };



        agreementMasterList.forEach(agreementMaster -> {
            Long minOrderDistance = 99999L;
            Long fourthDivisionId = 0L;
            Set<Long> lv4DivisionIdSet = Arrays.asList(agreementMaster.getLv4DivisionIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toSet());
            agreementMaster.setFourthDivisionIdList(lv4DivisionIdSet);
            for(Long lv4DivisionId : lv4DivisionIdSet){
                if(fourthDivisionDistanceMap.get(lv4DivisionId) < minOrderDistance){
                    minOrderDistance = fourthDivisionDistanceMap.get(lv4DivisionId);
                    fourthDivisionId = lv4DivisionId;
                }
            }
            agreementMaster.setFourthDivisionId(fourthDivisionId);
            agreementMaster.setOrderDistance(minOrderDistance);
        });

        return fourthDivisionDistanceMap;
    }


    public void getOrderDistance(OrderDetailData orderDetailData, Map<String,DefaultContext<String,Object>> masterFeatureContext,
                                 List<AgreementMasterExtra> agreementMasterList){



        getAgreementMasterOrderDistance(orderDetailData,agreementMasterList);

        Map<Long, AgreementMasterExtra> agreementMasterMap = agreementMasterList.stream()
                .collect(Collectors.toMap(AgreementMaster::getMasterId, agreementMaster -> agreementMaster));

        masterFeatureContext.keySet().forEach(masterId -> {
            AgreementMasterExtra enterpriseAgreementMaster = agreementMasterMap.get(Long.valueOf(masterId));
            masterFeatureContext.get(masterId).put("agreement_order_master_distance",enterpriseAgreementMaster.getOrderDistance());
        });
    }



    @Resource
    private QLExpressHandler qlExpressHandler;


    /**
     * 查询订单特征
     * @param pushCommonObject
     * @param orderFeatureSet
     * @return 推单特征
     */
    public PushFeature getOrderFeatures(PushCommonObject pushCommonObject, Set<String> orderFeatureSet) {

        DefaultContext<String, Object> dimensionData = buildDimensionData(pushCommonObject.getOrderDetailData());
        Set<String> masterIdSet = pushCommonObject.getMasterSet();
        PushFeature feature = new PushFeature(pushCommonObject.getOrderDetailData().getGlobalOrderId(),masterIdSet);
        feature.addDimensionFeature(dimensionData);

        try{

            List<ComplexFeature> orderComplexFeatureList = complexFeatureRepository.selectByFeatureFor("order",orderFeatureSet);
            if(com.wanshifu.framework.utils.CollectionUtils.isNotEmpty(orderComplexFeatureList)){
                orderComplexFeatureList.forEach(complexFeature -> orderFeatureSet.addAll(Arrays.asList(complexFeature.getFeatureDependency().split(","))));
            }

            // 订单特征t
            List<BaseFeature> orderBaseFeatureList = baseFeatureRepository.selectByFeatureType("order",orderFeatureSet);
            Map<String,List<BaseFeature>> baseFeatureMap = orderBaseFeatureList.stream().collect(Collectors.groupingBy(baseFeature -> baseFeature.getDbType() + ";" + baseFeature.getTableName()));

            for(Map.Entry<String,List<BaseFeature>> entry: baseFeatureMap.entrySet()){
                Map<String, BaseFeatureConfigDimensionList>  orderBaseFeatureMap =  initBaseFeatureDimension(entry.getValue());
                queryOrderBaseFeature(feature,orderBaseFeatureMap,dimensionData);
            }

            // 计算订单特征
            calculateOrderComplexFeature(feature.getOrderFeature(),orderComplexFeatureList);
        }catch(Exception e){
            log.error(String.format("query orderFeature error,globalOrderId:%d,orderFeatureSet:%s",pushCommonObject.getGlobalOrderId(),orderFeatureSet),e);
        }

        return feature;
    }


    /**
     * 查询师傅特征
     *
     * @param pushFeature
     * @param masterIdSet
     * @param masterFeatureSet
     * @return
     */
    public void getMasterFeatures(PushFeature pushFeature,Set<String> masterIdSet,Set<String> masterFeatureSet) {
        try{
            List<ComplexFeature> masterComplexFeatureList = complexFeatureRepository.selectByFeatureFor("master",masterFeatureSet);
            if(com.wanshifu.framework.utils.CollectionUtils.isNotEmpty(masterComplexFeatureList)){
                masterComplexFeatureList.forEach(complexFeature -> masterFeatureSet.addAll(Arrays.asList(complexFeature.getFeatureDependency().split(","))));
            }
            // 特征
            List<BaseFeature> masterBaseFeatureList = baseFeatureRepository.selectByFeatureType("master",masterFeatureSet);
            Map<String,List<BaseFeature>> baseFeatureMap = masterBaseFeatureList.stream().collect(Collectors.groupingBy(baseFeature -> baseFeature.getDbType() + ";" + baseFeature.getTableName()));

            for(Map.Entry<String,List<BaseFeature>> entry: baseFeatureMap.entrySet()){
                Map<String, BaseFeatureConfigDimensionList>  masterBaseFeatureMap =  initBaseFeatureDimension(entry.getValue());
                queryMasterBaseFeature(pushFeature,masterBaseFeatureMap,pushFeature.getDimensionFeature(),masterIdSet);
            }

            List<ComplexFeature> calculateComplexFeatureList = masterComplexFeatureList.stream().filter(complexFeature -> !StringUtil.isEmpty(complexFeature.getCalculateExpression())).collect(Collectors.toList());

            //计算师傅复杂特征
            calculateMasterComplexFeature(pushFeature,calculateComplexFeatureList);

            getInterfaceFeatureBatch(masterIdSet,masterComplexFeatureList,pushFeature.getOrderFeature(),pushFeature.getMasterFeature());

            if(masterFeatureSet.contains("serve_time_conflict")){
                getServeTimeConflict(pushFeature.getOrderFeature(),masterIdSet,pushFeature.getMasterFeature());
            }


            if(masterFeatureSet.contains("wait_door_in_order_distance")){
                getWaitDoorInOrderDistance(pushFeature.getOrderFeature(),masterIdSet,pushFeature.getMasterFeature());
            }


            if(masterFeatureSet.contains("master_time_conflict")){
                reserveCustomerPeriodConflictMaster(
                        masterIdSet,
                        pushFeature.getOrderFeature(),
                        pushFeature.getMasterFeature()
                );
            }


            if(masterFeatureSet.contains("new_contract_rest_status")){
                newContractRestStatus(
                        masterIdSet,
                        pushFeature.getMasterFeature()
                );
            }


            if(masterFeatureSet.contains("real_time_in_service_cnt")){
            realTimeInServiceCnt(
                    masterIdSet,
                    pushFeature.getOrderFeature(),
                    pushFeature.getMasterFeature()
            );
        }

            if(masterFeatureSet.contains("account_status") || masterFeatureSet.contains("account_freeze_status")){
                getMasterFeatures(
                        masterIdSet,
                        pushFeature.getOrderFeature(),
                        pushFeature.getMasterFeature()
                );
            }


            if(masterFeatureSet.contains("forbidden_offer_price_status")){
                forbiddenOfferPriceStatus(
                        masterIdSet,
                        pushFeature.getOrderFeature(),
                        pushFeature.getMasterFeature()
                );
            }


            if(masterFeatureSet.contains("order_master_navigational_distance") ){
                calculateOrderMasterNavigationalDistance(
                        masterIdSet,
                        pushFeature.getOrderFeature(),
                        pushFeature.getMasterFeature()
                );
            }
            


            if(masterFeatureSet.contains("week_grab_cnt") || masterFeatureSet.contains("week_appoint_cnt") ||
                    masterFeatureSet.contains("min_week_grab_cnt") || masterFeatureSet.contains("min_week_appoint_cnt") ){
                getWeekGrabOrAppointCnt(masterIdSet,pushFeature.getMasterFeature());
            }


            if(masterFeatureSet.contains("full_time_master_week_grab_cnt") || masterFeatureSet.contains("full_time_master_week_appoint_cnt") ||
                    masterFeatureSet.contains("full_time_master_min_week_grab_cnt") || masterFeatureSet.contains("full_time_master_min_week_appoint_cnt") ||
                    masterFeatureSet.contains("full_time_master_daily_grab_cnt") || masterFeatureSet.contains("full_time_master_daily_appoint_cnt")){
                getFullTimeWeekGrabOrAppointCnt(masterIdSet,pushFeature.getMasterFeature());
            }



        }catch(Exception e){
            log.error(String.format("query master features error,masterIdSet:%s,masterFeatureSet:%s",masterIdSet,masterFeatureSet),e);
        }

    }


    public PushFeature buildPushFeature(OrderDetailData orderDetailData, Set<String> masterSet) {
        DefaultContext<String, Object> dimensionData = buildDimensionData(orderDetailData);
        PushFeature feature = new PushFeature(orderDetailData.getGlobalOrderId(), masterSet);
        feature.addDimensionFeature(dimensionData);
        return feature;
    }

    public PushFeature buildPushFeature(OrderDetailData orderDetailData) {
        DefaultContext<String, Object> dimensionData = buildDimensionData(orderDetailData);
        PushFeature feature = new PushFeature();
        feature.setGlobalOrderId(orderDetailData.getGlobalOrderId());
        feature.addDimensionFeature(dimensionData);
        return feature;
    }


    public DefaultContext<String, Object> buildOrderFeatures(OrderDetailData orderDetailData) {
        return buildDimensionData(orderDetailData);
    }


    public void queryUserGroups(OrderDetailData orderDetailData,DefaultContext<String, Object> orderFeatures){
        final String groupIds = hBaseClient.querySingle("usr_groups_stat", String.valueOf(orderDetailData.getUserId()),"group_ids");
        if (StringUtils.isNotBlank(groupIds)) {
            final List<Integer> groupIdList = Arrays.asList(
                    groupIds.split(","))
                    .stream().map(row -> Integer.valueOf(row)).collect(Collectors.toList());
            orderFeatures.put("user_group",groupIdList);
            orderFeatures.put("appoint_user",groupIdList);
        }else{
            orderFeatures.put("user_group",new ArrayList<>());
            orderFeatures.put("appoint_user",new ArrayList<>());

        }
    }




    /**
     * 查询基础订单特征
     *
     * @param pushFeature
     * @param featureQueryMap
     * @param dimensionData
     */
    private void queryOrderBaseFeature(PushFeature pushFeature, Map<String, BaseFeatureConfigDimensionList> featureQueryMap,
                                       Map<String, Object> dimensionData) {
        for (Map.Entry<String, BaseFeatureConfigDimensionList> row : featureQueryMap.entrySet()) {
            // 当前维度
            String dimensionColumn = row.getKey();
            List<String> dimensionColumnList = Arrays.asList(dimensionColumn.split(","));
            BaseFeatureConfigDimensionList featureConfigRowList = row.getValue();
            // 查询
            JSONObject result = featureQueryServiceImpl.queryOrderFeature(dimensionColumnList,
                    new HashSet<String>(featureConfigRowList.getFeatureFieldList()), dimensionData,featureConfigRowList.get(0).getDbType(),featureConfigRowList.get(0).getTableName());
            // 添加
            addOrderFeature(featureConfigRowList, result, pushFeature);
        }
    }


    private void addOrderFeature(BaseFeatureConfigDimensionList featureConfigRowList, JSONObject featureField,
                                 PushFeature feature) {
        for (BaseFeature baseFeature : featureConfigRowList) {
            String featureCode = baseFeature.getFeatureCode();
            String defaultValue = baseFeature.getDefaultValue();
            // 类型转换和默认值
            Object featureValue = getFeatureValue(featureField,  baseFeature.getFeatureField(), baseFeature.getFieldType(),
                    defaultValue);
            if (FieldConstant.DEFAULT_UNSPECIFIED.equals(featureValue.toString())) {
                continue;
            }
            feature.addOrderFeature(featureCode, convertFeatureValueToList(baseFeature,featureValue));
        }
    }

    private Object convertFeatureValueToList(BaseFeature baseFeature,Object featureValue){
        if(org.apache.commons.lang.StringUtils.isNotBlank(baseFeature.getListType())){
            String featureValueStr = (String)featureValue;
            if(org.apache.commons.lang.StringUtils.isNotBlank(featureValueStr)){
                return Stream.of(featureValueStr.split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            }else{
                return new ArrayList<>();
            }
        }
        return featureValue;
    }


    private Object getFeatureValue(JSONObject featureField, String featureName, String fieldType, String defaultValue) {
        Object result = null;
        try {
            if ("Long".equals(fieldType)) {
                result = JSONUtil.getLong(featureField, featureName, defaultValue);
            } else if ("Double".equals(fieldType)) {
                result = JSONUtil.getDouble(featureField, featureName, defaultValue);
            } else {
                result = JSONUtil.getString(featureField, featureName, defaultValue);
            }
        } catch (Exception e) {
            result = VariableCastUtil.variableCast(defaultValue, fieldType);
        }
        return result;
    }


    private Map<String,Object> getMultiDimensionFeatureValue(List<JSONObject> featureFieldList, String featureName, String fieldType,String defaultValue,String calculateType) {

        Map<String,Object> resultMap = new HashMap<>();
        List<Object> list = new ArrayList<>();
        Object calculateValue = null;

        if(com.wanshifu.framework.utils.CollectionUtils.isEmpty(featureFieldList)){
            Object result = getFeatureValue(null, featureName, fieldType, defaultValue);
            list.add(result);
            calculateValue = result;
        }else{

            for(JSONObject jsonObject : featureFieldList) {
                Object result = getFeatureValue(jsonObject, featureName, fieldType, defaultValue);
                list.add(result);

                if(calculateValue == null){
                    calculateValue = result;
                }else{
                    if("min".equals(calculateType)){
                        if ("Long".equals(fieldType)) {
                            if(((Long)calculateValue) > ((Long)result)){
                                calculateValue = result;
                            }

                        }else if ("Double".equals(fieldType)) {
                            if(((Double)calculateValue) > ((Double)result)){
                                calculateValue = result;
                            }

                        }
                    }else if("max".equals(calculateType)){
                        if ("Long".equals(fieldType)) {
                            if(((Long)calculateValue) < ((Long)result)){
                                calculateValue = result;
                            }

                        }else if ("Double".equals(fieldType)) {
                            if(((Double)calculateValue) < ((Double)result)){
                                calculateValue = result;
                            }

                        }
                    }

                }
            }

        }

        resultMap.put("valueList",list);
        resultMap.put("calculateValue",calculateValue);

        return resultMap;
    }


    /**
     * 计算订单特征
     *
     * @param orderFeature
     */
    private void calculateOrderComplexFeature(Map<String, Object> orderFeature,List<ComplexFeature> orderComplexFeatureList) {
        Map<String, ComplexFeature> complexFeatureMap = new HashMap<>();
        orderComplexFeatureList.forEach(complexFeature -> {
            complexFeature.setFeatureDependencySet(complexFeature.getFeatureDependency());
            complexFeature.setDependencyCheck(complexFeature.getDependencyCheck());
            complexFeatureMap.put(complexFeature.getFeatureCode(),complexFeature);
        });
        DefaultContext<String, Object> calcuMaterial = new DefaultContext<>();
        calcuMaterial.putAll(orderFeature);
        for (Map.Entry<String, ComplexFeature> row : complexFeatureMap.entrySet()) {
            String featureCode = row.getKey();
            ComplexFeature complexFeature = row.getValue();
            if (complexFeature.needDependencyCheck()) {
                if (!complexFeature.featureCheck(calcuMaterial.keySet())) {
                    continue;
                }
            }
            String defaultValue = complexFeature.getDefaultValue();
            String aviatorExpression = complexFeature.getCalculateExpression();
            Object featureValue = calculateFeatureValue(aviatorExpression, calcuMaterial, defaultValue,
                    complexFeature.getFieldType());
            if (FieldConstant.DEFAULT_UNSPECIFIED.equals(featureValue.toString())) {
                continue;
            }
            orderFeature.put(featureCode, featureValue);
        }
    }


    /**
     * 计算师傅特征
     *
     * @param feature
     */
    private void calculateMasterComplexFeature(PushFeature feature,List<ComplexFeature> masterComplexFeatureList) {
        DefaultContext<String, DefaultContext<String, Object>> masterFeature = feature.getMasterFeature();
        Map<String, ComplexFeature> complexFeatureMap = new HashMap<>();
        Set<String> additionFeatureList = new HashSet<>();
        masterComplexFeatureList.forEach(complexFeature -> {
            if (complexFeature.getFeatureDependency()!=null) {
                for (String featureName : complexFeature.getFeatureDependency().split(",")) {
                    if (featureName.startsWith("{")) {
                        additionFeatureList.add(StrUtil.removeAll(featureName, '{','}'));
                    }
                }
            }
            complexFeature.setFeatureDependencySet(complexFeature.getFeatureDependency());
            complexFeature.setDependencyCheck(complexFeature.getDependencyCheck());
            complexFeatureMap.put(complexFeature.getFeatureCode(),complexFeature);
        });

        for (DefaultContext.Entry<String, DefaultContext<String, Object>> row : masterFeature.entrySet()) {
            // 循环师傅
            HashMap<String, Object> currentMasterFeature = row.getValue();
            DefaultContext<String, Object> calcuMaterial = new DefaultContext<>();
            calcuMaterial.putAll(currentMasterFeature);
            Map<String, Object> additionFeatureMap = feature.getOrderFeatureByList(additionFeatureList);
            calcuMaterial.putAll(additionFeatureMap);
            for (Map.Entry<String, ComplexFeature> rowTwo : complexFeatureMap.entrySet()) {
                // 循环特征
                String featureName = rowTwo.getKey();
                ComplexFeature complexFeature = rowTwo.getValue();
                if (complexFeature.needDependencyCheck()) {
                    if (!complexFeature.featureCheck(calcuMaterial.keySet())) {
                        continue;
                    }
                }
                String aviatorExpression = complexFeature.getCalculateExpression();
                Object featureValue = calculateFeatureValue(aviatorExpression, calcuMaterial,
                        complexFeature.getDefaultValue(), complexFeature.getFieldType());
                if (FieldConstant.DEFAULT_UNSPECIFIED.equals(featureValue.toString())) {
                    continue;
                }
                currentMasterFeature.put(featureName, featureValue);
            }
        }
    }

    /**
     * 计算特征值：aviator
     *
     * @return
     */
    private Object calculateFeatureValue(String expression, DefaultContext<String, Object> calcuMaterial,
                                         String defaultValue, String fieldType) {
        Object featureValue = null;
        try {
            featureValue = qlExpressHandler.getExpressRunner().execute(expression, calcuMaterial,null,true,false);
            featureValue = VariableCastUtil.variableCast(featureValue, fieldType);
        } catch (Exception e) {
            log.error("calculateFeatureValue error",e);
            featureValue = VariableCastUtil.variableCast(defaultValue, fieldType);
        }
        return featureValue;
    }

    /**
     * 添加师傅特征
     *
     * @param featureConfigRowList
     * @param feature
     */
    private void addMasterFeature(BaseFeatureConfigDimensionList featureConfigRowList,
                                  Map<String, List<JSONObject>> masterFeaturesArray, PushFeature feature) {
        // 师傅
        for (String masterId : feature.getMasterFeature().keySet()) {
            DefaultContext<String, Object> currentMasterFeature = new DefaultContext<>();
            List<JSONObject> masterFeatureRowList = masterFeaturesArray.get(masterId);
            // 特征
            for (BaseFeature baseFeature : featureConfigRowList) {
                String featureCode = baseFeature.getFeatureCode();

                String defaultValue = baseFeature.getDefaultValue();
                Object featureValue = null;


                Object calculateValue =null;

                if(org.apache.commons.lang.StringUtils.isNotBlank(baseFeature.getMultiValueDimension())){
                    // 类型转换和默认值
                    Map<String,Object> valueMap = getMultiDimensionFeatureValue(masterFeatureRowList, baseFeature.getFeatureField(), baseFeature.getFieldType(),defaultValue,baseFeature.getCalculateType());
                    featureValue = valueMap.get("valueList");
                    calculateValue = valueMap.get("calculateValue");

                }else{
                    // 类型转换和默认值
                    featureValue = getFeatureValue(com.wanshifu.framework.utils.CollectionUtils.isNotEmpty(masterFeatureRowList) && masterFeatureRowList.get(0) != null ? masterFeatureRowList.get(0) : null, baseFeature.getFeatureField(), baseFeature.getFieldType(),
                            defaultValue);
                }

                currentMasterFeature.put(featureCode, convertFeatureValueToList(baseFeature,featureValue));
                if(calculateValue != null){
                    currentMasterFeature.put(featureCode + ":calculateValue", calculateValue);
                }
            }
            //冗余师傅ID 2022-03-08
            currentMasterFeature.put(FieldConstant.MASTER_ID,masterId);
            feature.addMasterFeature(masterId, currentMasterFeature);
        }
    }



    /**
     * 查询基础师傅特征
     *
     * @param pushFeature
     * @param featureQueryMap
     * @param dimensionData
     */
    private void queryMasterBaseFeature(PushFeature pushFeature, Map<String, BaseFeatureConfigDimensionList> featureQueryMap,
                                        Map<String, Object> dimensionData, Set<String> masterIdSet) {
        for (Map.Entry<String, BaseFeatureConfigDimensionList> row : featureQueryMap.entrySet()) {
            // 当前维度
            String dimensionColumn = row.getKey();
            BaseFeatureConfigDimensionList featureConfigRowList = row.getValue();
            List<String> dimensionColumnList = null;
            if (featureConfigRowList.isDynamicDimension()) {
                // 动态维度
                featureConfigRowList.genrateDimensionFeature(dimensionData);
                dimensionColumnList = featureConfigRowList.dynamicDimensionList();
            } else {
                //TODO 验证是否有序
                dimensionColumnList = Arrays.asList(dimensionColumn.split(","));
            }
            // 查询
            JSONArray result = featureQueryServiceImpl.queryMasterFeature(dimensionColumnList,
                    featureConfigRowList.getFeatureFieldList() ,dimensionData, masterIdSet,featureConfigRowList.get(0).getDbType(),featureConfigRowList.get(0).getTableName(),featureConfigRowList.getMultiValueDimension());

            Map<String, List<JSONObject>> featureFieldMap = FeatureQueryServiceImpl.getMasterFeatureMap(result);
            // 添加
            addMasterFeature(featureConfigRowList, featureFieldMap, pushFeature);

            if(org.apache.commons.lang.StringUtils.isNotBlank(featureConfigRowList.getMultiValueDimension())){
                pushFeature.addMultiValueFeature(featureConfigRowList.getFeatureCodeList());
            }
        }
    }


    /**
     * 基础特征维度划分
     */
    private Map<String, BaseFeatureConfigDimensionList> initBaseFeatureDimension(List<BaseFeature> baseFeatureList) {

        HashMap<String, BaseFeatureConfigDimensionList> baseFeatureMap = new HashMap<>();

        for (BaseFeature baseFeature : baseFeatureList) {
            String featureDimension = baseFeature.getFeatureDimension();
            if (baseFeatureMap.containsKey(featureDimension)) {
                baseFeatureMap.get(featureDimension).add(baseFeature);
            } else {
                BaseFeatureConfigDimensionList featureConfigRowList = new BaseFeatureConfigDimensionList();
                if (featureDimension.contains("{")) {
                    featureConfigRowList.setDynamicDimensionTrue(featureDimension);
                }
                featureConfigRowList.setMultiValueDimension(baseFeature.getMultiValueDimension());
                featureConfigRowList.add(baseFeature);
                baseFeatureMap.put(featureDimension, featureConfigRowList);
            }
        }
        return baseFeatureMap;
    }

    /**
     * 初始化特征维度
     * @param orderDetailData
     * @return
     */
    private DefaultContext<String, Object> buildDimensionData(OrderDetailData orderDetailData) {

        DefaultContext<String, Object> deminsionResult = new DefaultContext<String, Object>();
        try {
            String accountType = orderDetailData.getAccountType();
            // 由于是必要特征,所以作为维度特征保证可靠性
            deminsionResult.put(FieldConstant.ACCOUNT_TYPE, accountType);
            deminsionResult.put(FieldConstant.APPOINT_TYPE, orderDetailData.getAppointType());
            deminsionResult.put(FieldConstant.ORDERTEC_SNAPSHOP, orderDetailData.getOrderTechniques());

            //业务线ID
            deminsionResult.put(FieldConstant.BUSINESS_LINE_ID, orderDetailData.getBusinessLineId());
            deminsionResult.put(FieldConstant.BUSINESS_LINE_TYPE, orderDetailData.getBusinessLineId() != null && orderDetailData.getBusinessLineId() == 1 ? MasterSourceType.TOB.code : MasterSourceType.TOC.code);

            deminsionResult.put(FieldConstant.EXPECT_COMPLETE_TIME, orderDetailData.getExpectCompleteTime());
            deminsionResult.put(FieldConstant.EXPECT_DOORIN_START_TIME, orderDetailData.getExpectDoorInStartDate());


            deminsionResult.put(FieldConstant.TODAY_TIME, DateFormatterUtil.getNow1());

            deminsionResult.put(FieldConstant.MASTER_ORDER_ID, orderDetailData.getMasterOrderId());
            //sign
            deminsionResult.put(FieldConstant.CATEGORY_ID, orderDetailData.getOrderCategoryId());
            Long globalOrderTraceId = orderDetailData.getGlobalOrderId();
            deminsionResult.put(FieldConstant.GLOBAL_ORDER_ID, globalOrderTraceId);
            deminsionResult.put(FieldConstant.TIMER_FLAG, orderDetailData.getTimerFlag());

            deminsionResult.put(FieldConstant.SECOND_DIVISION_ID, orderDetailData.getSecondDivisionId());
            deminsionResult.put(FieldConstant.THIRD_DIVISION_ID, orderDetailData.getThirdDivisionId());
            deminsionResult.put(FieldConstant.FOURTH_DIVISION_ID, orderDetailData.getFourthDivisionId());

            deminsionResult.put(FieldConstant.ORDER_SERVE_TYPE, orderDetailData.getOrderServeType());
            deminsionResult.put(FieldConstant.LV1_SERVE_ID, orderDetailData.getLv1ServeIds());
            deminsionResult.put(FieldConstant.LV2_SERVE_IDS, orderDetailData.getLv2ServeIdList());
            deminsionResult.put(FieldConstant.LV3_SERVE_IDS, orderDetailData.getLv3ServeIdList());
            deminsionResult.put(FieldConstant.ORDER_LNG_LAT,orderDetailData.getOrderLngLat());
            deminsionResult.put(FieldConstant.ORDER_FROM,getOrderFrom(orderDetailData));
            deminsionResult.put(FieldConstant.TIME_LINESS_TAG,getTimeLinessTag(orderDetailData));
            deminsionResult.put(FieldConstant.VALUE_ADDED_SERVICE, getValueAddedServiceTag(orderDetailData));
            deminsionResult.put(FieldConstant.CUSTOMER_PHONE,orderDetailData.getCustomerPhone());

            List<Long> goodsLevel2Ids = Arrays.stream(Optional.ofNullable(orderDetailData.getParentGoodsIds())
                    .orElse("0").split(",")).map(Long::parseLong)
                    .collect(Collectors.toList());
            deminsionResult.put(FieldConstant.GOODS_LEVEL_2_IDS, goodsLevel2Ids);

            if (orderDetailData.getOrderTechniqueSet().size()==1) {
                String tecString=orderDetailData.getOrderTechniqueSet().iterator().next();
                String[] stringArray=tecString.split(",");
//				Arrays.sort(stringArray);//字符串排序
                Arrays.sort(stringArray,new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        return Double.valueOf(o1)-Double.valueOf(o2)>0?1:-1;
                    }
                });
                deminsionResult.put(FieldConstant.SINGLE_ORDER_TECHNOLOGYS_IN_DEMAND
                        ,String.join(",", stringArray));
            }
            int goodsSize=orderDetailData.getParentGoodsIdsSet().size();
            deminsionResult.put(FieldConstant.GOODS_NUM, orderDetailData.getGoodsNum());

            if (orderDetailData.getChildGoodsIdsSet()!=null&&goodsSize == 1) {
                deminsionResult.put(FieldConstant.GOODS_PARENT_ID,
                        NumberUtils.toLong(orderDetailData.getParentGoodsIds(), 0));
                deminsionResult.put(FieldConstant.GOODS_CHILD_ID,
                        NumberUtils.toLong(orderDetailData.getChildGoodsIds(), 0));
            }

            deminsionResult.put(FieldConstant.ALL_GOODS_PARENT_ID, orderDetailData.getParentGoodsIds());

            if (FieldConstant.ENTERPRISE.equals(accountType)) {
                deminsionResult.put(FieldConstant.USER_ID, orderDetailData.getUserId());
                deminsionResult.put(FieldConstant.ENTERPRISE_ID, orderDetailData.getAccountId());
            } else {
                deminsionResult.put(FieldConstant.USER_ID, orderDetailData.getAccountId());
            }


            deminsionResult.put(FieldConstant.TEAM_MASTER_ORDER_PUSH,teamMasterPushOrder(orderDetailData));

            deminsionResult.put(FieldConstant.CANCEL_APPOINT,orderDetailData.getCancelAppoint());
            deminsionResult.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderDetailData.getExpectDoorInStartTimeString());

            deminsionResult.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderDetailData.getExpectDoorInStartTimeString());
            deminsionResult.put(FieldConstant.DATE, DateFormatterUtil.getNow());
            deminsionResult.put(FieldConstant.YESTERDAY, DateFormatterUtil.getAssignDay(-1));

        } catch (Exception e) {
            log.error(String.format("buildDimensionData error,orderDetailData:%s", JSON.toJSONString(orderDetailData)),e);
        }
        log.info("{},deminsion:{}",orderDetailData.getMasterOrderId(),deminsionResult);
        return deminsionResult;
    }

    private int teamMasterPushOrder(OrderDetailData orderDetailData) {
        try{
            Integer isExclusiveTeamMaster = orderDetailData.getIsExclusiveTeamMaster();
            if(isExclusiveTeamMaster != null && isExclusiveTeamMaster == 1){
                return 0;
            }
            String orderFrom = orderDetailData.getOrderFrom();
            List<String> baseServe = Arrays.asList("1003106,2003106,3003106,4003106,1003133,2003133,3003133,4003133,1133144,2133144,3133144,4133144,1193144,2193144,3193144,4193144,1203144,2203144,3203144,4203144,1143144,2143144,3143144,4143144,1153144,2153144,3153144,4153144".split(","));
            String[] serveArray = Optional.ofNullable(orderDetailData.getLv3ServeIds()).orElse("").split(",");
            List<String> baseServeTypes = Arrays.asList("1,2,3,4,15".split(","));

            Long categoryId = orderDetailData.getOrderCategoryId();
            Integer serveType = orderDetailData.getOrderServeType();

            BigDecimal goodsNum = new BigDecimal(orderDetailData.getGoodsNum());

            boolean conditionOne = "site".equals(orderFrom) && Stream.of(serveArray).anyMatch(baseServe::contains);
            boolean conditionTwo = "site".equals(orderFrom)
                    && Objects.equals(categoryId, 1L)
                    && baseServeTypes.contains(String.valueOf(serveType))
                    && goodsNum.compareTo(BigDecimal.TEN) > 0;

            return (conditionOne || conditionTwo) ? 1 : 0;
        }catch(Exception e){
            log.error(String.format("计算推单师傅推单失败,orderDetailData:%s",JSON.toJSONString(orderDetailData)),e);
        }

        return 0;
    }

    private String getOrderFrom(OrderDetailData orderDetailData){
        String orderFrom = orderDetailData.getOrderFrom();
        if("user".equals(orderDetailData.getAccountType())){
            if("site".equals(orderFrom) || "thirdpart".equals(orderFrom)){
                return "site";
            }else{
                return "family";
            }
        }else if("enterprise".equals(orderDetailData.getAccountType())){
            if("site".equals(orderFrom) || "applet".equals(orderFrom)){
                return "enterprise_inside";
            }else{
                return "enterprise_outside";
            }
        }

        return "";


    }


    private List<String> getTimeLinessTag(OrderDetailData orderDetailData){
        List<String> timeLinessTagList = new ArrayList<>();

        if(org.apache.commons.lang.StringUtils.isBlank(orderDetailData.getExpectDoorInStartDate())){
            return timeLinessTagList;
        }

        if(orderDetailData.getTimerFlag() != null && orderDetailData.getTimerFlag() == 1){
            timeLinessTagList.add("regular_time_order");
        }

        if(orderDetailData.getEmergencyOrderFlag() != null && orderDetailData.getEmergencyOrderFlag() == 1){
            timeLinessTagList.add("emergency_order");
        }

        if(orderDetailData.getOnTimeOrderFlag() != null && orderDetailData.getOnTimeOrderFlag() == 1){
            timeLinessTagList.add("on_time_order");
        }

        if(org.apache.commons.lang.StringUtils.isNotBlank(orderDetailData.getExpectDoorInStartDate())){
            timeLinessTagList.add("expect_time_order");
        }

        return timeLinessTagList;
    }

    private List<String> getValueAddedServiceTag(OrderDetailData orderDetailData){
        List<String> valueAddedServiceTagList = new ArrayList<>();

        //加急单
        if(orderDetailData.getEmergencyOrderFlag() != null && orderDetailData.getEmergencyOrderFlag() == 1){
            valueAddedServiceTagList.add("emergency_order");
        }

        //旧件寄回
        if(orderDetailData.getIsSendBackOld() != null && orderDetailData.getIsSendBackOld() == 1){
            valueAddedServiceTagList.add("is_send_back_old");
        }

        //拆旧服务
        if (!Strings.isNullOrEmpty(orderDetailData.getDemolishType()) &&
                ("useall".equals(orderDetailData.getDemolishType()) || "usepart".equals(orderDetailData.getDemolishType()))) {
            valueAddedServiceTagList.add("demolish");
        }

        //需师傅带配件
        if (orderDetailData.getIsParts() != null && orderDetailData.getIsParts() == 1) {
            valueAddedServiceTagList.add("is_parts");
        }

        //有当日装服务
        if (CollectionUtil.isNotEmpty(orderDetailData.getOrderTags()) && orderDetailData.getOrderTags().contains("same_day_outfit")) {
            valueAddedServiceTagList.add("same_day_outfit");
        }


        return valueAddedServiceTagList;
    }



    /**
     * 预约冲突师傅查询 50个师傅调用一次接口
     */
    private void reserveCustomerPeriodConflictMaster(
            Set<String> masterSet,
            DefaultContext<String, Object> orderFeatures,
            DefaultContext<String, DefaultContext<String, Object>> masterFeatures
    ){
        try {
            final Object doorStart = orderFeatures.get(FieldConstant.EXPECT_DOOR_IN_START_DATE);
            final Object doorEnd = orderFeatures.get(FieldConstant.EXPECT_DOOR_IN_END_DATE);
            if (doorStart==null||doorEnd==null) {
                masterSet.forEach(masterId -> {
                    DefaultContext<String, Object> featureContext = masterFeatures.get(masterId);
                    if(featureContext != null){
                        featureContext.put("master_time_conflict", 0);
                    }
                });
                return;
            }
            Date startTime=(Date)doorStart ;
            Date endTime=(Date)doorEnd;

            final List<Long> reserveCustomerConflictMaster = batchGetReserveCustomerPeriod(masterSet, startTime, endTime, 50);
            for (String masterId : masterSet) {
                LoCollectionsUtil.putToMapValue(
                        masterFeatures,masterId
                        ,"master_time_conflict", reserveCustomerConflictMaster.contains(Long.valueOf(masterId)) ? 1 : 0
                );
            }
        }catch (Exception e){
            log.warn("ReserveCustomerPeriodConflictMaster:{}",e);
        }
    }


    @Resource
    private RecruitMasterApi recruitMasterApi;


    /**
     * 新合约请假状态
     */
    private void newContractRestStatus(
            Set<String> masterSet,
            DefaultContext<String, DefaultContext<String, Object>> masterFeatures
    ){
        try {


            for (String masterId : masterSet) {
                GetMasterCurrentLeaveRequestRqt rqt = new GetMasterCurrentLeaveRequestRqt();
                rqt.setMasterId(Long.valueOf(masterId));
                rqt.setNowTime(new Date());
                rqt.setSceneId(4L);
                GetMasterCurrentLeaveRequestResp resp = recruitMasterApi.getMasterCurrentLeaveRequest(rqt);
                Integer restStatus = Objects.isNull(resp) ? 0 : Objects.isNull(resp.getLeaveRequestStartTime()) ? 0 : 1;

                LoCollectionsUtil.putToMapValue(
                        masterFeatures,masterId
                        ,"new_contract_rest_status", restStatus
                );
            }
        }catch (Exception e){
            log.warn("newContractRestStatus:{}",e);
        }
    }



    private void getMasterFeatures( Set<String> masterSet,
                                    DefaultContext<String, Object> orderFeatures,
                                    DefaultContext<String, DefaultContext<String, Object>> masterFeatures){

        try{
            List<List<String>> masterBatchList = LocalCollectionsUtil.groupByBatch(masterSet,10);
            for(List<String> masterBatch : masterBatchList){
                GetMasterInfoListByIdsRqt rqt = new GetMasterInfoListByIdsRqt();
                List<Long> masterIdList = masterBatch.stream().map(Long::parseLong).collect(Collectors.toList());
                rqt.setMasterIds(masterIdList);
                List<GetMasterInfoListByIdsResp> respList = commonQueryServiceApi.getMasterInfoListByIds(rqt);
                if(CollectionUtils.isNotEmpty(respList)){
                    respList.forEach(resp -> {
                        Map<String,Object> masterFeatureMap = masterFeatures.get(String.valueOf(resp.getMasterId()));
//                map.put("rest_state",resp.getRestState());
//                map.put("lng_lat",resp.getLocationLongitude() + "," + resp.getLocationLatitude());
//                map.put("master_current_location",resp.getCurrentLongitude() + "," + resp.getCurrentLatitude());
                        masterFeatureMap.put("account_status","on".equals(resp.getStatus()) ? 0 : 1);
                        long freezingRecoverTime = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
                        masterFeatureMap.put("account_freeze_status", (resp.getFreezeTime() != null && resp.getFreezeTime() > 0 && resp.getFreezeTime() > freezingRecoverTime) ? 1 : 0);
                    });
                }
            }
        }catch(Exception e){
            log.info("getMasterFeatures error",e);
        }

    }




    private void calculateOrderMasterNavigationalDistance( Set<String> masterSet,
                                    DefaultContext<String, Object> orderFeatures,
                                    DefaultContext<String, DefaultContext<String, Object>> masterFeatures){


        try{

            String orderLngLat = (String)orderFeatures.get(FieldConstant.ORDER_LNG_LAT);
           

            masterFeatures.keySet().forEach(masterId -> {

                DefaultContext<String,Object> masterFeatureContext = masterFeatures.get(masterId);

                if(StringUtils.isBlank(orderLngLat)){
                    masterFeatureContext.put("order_master_navigational_distance",BigDecimal.ZERO);
                    return ;
                }

                String masterLngLat = (String)masterFeatureContext.get("lng_lat");
                if(StringUtils.isBlank(masterLngLat)){
                    masterFeatureContext.put("order_master_navigational_distance",BigDecimal.ZERO);
                    return ;
                }

                Double distance = navigationalDistanceCalculateService.calculate(masterLngLat,orderLngLat);
                masterFeatureContext.put("order_master_navigational_distance", new BigDecimal(distance).divide(new BigDecimal(1000)).setScale(2, RoundingMode.HALF_UP));

            });

        }catch(Exception e){
            log.info("getOrderMasterNavigationalDistance error",e);
        }

    }



    public void getWeekGrabOrAppointCnt( Set<String> masterSet, DefaultContext<String, DefaultContext<String, Object>> masterFeatures){
        try{

            String masterIds = String.join(",",masterSet);
            GetMstCooperativeBusinessOrderStatRqt rqt = new GetMstCooperativeBusinessOrderStatRqt();
            rqt.setMasterIdList(masterIds);
            List<GetMstCooperativeBusinessOrderStatResp> respList = bigdataOpenServiceApi.mstCooperativeBusinessOrderStat(rqt);

            log.info(String.format("mstCooperativeBusinessOrderStat,rqt:%s,resp:%s",JSON.toJSONString(rqt),JSON.toJSONString(respList)));


//            List<GetMstCooperativeBusinessOrderStatResp> respList = new ArrayList<>();
//
//            GetMstCooperativeBusinessOrderStatResp resp1 = new GetMstCooperativeBusinessOrderStatResp();
//            resp1.setMasterId("***********");
//            resp1.setMstOrderCancelCntCurrWeek(10);
//            resp1.setMstGrabNotCancelCntCurrWeek(20);
//            resp1.setMstGrabTotalCntCurrWeek(30);
//            respList.add(resp1);
//
//
//
//            GetMstCooperativeBusinessOrderStatResp resp2 = new GetMstCooperativeBusinessOrderStatResp();
//            resp2.setMasterId("6098176290");
//            resp2.setMstGrabNotCancelCntCurrWeek(40);
//            resp2.setMstOrderPayCntCurrWeek(30);
//            respList.add(resp2);
//
//            GetMstCooperativeBusinessOrderStatResp resp3 = new GetMstCooperativeBusinessOrderStatResp();
//            resp3.setMasterId("5246634710");
//            resp3.setMstOrderCancelCntCurrWeek(70);
//            resp3.setMstGrabNotCancelCntCurrWeek(80);
//            resp3.setMstGrabTotalCntCurrWeek(90);
//            respList.add(resp3);







            Integer minWeekGrabCnt = null;
            String minWeekGrabCntMasterId = null;


            Integer minWeekAppointCnt = null;
            String minWeekAppointCntMasterId = null;

            if(CollectionUtils.isNotEmpty(respList)){
                minWeekGrabCnt = respList.get(0).getMstGrabNotCancelCntCurrWeek();
                minWeekGrabCntMasterId = respList.get(0).getMasterId();

                minWeekAppointCnt = respList.get(0).getMstOrderPayCntCurrWeek();
                minWeekAppointCntMasterId = respList.get(0).getMasterId();

                for(GetMstCooperativeBusinessOrderStatResp resp : respList){
                    if(resp.getMstGrabNotCancelCntCurrWeek() < minWeekGrabCnt){
                        minWeekGrabCnt = resp.getMstGrabNotCancelCntCurrWeek();
                        minWeekGrabCntMasterId = resp.getMasterId();
                    }

                    if(resp.getMstOrderPayCntCurrWeek() < minWeekAppointCnt){
                        minWeekAppointCnt = resp.getMstOrderPayCntCurrWeek();
                        minWeekAppointCntMasterId = resp.getMasterId();
                    }
                }
            }

            Map<String,GetMstCooperativeBusinessOrderStatResp> respMap = CollectionUtils.isNotEmpty(respList) ? respList.stream().collect(Collectors.toMap(GetMstCooperativeBusinessOrderStatResp::getMasterId, resp -> resp)) : new HashMap<>();

            masterFeatures.keySet().forEach(currentMasterId -> {

            });

            for(String currentMasterId : masterFeatures.keySet()){
                DefaultContext<String,Object> masterFeatureContext = masterFeatures.get(currentMasterId);
                GetMstCooperativeBusinessOrderStatResp resp = respMap.get(currentMasterId);
                masterFeatureContext.put("week_grab_cnt", (Objects.nonNull(resp) && Objects.nonNull(resp.getMstGrabNotCancelCntCurrWeek())) ? resp.getMstGrabNotCancelCntCurrWeek() : 0);
                masterFeatureContext.put("week_appoint_cnt", (Objects.nonNull(resp) && Objects.nonNull(resp.getMstOrderPayCntCurrWeek())) ? resp.getMstOrderPayCntCurrWeek() : 0);
                masterFeatureContext.put("min_week_grab_cnt", (StringUtils.isNotBlank(minWeekGrabCntMasterId) && currentMasterId.equals(minWeekGrabCntMasterId)) ? 1 : 0);
                masterFeatureContext.put("min_week_appoint_cnt", (StringUtils.isNotBlank(minWeekAppointCntMasterId) && currentMasterId.equals(minWeekAppointCntMasterId)) ? 1 : 0);
            }

        }catch(Exception e){
            log.info("getWeekGrabOrAppointCnt error",e);
        }

    }



    public void getFullTimeWeekGrabOrAppointCnt( Set<String> masterSet, DefaultContext<String, DefaultContext<String, Object>> masterFeatures){
        try{

            List<AllTimeOrderMstStatResp> respList = new ArrayList<>();
            List<List<String>> batchList = LocalCollectionsUtil.groupByBatch(masterSet,allTimeMasterBatchSize);
            for(List<String> batch : batchList){
                String masterIds = String.join(",",batch);
                AllTimeOrderMstStatRqt rqt = new AllTimeOrderMstStatRqt();
                rqt.setMasterIdList(masterIds);
                List<AllTimeOrderMstStatResp> allTimeOrderMstStatRespList = bigdataOpenServiceApi.allTimeOrderMstStat(rqt);
                log.info(String.format("allTimeOrderMstStat,rqt:%s,resp:%s",JSON.toJSONString(rqt),JSON.toJSONString(allTimeOrderMstStatRespList)));
                if(CollectionUtils.isNotEmpty(allTimeOrderMstStatRespList)){
                    respList.addAll(allTimeOrderMstStatRespList);
                }
            }



            Integer minWeekGrabCnt = null;
            String minWeekGrabCntMasterId = null;


            Integer minWeekAppointCnt = null;
            String minWeekAppointCntMasterId = null;

            if(CollectionUtils.isNotEmpty(respList)){
                minWeekGrabCnt = respList.get(0).getMstGrabNotCancelCntCurrWeek();
                minWeekGrabCntMasterId = respList.get(0).getMasterId();

                minWeekAppointCnt = respList.get(0).getMstOrderPayCntCurrWeek();
                minWeekAppointCntMasterId = respList.get(0).getMasterId();

                for(AllTimeOrderMstStatResp resp : respList){
                    if(resp.getMstGrabNotCancelCntCurrWeek() < minWeekGrabCnt){
                        minWeekGrabCnt = resp.getMstGrabNotCancelCntCurrWeek();
                        minWeekGrabCntMasterId = resp.getMasterId();
                    }

                    if(resp.getMstOrderPayCntCurrWeek() < minWeekAppointCnt){
                        minWeekAppointCnt = resp.getMstOrderPayCntCurrWeek();
                        minWeekAppointCntMasterId = resp.getMasterId();
                    }
                }
            }

            Map<String,AllTimeOrderMstStatResp> respMap = CollectionUtils.isNotEmpty(respList) ? respList.stream().collect(Collectors.toMap(AllTimeOrderMstStatResp::getMasterId, resp -> resp)) : new HashMap<>();


            for(String currentMasterId : masterFeatures.keySet()){
                DefaultContext<String,Object> masterFeatureContext = masterFeatures.get(currentMasterId);
                AllTimeOrderMstStatResp resp = respMap.get(currentMasterId);
                masterFeatureContext.put("full_time_master_daily_grab_cnt", (Objects.nonNull(resp) && Objects.nonNull(resp.getMstGrabNotCancelCntCurrDay())) ? resp.getMstGrabNotCancelCntCurrDay() : 0);
                masterFeatureContext.put("full_time_master_daily_appoint_cnt", (Objects.nonNull(resp) && Objects.nonNull(resp.getMstOrderPayCntCurrDay())) ? resp.getMstOrderPayCntCurrDay() : 0);
                masterFeatureContext.put("full_time_master_week_grab_cnt", (Objects.nonNull(resp) && Objects.nonNull(resp.getMstGrabNotCancelCntCurrWeek())) ? resp.getMstGrabNotCancelCntCurrWeek() : 0);
                masterFeatureContext.put("full_time_master_week_appoint_cnt", (Objects.nonNull(resp) && Objects.nonNull(resp.getMstOrderPayCntCurrWeek())) ? resp.getMstOrderPayCntCurrWeek() : 0);

                masterFeatureContext.put("full_time_master_min_week_grab_cnt", (StringUtils.isNotBlank(minWeekGrabCntMasterId) && currentMasterId.equals(minWeekGrabCntMasterId)) ? 1 : 0);
                masterFeatureContext.put("full_time_master_min_week_appoint_cnt", (StringUtils.isNotBlank(minWeekAppointCntMasterId) && currentMasterId.equals(minWeekAppointCntMasterId)) ? 1 : 0);
//                masterFeatureContext.put("full_time_master_min_daily_grab_cnt", (StringUtils.isNotBlank(minWeekGrabCntMasterId) && currentMasterId.equals(minWeekGrabCntMasterId)) ? 1 : 0);
//                masterFeatureContext.put("full_time_master_min_daily_appoint_cnt", (StringUtils.isNotBlank(minWeekAppointCntMasterId) && currentMasterId.equals(minWeekAppointCntMasterId)) ? 1 : 0);


            }

        }catch(Exception e){
            log.info("getFullTimeWeekGrabOrAppointCnt error",e);
        }

    }



    public void getMaxLastGrabTime( Set<String> masterSet, DefaultContext<String, DefaultContext<String, Object>> masterFeatures) {

        try {


            Map<String, Map<String, String>> resultMap = hBaseClient.batchQuery("mst_buss_extra", "master_id", new ArrayList<>(masterSet), Collections.singletonList("cooperation_business_last_grab_time"));

            String masterId = null;
            if(!resultMap.isEmpty()){
                masterId = resultMap.keySet().iterator().next();
                Date lastGrabTime = DateUtils.parseDate(resultMap.get(masterId).get("cooperation_business_last_grab_time"));

                for (String currentMasterId : resultMap.keySet()) {
                    Date grabTime = DateUtils.parseDate(resultMap.get(currentMasterId).get("cooperation_business_last_grab_time"));
                    if (grabTime.compareTo(lastGrabTime) < 0) {
                        masterId = currentMasterId;
                        lastGrabTime = grabTime;
                    }
                }
            }


            String finalMasterId = masterId;
            masterFeatures.keySet().forEach(currentMasterId -> {
                DefaultContext<String, Object> masterFeatureContext = masterFeatures.get(currentMasterId);
                masterFeatureContext.put("max_last_grab_time", Objects.nonNull(finalMasterId) && finalMasterId.equals(currentMasterId) ? 1 : 0);
            });

        } catch (Exception e) {
            log.info("getMaxLastGrabTime error", e);
        }
    }


    public void getMaxLastAppointTime( Set<String> masterSet, DefaultContext<String, DefaultContext<String, Object>> masterFeatures) {

        try {


            Map<String, Map<String, String>> resultMap = hBaseClient.batchQuery("mst_buss_extra", "master_id", new ArrayList<>(masterSet), Collections.singletonList("cooperation_business_last_appoint_time"));

            String masterId = null;
            if(!resultMap.isEmpty()){
                masterId = resultMap.keySet().iterator().next();
                Date lastAppointTime = DateUtils.parseDate(resultMap.get(masterId).get("cooperation_business_last_appoint_time"));

                for (String currentMasterId : resultMap.keySet()) {
                    Date appointTime = DateUtils.parseDate(resultMap.get(currentMasterId).get("cooperation_business_last_appoint_time"));
                    if (appointTime.compareTo(lastAppointTime) < 0) {
                        masterId = currentMasterId;
                        lastAppointTime = appointTime;
                    }
                }
            }


            String finalMasterId = masterId;
            masterFeatures.keySet().forEach(currentMasterId -> {
                DefaultContext<String, Object> masterFeatureContext = masterFeatures.get(currentMasterId);
                masterFeatureContext.put("max_last_appoint_time", Objects.nonNull(finalMasterId) && finalMasterId.equals(currentMasterId) ? 1 : 0);
            });

        } catch (Exception e) {
            log.info("getMaxLastGrabTime error", e);
        }
    }



    public void getFullTimeMasterMaxLastAppointTime( Set<String> masterSet, DefaultContext<String, DefaultContext<String, Object>> masterFeatures) {

        try {


            Map<String, Map<String, String>> resultMap = hBaseClient.batchQuery("mst_buss_extra", "master_id", new ArrayList<>(masterSet), Collections.singletonList("full_time_master_last_appoint_time"));

            String masterId = null;
            if(!resultMap.isEmpty()){
                masterId = resultMap.keySet().iterator().next();
                Date lastAppointTime = DateUtils.parseDate(resultMap.get(masterId).get("full_time_master_last_appoint_time"));

                for (String currentMasterId : resultMap.keySet()) {
                    Date appointTime = DateUtils.parseDate(resultMap.get(currentMasterId).get("full_time_master_last_appoint_time"));
                    if (appointTime.compareTo(lastAppointTime) < 0) {
                        masterId = currentMasterId;
                        lastAppointTime = appointTime;
                    }
                }
            }


            String finalMasterId = masterId;
            masterFeatures.keySet().forEach(currentMasterId -> {
                DefaultContext<String, Object> masterFeatureContext = masterFeatures.get(currentMasterId);
                masterFeatureContext.put("full_time_master_max_last_appoint_time", Objects.nonNull(finalMasterId) && finalMasterId.equals(currentMasterId) ? 1 : 0);
            });

        } catch (Exception e) {
            log.info("getFullTimeMasterMaxLastAppointTime error", e);
        }
    }




}
