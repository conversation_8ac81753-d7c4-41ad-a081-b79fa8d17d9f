package com.wanshifu.master.order.push.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.AgentDistributeStrategy;
import com.wanshifu.master.order.push.domain.po.AgentInfo;
import com.wanshifu.master.order.push.domain.resp.agent.AgentDistributeDetailResp;
import com.wanshifu.master.order.push.domain.rqt.agent.*;

/**
 * <AUTHOR>
 * @date 2025/2/26 19:40
 */
public interface AgentDistributeStrategyService {

    Integer add(AddAgentDistributeStrategyRqt rqt);


    Integer update(UpdateAgentDistributeStrategyRqt rqt);

    Integer enable(EnableAgentDistributeStrategyRqt rqt);

    AgentDistributeDetailResp detail(AgentDistributeStrategyDetailRqt rqt);

    SimplePageInfo<AgentInfo> getAgentList(GetAgentListRqt rqt);

    SimplePageInfo<AgentDistributeStrategy> list(GetAgentDistributeStrategyListRqt rqt);


    Integer delete(DeleteAgentDistributeStrategyRqt rqt);
}
