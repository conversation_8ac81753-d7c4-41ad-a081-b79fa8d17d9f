package com.wanshifu.master.order.push.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ql.util.express.DefaultContext;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.enterprise.order.api.InfoQueryApi;
import com.wanshifu.enterprise.order.domain.infoQuery.api.request.OrderGoodsInfoRqt;
import com.wanshifu.enterprise.order.domain.infoQuery.api.response.OrderGoodsInfoResp;
import com.wanshifu.enterprise.order.domain.po.OrderServiceAttributeInfo;
import com.wanshifu.fee.center.api.FeeRuleApi;
import com.wanshifu.fee.center.domain.dto.AccountInfo;
import com.wanshifu.fee.center.domain.dto.AddressInfo;
import com.wanshifu.fee.center.domain.dto.CalculateServiceInfo;
import com.wanshifu.fee.center.domain.enums.DivisionTypeEnum;
import com.wanshifu.fee.center.domain.enums.SceneCodeEnum;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateBatchReq;
import com.wanshifu.fee.center.domain.request.ApplyOrderCalculateReq;
import com.wanshifu.fee.center.domain.request.feeRule.BizRuleBatchReq;
import com.wanshifu.fee.center.domain.response.ApplyOrderCalculateBatchResp;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.master.order.push.domain.common.MasterMatchCondition;
import com.wanshifu.master.order.push.domain.common.MatchSupportMasterRqt;
import com.wanshifu.master.order.push.domain.common.PushFeature;
import com.wanshifu.master.order.push.domain.constant.FieldConstant;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.enums.*;
import com.wanshifu.master.order.push.domain.message.EnterpriseAppointMessage;
import com.wanshifu.master.order.push.domain.message.EnterpriseMatchAgreementMasterResultMessage;
import com.wanshifu.master.order.push.domain.po.*;
import com.wanshifu.master.order.push.domain.rqt.OrderDetailData;
import com.wanshifu.master.order.push.domain.rqt.orderscoringstrategy.OrderScoringStrategyDetailRqt;
import com.wanshifu.master.order.push.domain.rqt.orderselectstrategy.OrderSelectStrategyDetailRqt;
import com.wanshifu.master.order.push.repository.*;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.order.offer.domains.enums.AccountType;
import com.wanshifu.order.offer.domains.enums.AppointType;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EnterpriseAppointMasterServiceImpl implements EnterpriseAppointMasterService {


    @Resource
    private Tools tools;


    @Resource
    private InfoQueryApi infoQueryApi;


    @Resource
    private PushQueueService pushQueueService;


    @Resource
    private OrderRoutingStrategyRepository orderRoutingStrategyRepository;

    @Resource
    private OrderMatchRoutingRepository orderMatchRoutingRepository;


    @Resource
    private CompensateDistributeRepository compensateDistributeRepository;


    @Resource
    private DistributeFactory distributeFactory;

    @Resource
    private OrderSelectStrategyService orderSelectStrategyService;

    @Resource
    private OrderScoringStrategyService orderScoringStrategyService;


    @Resource
    private AddressApi addressApi;

    @Resource
    private FeeRuleApi feeRuleApi;

    @Resource
    private HBaseClient hBaseClient;



    @Resource
    private OrderDataBuilder orderDataBuilder;


    @Value("${enterprise.match.agreementMaster.switch:on}")
    private String enterpriseMatchAgreementMasterSwitch;


    @Resource
    private PushProgressRepository pushProgressRepository;

    @Resource
    private FeatureRepository featureRepository;



    @Value("${master.feature.query.switch:on}")
    private String masterFeatureQuerySwitch;


    @Value("${enterprise.nearby.appoint.switch:on}")
    private String enterpriseNearbyAppointSwitch;


    @Resource
    private ApolloConfigUtils apolloConfigUtils;




    private List<String> getTimeLinessTag(OrderDetailData orderDetailData){

        List<String> timeLinessTagList = new ArrayList<>();


        if(orderDetailData.getTimerFlag() != null && orderDetailData.getTimerFlag() == 1){
            timeLinessTagList.add("regular_time_order");
        }

        if(orderDetailData.getEmergencyOrderFlag() != null && orderDetailData.getEmergencyOrderFlag() == 1){
            timeLinessTagList.add("emergency_order");
        }

        if(orderDetailData.getOnTimeOrderFlag() != null && orderDetailData.getOnTimeOrderFlag() == 1){
            timeLinessTagList.add("on_time_order");
        }

        if(orderDetailData.getExpectDoorInStartDate() != null){
            timeLinessTagList.add("expect_time_order");
        }

        return timeLinessTagList;
    }


    private DefaultContext<String,Object> initOrderFeatures(OrderDetailData orderDetailData){
        DefaultContext<String, Object> orderFeatures=new DefaultContext<>();
        orderFeatures.put("account_id",orderDetailData.getAccountId());
        orderFeatures.put("business_line_id",orderDetailData.getBusinessLineId());
        orderFeatures.put("category_id",orderDetailData.getOrderCategoryId());
        orderFeatures.put("appoint_type",orderDetailData.getAppointType());


//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());

        //TODO 校验经纬度先后顺序
        orderFeatures.put("order_lng_lat", orderDetailData.getOrderLngLat());
        orderFeatures.put("second_division_id", orderDetailData.getSecondDivisionId());
        orderFeatures.put("third_division_id", orderDetailData.getThirdDivisionId());
        orderFeatures.put("lv1_serve_id",orderDetailData.getLv1ServeIds());
        orderFeatures.put("lv2_serve_ids", orderDetailData.getLv2ServeIdList());
        orderFeatures.put("lv3_serve_ids",orderDetailData.getLv3ServeIdList());


//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE,orderExtraData.getExpectDoorInStartDate());
//        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE,orderExtraData.getExpectDoorInEndDate());


        //订单包：如果是总包订单,需要把总包下单用户id传给智能推单 V7.7.2
//        orderFeatures.put("user_id",orderDetailData.getUserId());

        orderFeatures.put(FieldConstant.ORDER_FROM,orderDetailData.getOrderFrom());

        orderFeatures.put(FieldConstant.TIME_LINESS_TAG,getTimeLinessTag(orderDetailData));

        orderFeatures.put(FieldConstant.CUSTOMER_PHONE,orderDetailData.getCustomerPhone());
        orderFeatures.put(FieldConstant.BUSINESS_LINE_TYPE, orderDetailData.getBusinessLineId() != null && orderDetailData.getBusinessLineId() == 1 ? MasterSourceType.TOB.code : MasterSourceType.TOC.code);
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_START_DATE, orderDetailData.getExpectDoorInStartTimeString());
        orderFeatures.put(FieldConstant.EXPECT_DOOR_IN_END_DATE, orderDetailData.getExpectDoorInEndTimeString());

        if (AccountType.ENTERPRISE.code.equals(orderDetailData.getAccountType())) {
            orderFeatures.put(FieldConstant.USER_ID, orderDetailData.getUserId());
            orderFeatures.put(FieldConstant.ENTERPRISE_ID, orderDetailData.getAccountId());
        } else {
            orderFeatures.put(FieldConstant.USER_ID, orderDetailData.getAccountId());
        }

        Long userId = (Long)orderFeatures.get(FieldConstant.USER_ID);

        if(Objects.nonNull(userId) && userId > 0){
            final String groupIds = hBaseClient.querySingle("usr_groups_stat", String.valueOf(userId),"group_ids");
            if (StringUtils.isNotBlank(groupIds)) {
                final List<Integer> groupIdList = Arrays.asList(
                        groupIds.split(","))
                        .stream().map(row -> Integer.valueOf(row)).collect(Collectors.toList());
                orderFeatures.put("appoint_user",groupIdList);
                orderFeatures.put("user_group",groupIdList);
            }else{
                orderFeatures.put("appoint_user",new ArrayList<>());
                orderFeatures.put("user_group",new ArrayList<>());
            }
        }else{
            orderFeatures.put("appoint_user",new ArrayList<>());
            orderFeatures.put("user_group",new ArrayList<>());
        }


        return orderFeatures;
    }



    @Override
    public Long matchAgreementMaster(EnterpriseAppointMessage message){
        try{
            if(!"on".equals(enterpriseMatchAgreementMasterSwitch)){
                sendNoAgreementMasterResultMessage(message);
                return null;
            }
            Long globalOrderId = message.getGlobalOrderTraceId();
            OrderDetailData orderDetailData = orderDataBuilder.buildEnterpriseOrder(globalOrderId);
            if(Objects.isNull(orderDetailData)){
                sendNoAgreementMasterResultMessage(message);
                return null;
            }

            if(apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())){
                sendNoAgreementMasterResultMessage(message);
                insertAgreementMasterMatch(orderDetailData,"该城市订单不派单");
                return null;
            }

            orderDetailData.getPushExtraData().setOrderPushEliminateMasterIds(message.getExclusiveMasterList());

            log.info("orderDetailData:" + JSON.toJSONString(orderDetailData));


            OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyRepository.selectStrategy("enterprise_appoint",String.valueOf(orderDetailData.getSecondDivisionId()),String.valueOf(orderDetailData.getOrderCategoryId()),
                    orderDetailData.getBusinessLineId() );

            if(Objects.isNull(orderRoutingStrategy)){
                orderRoutingStrategy = orderRoutingStrategyRepository.selectStrategy("enterprise_appoint","all",String.valueOf(orderDetailData.getOrderCategoryId()),(orderDetailData.getBusinessLineId().intValue()));
            }
            if(Objects.isNull(orderRoutingStrategy)){
                insertAgreementMasterMatch(orderDetailData,"无总包直接指派路由策略");
                sendNoAgreementMasterResultMessage(message);
                return null;
            }

            OrderMatchRouting orderMatchRouting = orderMatchRoutingRepository.selectByPrimaryKey(orderRoutingStrategy.getMatchRoutingId());
            if(!"agreement".equals(orderMatchRouting.getLv1MasterType())){
                insertAgreementMasterMatch(orderDetailData,"无总包直接指派匹配路由");
                sendNoAgreementMasterResultMessage(message);
                return null;
            }

            MasterMatchCondition masterMatchCondition = new MasterMatchCondition();
            masterMatchCondition.setThirdDivisionId(orderDetailData.getThirdDivisionId());
            masterMatchCondition.setFourthDivisionId(orderDetailData.getFourthDivisionId());
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.ENTERPRISE_APPOINT);
            MatchMasterResult matchMasterResult = orderMasterMatcher.match(orderDetailData,masterMatchCondition);


            List<AgreementMasterExtra> agreementMasterList = (List<AgreementMasterExtra>)matchMasterResult.getExtraData().get("agreement_master_list");

            DefaultContext<String, Object> orderFeatureContext = initOrderFeatures(orderDetailData);


            //获取策略
            OrderDistributor orderDistributor = distributeFactory
                    .matchDistributor(orderDetailData, orderFeatureContext, DistributeType.ENTERPRISE_APPOINT.getCode());

            log.info("enterpriseAppointAgreementMaster orderDistributor: " +  JSON.toJSONString(orderDistributor));

            if(!orderDistributor.isMatched()){
                insertAgreementMasterMatch(orderDetailData,"未配置总包直接指派调度策略");
                sendNoAgreementMasterResultMessage(message);
                return null;
            }


            List<OrderDistributor.CompensateDistributeDTO> compensateDistributeDTOList = orderDistributor.getCompensateDistributeList();

            OrderDistributor.CompensateDistributeDTO compensateDistributeDTO= null;
            CompensateDistribute compensateDistribute = null;

            if(CollectionUtils.isNotEmpty(compensateDistributeDTOList)){
                compensateDistributeDTO = compensateDistributeDTOList.get(0);
                Integer compensateDistributeId = compensateDistributeDTO.getCompensateDistributeId();
                compensateDistribute = compensateDistributeRepository.selectByPrimaryKey(compensateDistributeId);


                if(CollectionUtils.isEmpty(agreementMasterList) || agreementMasterList.size() < compensateDistribute.getTriggerNum()){
                    this.compensateDistribute(message,compensateDistributeDTO,orderDetailData,masterMatchCondition,orderFeatureContext);
                    return 0L;
                }
            }


            if(CollectionUtils.isEmpty(agreementMasterList)){
                insertAgreementMasterMatch(orderDetailData,"无招募师傅");
                sendNoAgreementMasterResultMessage(message);
                return null;
            }
            agreementMasterList.forEach(agreementMasterExtra -> agreementMasterExtra.setPricingMethod("master".equals(agreementMasterExtra.getMasterPriceType()) ? "master" : agreementMasterExtra.getPricingType()));


            List<EnterpriseOrderAgreementMasterMatch> agreementMasterMatchList = new ArrayList<>();
            agreementMasterList.forEach(agreementMaster -> {
                EnterpriseOrderAgreementMasterMatch agreementMasterMatch = new EnterpriseOrderAgreementMasterMatch();
                agreementMasterMatch.setGlobalOrderTraceId(orderDetailData.getGlobalOrderId());
                agreementMasterMatch.setMasterId(agreementMaster.getMasterId());
                agreementMasterMatch.setUserId(orderDetailData.getUserId());
                agreementMasterMatch.setOrderVersion(orderDetailData.getOrderVersion());
                agreementMasterMatch.setIsMatchSucc(1);
                agreementMasterMatch.setMatchFailReason("");
                agreementMasterMatch.setOrderNo(orderDetailData.getOrderNo());
                agreementMasterMatch.setOrderCreateTime(orderDetailData.getOrderCreateTime());
                agreementMasterMatch.setRecruitId(agreementMaster.getRecruitId());
                agreementMasterMatch.setRecruitSceneId(agreementMaster.getSceneId());
                agreementMasterMatch.setRecruitTagName(agreementMaster.getTagName());
                agreementMasterMatch.setMatchDivisionLevel((Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0) ? 4 : 3);
                agreementMasterMatch.setAgreementMasterId(agreementMaster.getId());
                agreementMasterMatch.setCreateTime(new Date());
                agreementMasterMatch.setUpdateTime(new Date());
                agreementMasterMatch.setMasterSourceType(agreementMaster.getMasterSourceType());
                agreementMasterMatchList.add(agreementMasterMatch);
            });

            Map<String, EnterpriseOrderAgreementMasterMatch> agreementMasterMatchMap = agreementMasterMatchList.stream()
                    .collect(Collectors.toMap(EnterpriseOrderAgreementMasterMatch::getAgreementMasterId, agreementMasterMatch -> agreementMasterMatch));

            Integer matchType = agreementMasterList.get(0).getMatchType();


            List<AgreementMasterBase> agreementMasterBaseList = null;

            Set<String> masterSet;

            if(matchType == 1){
                agreementMasterBaseList = getAgreementMasterMinCooperationPrice(agreementMasterList,orderDetailData,agreementMasterMatchMap);
                if(CollectionUtils.isEmpty(agreementMasterBaseList)){
                    enterpriseAgreementMasterMatchRepository.insertList(agreementMasterMatchList);
                    List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster = this.getFilterMatchMaster(agreementMasterList, agreementMasterMatchMap);

                    sendNoAgreementMasterResultMessage(message, filterMatchMaster, 1);

                    return null;
                }

                Set<String> agreementMasterIdSet = agreementMasterBaseList.stream().map(AgreementMasterBase::getId).collect(Collectors.toSet());
                agreementMasterList = agreementMasterList.stream().filter(enterpriseAgreementMaster -> agreementMasterIdSet.contains(enterpriseAgreementMaster.getId())).collect(Collectors.toList());

                masterSet = agreementMasterBaseList.stream().map(AgreementMasterBase::getMasterId).collect(Collectors.toSet());

            }else{
                masterSet = agreementMasterList.stream().map(AgreementMasterExtra::getMasterId).map(String::valueOf).collect(Collectors.toSet());

            }


            //获取特征

            DefaultContext<String, DefaultContext<String, Object>> masterFeatures;
            if("on".equals(masterFeatureQuerySwitch)){
                //获取特征
                PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData,masterSet);
                log.info("enterpriseAppointAgreementMaster orderDistributor: " +  JSON.toJSONString(orderDistributor));
                featureRepository.getMasterFeatures(pushFeature,masterSet,orderDistributor.getMasterFeatureSet());
                masterFeatures = pushFeature.getMasterFeature();
            }else{
                masterFeatures = featureRepository.masterFeatures(masterSet, orderDistributor.getMasterFeatureSet(), orderFeatureContext);
            }


            if(RecruitScene.NEW_CONTRACT.code.equals(agreementMasterList.get(0).getSceneId())){
                //新合约场景判断新合约请假状态，而不判断普通开工状态
                masterFeatures.keySet().forEach(masterId -> masterFeatures.get(masterId).put("rest_status",1));
            }else{
                masterFeatures.keySet().forEach(masterId -> masterFeatures.get(masterId).put("new_contract_rest_status",0));
            }


//            featureRepository.orderFeatureReplenish(orderFeatureContext, orderDistributor.getOrderFeatureSet());

            if(matchType == 1){
                featureRepository.getAgreementCoopStreetNum(masterFeatures,agreementMasterList);
                featureRepository.getLowestAgreementPrice(masterFeatures,agreementMasterBaseList);
                featureRepository.getOrderDistance(orderDetailData,masterFeatures,agreementMasterList);
            }else{
                orderDistributor.getScoringStrategy().clear();
            }

            log.info("enterpriseAppointAgreementMaster orderDistributor: " +  JSON.toJSONString(orderDistributor));

            log.info("global_order_id:{}-orderFeatures[{}] - masterFeatures:[{}]",orderDetailData.getGlobalOrderId(),orderFeatureContext,masterFeatures);


            //过滤排序
            RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                    .withOrderId(String.valueOf(globalOrderId))
                    .withType(FieldConstant.RANK_DETAIL)
                    .build();

            String distributeRule = orderDistributor.getDistributeRule();
            List<ScorerMaster> scorerMasterList = orderDistributor.rank(masterSet,orderFeatureContext,masterFeatures,rankDetail);

            log.info("scorerMasterList:" + JSON.toJSON(scorerMasterList) + ",rankDetail:" + JSON.toJSONString(rankDetail));

            Map<Long,String> filterReasonMap = new HashMap<>();

            try{
                if(Objects.nonNull(rankDetail) && org.apache.commons.lang.StringUtils.isNotBlank(rankDetail.getDetailInfo())){
                    Map<String,Object> filterDetailsMap = (Map)JSON.parseObject(rankDetail.getDetailInfo()).get("filterDetails");
                    for(String key : filterDetailsMap.keySet()){
                        JSONArray jsonArray = (JSONArray)filterDetailsMap.get(key);
                        jsonArray.forEach(master -> {
                            filterReasonMap.put(Long.valueOf(String.valueOf(master)),key);
                        });
                    }
                }

            }catch(Exception e){
                log.error("rankDetail error",e);
            }


            log.info("after rank scorerMasterList:" + JSON.toJSONString(scorerMasterList));


            List<String> masterList = scorerMasterList.stream().map(ScorerMaster::getMasterId).collect(Collectors.toList());
            List<AgreementMasterExtra> finalAgreementMasterExtraList = agreementMasterList.stream().filter(agreementMasterBase -> masterList.contains(String.valueOf(agreementMasterBase.getMasterId()))).collect(Collectors.toList());

            Set<String> tempAgreementMasterIdSet = CollectionUtils.isNotEmpty(finalAgreementMasterExtraList) ? finalAgreementMasterExtraList.stream().map(AgreementMasterExtra::getId).collect(Collectors.toSet()) : Collections.emptySet();

            for(String agreementMasterId : agreementMasterMatchMap.keySet()){
                EnterpriseOrderAgreementMasterMatch masterMatch = agreementMasterMatchMap.get(agreementMasterId);
                if (Objects.nonNull(masterMatch.getIsCalculatePriceSucc())
                        && masterMatch.getIsCalculatePriceSucc() == 0) {
                    //计价已经失败的，直接continue
                    continue;
                }
                if ((Objects.nonNull(masterMatch.getIsFilter()) && masterMatch.getIsFilter() == 1)
                        || !Strings.isNullOrEmpty(masterMatch.getFilterReason())) {
                    //前面计价时取最低价已经过滤或者设置了过滤原因的,直接continue
                    continue;
                }
                masterMatch.setIsFilter(tempAgreementMasterIdSet.contains(agreementMasterId) ? 0 : 1);
                masterMatch.setFilterReason(tempAgreementMasterIdSet.contains(agreementMasterId) ? "" : filterReasonMap.getOrDefault(agreementMasterMatchMap.get(agreementMasterId).getMasterId(),""));
            }



            if(Objects.nonNull(compensateDistribute) && (CollectionUtils.isEmpty(scorerMasterList) ||
                    scorerMasterList.size() < compensateDistribute.getTriggerNum())){
                this.compensateDistribute(message,compensateDistributeDTO,orderDetailData,masterMatchCondition,orderFeatureContext);
            }else{

                List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster = this.getFilterMatchMaster(agreementMasterList, agreementMasterMatchMap);
                if(CollectionUtils.isEmpty(scorerMasterList)){
                    if (matchType == 1) {
                        sendNoAgreementMasterResultMessage(message, filterMatchMaster,1);
                    } else {
                        sendNoAgreementMasterResultMessage(message, filterMatchMaster,2);
                    }

                }else{
                    if(matchType == 1){
                        EnterpriseMatchAgreementMasterResultDto matchMasterDto = distribute(scorerMasterList,
                                finalAgreementMasterExtraList,
                                filterMatchMaster,
                                distributeRule);
                        if (Objects.isNull(matchMasterDto)) {
                            log.error("enterpriseAppointMaster distribute, unknown distributeRule!,distributeRule:{}", distributeRule);
                        } else {

                            List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> canHiredMaster = matchMasterDto.getCanHiredMaster();
                            List<Long> masterIdList = canHiredMaster.stream().map(EnterpriseMatchAgreementMasterResultMessage.MatchMaster::getMasterId).collect(Collectors.toList());

                            sendResultMessage(message, matchMasterDto, orderDetailData);

                            agreementMasterMatchList.forEach(agreementMasterMatch ->{
                                if (Objects.nonNull(agreementMasterMatch.getIsCalculatePriceSucc())
                                        && agreementMasterMatch.getIsCalculatePriceSucc() == 0) {
                                    return;
                                }
                                if (Objects.nonNull(agreementMasterMatch.getIsFilter())
                                        && agreementMasterMatch.getIsFilter() == 1) {
                                    return;
                                }
                                agreementMasterMatch.setDistributeRule(OrderDistributeRule.asCode(orderDistributor.getDistributeRule()).getDesc());
                                agreementMasterMatch.setIsDistribute(masterIdList.contains(agreementMasterMatch.getMasterId()) ? 1 : 0);
                            });
                        }

                    }else{
                        nearbyAppoint(scorerMasterList, agreementMasterMatchMap, agreementMasterList, filterMatchMaster, orderDetailData, message);
                    }

                }
            }


            if(CollectionUtils.isNotEmpty(agreementMasterMatchList)){
                enterpriseAgreementMasterMatchRepository.insertList(agreementMasterMatchList);
            }


            return 0L;
        }catch(Exception e){
            log.error("matchAgreementMaster error",e);
        }

        sendNoAgreementMasterResultMessage(message);
        return null;
    }


    @Resource
    private EnterpriseAgreementMasterMatchRepository enterpriseAgreementMasterMatchRepository;


    private void insertAgreementMasterMatch(OrderDetailData orderDetailData,String matchFailReason){
        EnterpriseOrderAgreementMasterMatch agreementMasterMatch = new EnterpriseOrderAgreementMasterMatch();
        agreementMasterMatch.setMatchDivisionLevel((Objects.nonNull(orderDetailData.getFourthDivisionId()) && orderDetailData.getFourthDivisionId() > 0) ? 4 : 3);
        agreementMasterMatch.setGlobalOrderTraceId(orderDetailData.getGlobalOrderId());
        agreementMasterMatch.setMasterId(0L);
        agreementMasterMatch.setUserId(orderDetailData.getUserId());
        agreementMasterMatch.setRecruitId(-1L);
        agreementMasterMatch.setOrderNo(orderDetailData.getOrderNo());
        agreementMasterMatch.setOrderCreateTime(orderDetailData.getOrderCreateTime());
        agreementMasterMatch.setIsMatchSucc(0);
        agreementMasterMatch.setMatchFailReason(matchFailReason);
        agreementMasterMatch.setCreateTime(new Date());
        agreementMasterMatch.setUpdateTime(new Date());
        this.enterpriseAgreementMasterMatchRepository.insertSelective(agreementMasterMatch);
    }

    /**
     * 构建过滤师傅数据给总包
     *
     * @param agreementMasterExtraList
     * @param filterMatchMaster
     * @return
     */
    private List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> buildFilterMatchMaster(List<AgreementMasterExtra> agreementMasterExtraList,
                                                                                                 List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster,
                                                                                                 String reason) {

        List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMaster = new ArrayList<>();
        if (CollectionUtil.isEmpty(agreementMasterExtraList)) {
            return filterMaster;
        }
        for (AgreementMasterExtra agreementMasterExtra : agreementMasterExtraList) {
            EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = new EnterpriseMatchAgreementMasterResultMessage.MatchMaster();
            matchMaster.setMasterId(agreementMasterExtra.getMasterId());
            matchMaster.setRecruitId(agreementMasterExtra.getRecruitId());
            matchMaster.setSort(filterMaster.size() + 1);
            matchMaster.setTagName(agreementMasterExtra.getTagName());
            matchMaster.setPriceType(agreementMasterExtra.getPricingMethod());
            matchMaster.setIsCanHired(0);
            matchMaster.setReason(reason);
            filterMaster.add(matchMaster);
        }
        if (CollectionUtil.isEmpty(filterMatchMaster)) {
            return filterMaster;
        } else {

            for (EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster : filterMatchMaster) {
                matchMaster.setSort(filterMaster.size() + 1);
                filterMaster.add(matchMaster);
            }

        }
        return filterMaster;
    }


    /**
     * 就近指派
     * @param scorerMasterList
     * @param agreementMasterExtraList
     * @param filterMatchMaster 调度规则过滤掉的师傅
     * @param orderDetailData
     * @param message
     */
    public void nearbyAppoint(List<ScorerMaster> scorerMasterList,
                              Map<String, EnterpriseOrderAgreementMasterMatch> agreementMasterMatchMap,
                              List<AgreementMasterExtra> agreementMasterExtraList,
                              List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster,
                              OrderDetailData orderDetailData,
                              EnterpriseAppointMessage message){

        if(!"on".equals(enterpriseNearbyAppointSwitch)){
            sendNoAgreementMasterResultMessage(message);
            return ;
        }
        if (CollectionUtil.isNotEmpty(agreementMasterMatchMap)) {
            for(String agreementMasterId : agreementMasterMatchMap.keySet()){
                EnterpriseOrderAgreementMasterMatch masterMatch = agreementMasterMatchMap.get(agreementMasterId);
                if (Objects.nonNull(masterMatch.getIsMatchSucc())
                        && masterMatch.getIsMatchSucc() != 1) {
                    continue;
                }
                if (Objects.nonNull(masterMatch.getIsCalculatePriceSucc())
                        && masterMatch.getIsCalculatePriceSucc() == 0) {
                    masterMatch.setCalculatePriceFailReason("就近指派未计价！");
                }
            }
        }


        if(StringUtils.isBlank(orderDetailData.getOrderLngLat())){
            List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMaster = new ArrayList<>();
            if (CollectionUtil.isEmpty(filterMatchMaster)) {
                //agreementMasterExtraList全部都是  无订单经纬度过滤  原因
                filterMaster = buildFilterMatchMaster(agreementMasterExtraList, null, "就近指派订单无经纬度");
            } else {
                //调度过滤原因+无订单经纬度过滤  原因
                filterMaster = buildFilterMatchMaster(agreementMasterExtraList, filterMatchMaster, "就近指派订单无经纬度");
            }
            sendNoAgreementMasterResultMessage(message, filterMaster, 2);
        }

        Set<Long> scoreMasterSet = scorerMasterList.stream().map(ScorerMaster::getMasterId).map(Long::valueOf).collect(Collectors.toSet());
        agreementMasterExtraList = agreementMasterExtraList.stream().filter(agreementMasterExtra -> scoreMasterSet.contains(agreementMasterExtra.getMasterId())).collect(Collectors.toList());

        featureRepository.getAgreementMasterOrderDistance(orderDetailData,agreementMasterExtraList);
        log.info("agreementMasterExtraList:" + JSON.toJSONString(agreementMasterExtraList));
        AgreementMasterExtra minDistanceAgreementMasterExtra = Collections.min(agreementMasterExtraList, Comparator.comparingLong(AgreementMasterExtra::getOrderDistance));

        //不在订单最近街道内的师傅(被过滤的师傅)
        List<AgreementMasterExtra> filterByNearbyFourthDivisionMasterExtraList = agreementMasterExtraList.stream().filter(agreementMasterExtra -> !agreementMasterExtra.getFourthDivisionIdList().contains(minDistanceAgreementMasterExtra.getFourthDivisionId())).collect(Collectors.toList());

        List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> nearbyAppointFilterMaster = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(filterByNearbyFourthDivisionMasterExtraList)) {
            for (AgreementMasterExtra agreementMasterExtra : filterByNearbyFourthDivisionMasterExtraList) {

                EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = buildMatchMasterAndAddMatchList(agreementMasterExtra, 0, "就近指派师傅不在订单最近街道内");
                matchMaster.setSort(nearbyAppointFilterMaster.size() + 1);
                nearbyAppointFilterMaster.add(matchMaster);
                if (Objects.nonNull(agreementMasterMatchMap)
                        && Objects.nonNull(agreementMasterMatchMap.get(agreementMasterExtra.getId()))) {

                    agreementMasterMatchMap.get(agreementMasterExtra.getId()).setIsFilter(1);
                    agreementMasterMatchMap.get(agreementMasterExtra.getId()).setFilterReason("就近指派师傅不在订单最近街道内");
                }
            }
        }


        agreementMasterExtraList = agreementMasterExtraList.stream().filter(agreementMasterExtra -> agreementMasterExtra.getFourthDivisionIdList().contains(minDistanceAgreementMasterExtra.getFourthDivisionId())).collect(Collectors.toList());
        log.info("filter agreementMasterExtraList:" + JSON.toJSONString(agreementMasterExtraList));


        List<AgreementMasterBase> agreementMasterBaseList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(agreementMasterExtraList)){
            Map<String, List<AgreementMasterExtra>> agreementMasterExtraMap = agreementMasterExtraList.stream().collect(Collectors.groupingBy(AgreementMasterExtra::getPricingMethod));
            agreementMasterExtraMap.keySet().forEach(pricingType -> {
                List<AgreementMasterBase> calculatePriceAgreementMasterBaseList = getAgreementMasterCooperationPrice(agreementMasterExtraMap.get(pricingType), orderDetailData, minDistanceAgreementMasterExtra.getFourthDivisionId(), null);
                if (Objects.nonNull(calculatePriceAgreementMasterBaseList)
                        && CollectionUtil.isNotEmpty(calculatePriceAgreementMasterBaseList)) {
                    agreementMasterBaseList.addAll(calculatePriceAgreementMasterBaseList);
                }
            });
        }

        List<AgreementMasterBase> finalAgreementMasterBaseList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(agreementMasterBaseList)){

            Map<String,List<AgreementMasterBase>> agreementMasterBaseMap = agreementMasterBaseList.stream().collect(Collectors.groupingBy(AgreementMasterBase::getMasterId));


            agreementMasterBaseMap.keySet().forEach(masterId -> {
                AgreementMasterBase agreementMasterBase = agreementMasterBaseMap.get(masterId).stream()
                        .min(Comparator.comparing(AgreementMasterBase::getCooperationPrice)).orElse(null);
                if(Objects.nonNull(agreementMasterBase)){
                    finalAgreementMasterBaseList.add(agreementMasterBase);
                }
            });
        }


        log.info("agreementMasterBaseList:" + JSON.toJSONString(finalAgreementMasterBaseList));
        if(CollectionUtils.isNotEmpty(finalAgreementMasterBaseList)){
            Collections.sort(finalAgreementMasterBaseList);

            //计价成功的协议师傅id
            List<String> agreementMasterIdList = finalAgreementMasterBaseList.stream().map(AgreementMasterBase::getId).collect(Collectors.toList());

            List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> canHiredMatchMasterList = new ArrayList<>();

            for (AgreementMasterExtra agreementMasterExtra : agreementMasterExtraList) {

                if (agreementMasterIdList.contains(agreementMasterExtra.getId())) {

                    EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = buildMatchMasterAndAddMatchList(agreementMasterExtra, 1, null);
                    matchMaster.setSort(canHiredMatchMasterList.size() + 1);
                    canHiredMatchMasterList.add(matchMaster);
                    if (Objects.nonNull(agreementMasterMatchMap)
                            && Objects.nonNull(agreementMasterMatchMap.get(agreementMasterExtra.getId()))) {

                        agreementMasterMatchMap.get(agreementMasterExtra.getId()).setIsCalculatePriceSucc(1);
                        agreementMasterMatchMap.get(agreementMasterExtra.getId()).setCalculatePriceFailReason("");

                    }

                } else {
                    EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = buildMatchMasterAndAddMatchList(agreementMasterExtra, 0, "就近指派师傅未计价成功或该招募师傅不是最低价");
                    matchMaster.setSort(nearbyAppointFilterMaster.size() + 1);
                    nearbyAppointFilterMaster.add(matchMaster);
                    if (Objects.nonNull(agreementMasterMatchMap)
                            && Objects.nonNull(agreementMasterMatchMap.get(agreementMasterExtra.getId()))) {

                        agreementMasterMatchMap.get(agreementMasterExtra.getId()).setIsFilter(1);
                        agreementMasterMatchMap.get(agreementMasterExtra.getId()).setFilterReason("就近指派师傅未计价成功或该招募师傅不是最低价");
                    }

                }
            }

            if (CollectionUtil.isNotEmpty(filterMatchMaster)) {
                for (EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster : filterMatchMaster) {
                    matchMaster.setSort(nearbyAppointFilterMaster.size() + 1);
                    nearbyAppointFilterMaster.add(matchMaster);
                }
            }

            sendNearbyAppointResultMessage(orderDetailData, canHiredMatchMasterList, nearbyAppointFilterMaster, message);
        }else{
            if (CollectionUtil.isNotEmpty(filterMatchMaster)) {
                for (EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster : filterMatchMaster) {
                    matchMaster.setSort(nearbyAppointFilterMaster.size() + 1);
                    nearbyAppointFilterMaster.add(matchMaster);
                }
            }

            if (CollectionUtil.isNotEmpty(agreementMasterExtraList)) {
                for (AgreementMasterExtra agreementMasterExtra : agreementMasterExtraList) {
                    EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = buildMatchMasterAndAddMatchList(agreementMasterExtra, 0, "就近指派师傅未计价成功");
                    matchMaster.setSort(nearbyAppointFilterMaster.size() + 1);
                    nearbyAppointFilterMaster.add(matchMaster);
                    if (Objects.nonNull(agreementMasterMatchMap)
                            && Objects.nonNull(agreementMasterMatchMap.get(agreementMasterExtra.getId()))) {

                        agreementMasterMatchMap.get(agreementMasterExtra.getId()).setIsCalculatePriceSucc(0);
                        agreementMasterMatchMap.get(agreementMasterExtra.getId()).setCalculatePriceFailReason("就近指派师傅未计价成功");
                    }
                }
            }

            sendNoAgreementMasterResultMessage(message, nearbyAppointFilterMaster, 2);
        }
    }

    /**
     * 构建给到总包的协议师傅信息
     * @param agreementMasterExtra
     * @param isCanHired
     * @param reason
     * @return
     */
    private EnterpriseMatchAgreementMasterResultMessage.MatchMaster buildMatchMasterAndAddMatchList(AgreementMasterExtra agreementMasterExtra,
                                                                                                    Integer isCanHired,
                                                                                                    String reason) {

        EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = new EnterpriseMatchAgreementMasterResultMessage.MatchMaster();

        matchMaster.setMasterId(agreementMasterExtra.getMasterId());
        matchMaster.setRecruitId(agreementMasterExtra.getRecruitId());
        matchMaster.setDistance(agreementMasterExtra.getOrderDistance());
        matchMaster.setStreet(agreementMasterExtra.getFourthDivisionId());
        matchMaster.setTagName(agreementMasterExtra.getTagName());
        matchMaster.setPriceType(agreementMasterExtra.getPricingMethod());

        if (Objects.nonNull(reason)) {
            matchMaster.setReason(reason);
        }

        matchMaster.setIsCanHired(isCanHired);

        return matchMaster;
    }

    private void sendNearbyAppointResultMessage(OrderDetailData orderDetailData,
                                                List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> canHiredMatchMasterList,
                                                List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMasterList,
                                                EnterpriseAppointMessage enterpriseAppointMessage){

        EnterpriseMatchAgreementMasterResultMessage resultMessage = new EnterpriseMatchAgreementMasterResultMessage();
        resultMessage.setEnterpriseId(orderDetailData.getAccountId());
        resultMessage.setOrderId(orderDetailData.getEnterpriseOrderId());
        resultMessage.setGlobalOrderTraceId(orderDetailData.getGlobalOrderId());
        resultMessage.setType(2);
        List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> matchMasterList = this.aggregateMaster(canHiredMatchMasterList, filterMatchMasterList);

        resultMessage.setMatchMaster(matchMasterList);

        List<Long> canHiredMasterIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(canHiredMatchMasterList)) {
            canHiredMasterIdList = canHiredMatchMasterList.stream().map(EnterpriseMatchAgreementMasterResultMessage.MatchMaster::getMasterId).collect(Collectors.toList());
        }
        List<Long> filterMasterIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(filterMatchMasterList)) {
            filterMasterIdList = filterMatchMasterList.stream().map(EnterpriseMatchAgreementMasterResultMessage.MatchMaster::getMasterId).collect(Collectors.toList());
        }

        log.info("nearby enterpriseAppointAgreementMaster,enterpriseId:{},orderId:{},type:{},canHiredMasterIdList:{},filterMasterIdList:{}",
                orderDetailData.getAccountId(),orderDetailData.getEnterpriseOrderId(),2, JSONUtil.toJsonStr(canHiredMasterIdList), JSONUtil.toJsonStr(filterMasterIdList));

        resultMessage.setAutoHireMasterWebRqt(enterpriseAppointMessage.getAutoHireMasterWebRqt());

        pushQueueService.sendAgreementMasterMatchMessage(resultMessage,1L);

        String orderVersion = String.valueOf(System.currentTimeMillis());
        pushProgressRepository.insertBasePushProgress(enterpriseAppointMessage.getGlobalOrderTraceId(),orderVersion,canHiredMatchMasterList.size(),new Date(),"enterprise_match_agreement_master");


    }


    private void compensateDistribute(EnterpriseAppointMessage message,OrderDistributor.CompensateDistributeDTO compensateDistributeDTO,
                                      OrderDetailData orderDetailData,MasterMatchCondition masterMatchCondition,
                                      DefaultContext<String,Object> orderFeatureContext){

        OrderRoutingStrategy orderRoutingStrategy = orderRoutingStrategyRepository.selectByPrimaryKey(compensateDistributeDTO.getOrderRoutingStrategyId());
        OrderMatchRouting orderMatchRouting = orderMatchRoutingRepository.selectByPrimaryKey(orderRoutingStrategy.getMatchRoutingId());


        if("agreement".equals(orderMatchRouting.getLv1MasterType())){
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.ENTERPRISE_APPOINT);
            MatchMasterResult matchMasterResult = orderMasterMatcher.match(orderDetailData,masterMatchCondition);
            Long orderSelectStrategyId = compensateDistributeDTO.getDistributeStrategyList().getOrderSelectStrategyId();

            OrderSelectStrategyDetailRqt orderSelectStr = new OrderSelectStrategyDetailRqt();
            orderSelectStr.setStrategyId(orderSelectStrategyId.intValue());
            OrderSelectStrategy orderSelectStrategy = orderSelectStrategyService.detail(orderSelectStr);


            Long orderScoringStrategyId = compensateDistributeDTO.getDistributeStrategyList().getOrderScoringStrategyId();
            OrderScoringStrategyDetailRqt orderScoringStrategyDetailRqt = new OrderScoringStrategyDetailRqt();
            orderScoringStrategyDetailRqt.setStrategyId(orderScoringStrategyId.intValue());
            OrderScoringStrategy orderScoringStrategy = orderScoringStrategyService.detail(orderScoringStrategyDetailRqt);

            OrderDistributor orderDistributor = new OrderDistributor();
            orderDistributor.setSelectStrategy(Collections.singletonList(orderSelectStrategy),orderFeatureContext);
            orderDistributor.setScoringStrategy(Collections.singletonList(orderScoringStrategy));

            featureRepository.orderFeatureReplenish(orderFeatureContext, orderDistributor.getOrderFeatureSet());

            log.info("enterpriseAppointAgreementMaster orderDistributor: " +  JSON.toJSONString(orderDistributor));




            List<AgreementMasterExtra> agreementMasterList = (List<AgreementMasterExtra>)matchMasterResult.getExtraData().get("agreement_master_list");

            if(CollectionUtils.isEmpty(agreementMasterList)){
                sendNoAgreementMasterResultMessage(message);
                return ;
            }

            agreementMasterList.forEach(agreementMasterExtra -> agreementMasterExtra.setPricingMethod("master".equals(agreementMasterExtra.getMasterPriceType()) ? "master" : agreementMasterExtra.getPricingType()));

            Integer matchType = agreementMasterList.get(0).getMatchType();
            Set<String> masterSet;
            List<AgreementMasterBase> agreementMasterBaseList = null;


            if(matchType == 1){
                agreementMasterBaseList = getAgreementMasterMinCooperationPrice(agreementMasterList,orderDetailData,null);
                if(CollectionUtils.isEmpty(agreementMasterBaseList)){
                    List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster = this.buildFilterMatchMaster(agreementMasterList, null, "补偿调度计价失败！");

                    sendNoAgreementMasterResultMessage(message, filterMatchMaster, 1);
                    return ;
                }

                masterSet = agreementMasterBaseList.stream().map(AgreementMasterBase::getMasterId).collect(Collectors.toSet());


                Set<String> agreementMasterIdSet = agreementMasterBaseList.stream().map(AgreementMasterBase::getId).collect(Collectors.toSet());


                agreementMasterList = agreementMasterList.stream().filter(enterpriseAgreementMaster -> agreementMasterIdSet.contains(enterpriseAgreementMaster.getId())).collect(Collectors.toList());

            }else{
                masterSet = agreementMasterList.stream().map(AgreementMasterExtra::getMasterId).map(String::valueOf).collect(Collectors.toSet());

            }





            DefaultContext<String, DefaultContext<String, Object>> masterFeatures;
            if("on".equals(masterFeatureQuerySwitch)){
                //获取特征
                PushFeature pushFeature = featureRepository.buildPushFeature(orderDetailData,masterSet);
                featureRepository.getMasterFeatures(pushFeature,masterSet,orderDistributor.getMasterFeatureSet());
                masterFeatures = pushFeature.getMasterFeature();
            }else{
                masterFeatures = featureRepository.masterFeatures(masterSet, orderDistributor.getMasterFeatureSet(), orderFeatureContext);
            }


            if(RecruitScene.NEW_CONTRACT.code.equals(agreementMasterList.get(0).getSceneId())){
                //新合约场景判断新合约请假状态，而不判断普通开工状态
                masterFeatures.keySet().forEach(masterId -> masterFeatures.get(masterId).put("rest_status",1));
            }else{
                masterFeatures.keySet().forEach(masterId -> masterFeatures.get(masterId).put("new_contract_rest_status",0));
            }


            if(matchType == 1){
                featureRepository.getAgreementCoopStreetNum(masterFeatures,agreementMasterList);
                featureRepository.getLowestAgreementPrice(masterFeatures,agreementMasterBaseList);
                featureRepository.getOrderDistance(orderDetailData,masterFeatures,agreementMasterList);
            }else{
                orderDistributor.getScoringStrategy().clear();
            }


            log.info("global_order_id:{}-orderFeatures[{}] - masterFeatures:[{}]",orderDetailData.getGlobalOrderId(),orderFeatureContext,masterFeatures);



            //过滤排序
            RankDetail rankDetail = RankDetail.RankDetailBuilder.aRankDetail()
                    .withOrderId(String.valueOf(orderDetailData.getGlobalOrderId()))
                    .withType(FieldConstant.RANK_DETAIL)
                    .build();

            List<ScorerMaster> scorerMasterList = orderDistributor.rank(masterSet,orderFeatureContext,masterFeatures,rankDetail);

            Map<Long,String> filterReasonMap = new HashMap<>();

            try{
                if(org.apache.commons.lang.StringUtils.isNotBlank(rankDetail.getDetailInfo())){
                    Map<String,Object> filterDetailsMap = (Map)JSON.parseObject(rankDetail.getDetailInfo()).get("filterDetails");
                    for(String key : filterDetailsMap.keySet()){
                        JSONArray jsonArray = (JSONArray)filterDetailsMap.get(key);
                        jsonArray.forEach(master -> {
                            filterReasonMap.put(Long.valueOf(String.valueOf(master)),key);
                        });
                    }
                }

            }catch(Exception e){
                log.error("rankDetail error",e);
            }

            List<String> masterList = scorerMasterList.stream().map(ScorerMaster::getMasterId).collect(Collectors.toList());
            List<AgreementMasterExtra> finalAgreementMasterExtraList = agreementMasterList.stream().filter(agreementMasterBase -> masterList.contains(String.valueOf(agreementMasterBase.getMasterId()))).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(scorerMasterList)){
                List<AgreementMasterExtra> filterAgreementMasterExtraList = agreementMasterList.stream().filter(agreementMasterBase -> !masterList.contains(String.valueOf(agreementMasterBase.getMasterId()))).collect(Collectors.toList());
                List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster = buildFilterMatchMaster(filterAgreementMasterExtraList, null, "补偿调度调度规则过滤！");

                if(matchType == 1){

                    String distributeRule = compensateDistributeDTO.getDistributeStrategyList().getDistributeRule();

                    EnterpriseMatchAgreementMasterResultDto matchMasterDto = distribute(scorerMasterList,
                            finalAgreementMasterExtraList,
                            filterMatchMaster,
                            distributeRule);
                    if (Objects.isNull(matchMasterDto)) {
                        log.error("enterpriseAppointMaster compensateDistribute, unknown distributeRule!,distributeRule:{}", distributeRule);
                    } else {
                        sendResultMessage(message, matchMasterDto, orderDetailData);
                    }

                }else{

                    nearbyAppoint(scorerMasterList, null, agreementMasterList, filterMatchMaster, orderDetailData, message);
                }


            }else{
                List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster = this.buildFilterMatchMaster(agreementMasterList, null, "补偿调度调度规则过滤！");
                if (matchType == 1) {
                    sendNoAgreementMasterResultMessage(message, filterMatchMaster, 1);
                } else {
                    sendNoAgreementMasterResultMessage(message, filterMatchMaster, 2);
                }
            }

        }else{
            sendNoAgreementMasterResultMessage(message);
        }
    }


    private void sendResultMessage(EnterpriseAppointMessage enterpriseAppointMessage,
                                   EnterpriseMatchAgreementMasterResultDto matchMasterDto,
                                   OrderDetailData orderDetailData){
        EnterpriseMatchAgreementMasterResultMessage resultMessage = new EnterpriseMatchAgreementMasterResultMessage();
        resultMessage.setEnterpriseId(orderDetailData.getAccountId());
        resultMessage.setOrderId(orderDetailData.getEnterpriseOrderId());
        resultMessage.setGlobalOrderTraceId(orderDetailData.getGlobalOrderId());
        resultMessage.setType(1);

        List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> matchMasterList = aggregateMaster(matchMasterDto.getCanHiredMaster(), matchMasterDto.getFilterMaster());
        resultMessage.setMatchMaster(matchMasterList);

        List<Long> canHiredMasterIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(matchMasterDto.getCanHiredMaster())) {
            canHiredMasterIdList = matchMasterDto.getCanHiredMaster().stream().map(EnterpriseMatchAgreementMasterResultMessage.MatchMaster::getMasterId).collect(Collectors.toList());
        }
        List<Long> filterMasterIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(matchMasterDto.getFilterMaster())) {
            filterMasterIdList = matchMasterDto.getFilterMaster().stream().map(EnterpriseMatchAgreementMasterResultMessage.MatchMaster::getMasterId).collect(Collectors.toList());
        }

        log.info("enterpriseAppointAgreementMaster,enterpriseId:{},orderId:{},type:{},canHiredMasterIdList:{},filterMasterIdList:{}",
                orderDetailData.getAccountId(),orderDetailData.getEnterpriseOrderId(),1, JSONUtil.toJsonStr(canHiredMasterIdList), JSONUtil.toJsonStr(filterMasterIdList));

        resultMessage.setAutoHireMasterWebRqt(enterpriseAppointMessage.getAutoHireMasterWebRqt());

        pushQueueService.sendAgreementMasterMatchMessage(resultMessage,1L);

        String orderVersion = String.valueOf(System.currentTimeMillis());
        pushProgressRepository.insertBasePushProgress(enterpriseAppointMessage.getGlobalOrderTraceId(), orderVersion, matchMasterDto.getCanHiredMaster().size(), new Date(), "enterprise_match_agreement_master");

    }

    private void sendNoAgreementMasterResultMessage(EnterpriseAppointMessage enterpriseAppointMessage){
        EnterpriseMatchAgreementMasterResultMessage resultMessage = new EnterpriseMatchAgreementMasterResultMessage();
        resultMessage.setEnterpriseId(enterpriseAppointMessage.getEnterpriseId());
        resultMessage.setOrderId(enterpriseAppointMessage.getOrderId());
        resultMessage.setGlobalOrderTraceId(enterpriseAppointMessage.getGlobalOrderTraceId());
        resultMessage.setType(0);
        resultMessage.setMatchMaster(null);
        resultMessage.setAutoHireMasterWebRqt(enterpriseAppointMessage.getAutoHireMasterWebRqt());

        pushQueueService.sendAgreementMasterMatchMessage(resultMessage,1L);

        String orderVersion = String.valueOf(System.currentTimeMillis());

        pushProgressRepository.insertBasePushZeroProgress(enterpriseAppointMessage.getGlobalOrderTraceId(),orderVersion,new Date(),"enterprise_match_agreement_master");
    }

    /**
     * 匹配到协议师傅后，无可用师傅构建返回信息返回总包
     * @param enterpriseAppointMessage
     * @param filterMatchMaster
     * @param type 1：有4级地址的匹配，2：无四级地址(走就近指派的逻辑)
     */
    private void sendNoAgreementMasterResultMessage(EnterpriseAppointMessage enterpriseAppointMessage,
                                                    List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster, Integer type) {

        EnterpriseMatchAgreementMasterResultMessage resultMessage = new EnterpriseMatchAgreementMasterResultMessage();
        resultMessage.setEnterpriseId(enterpriseAppointMessage.getEnterpriseId());
        resultMessage.setOrderId(enterpriseAppointMessage.getOrderId());
        resultMessage.setGlobalOrderTraceId(enterpriseAppointMessage.getGlobalOrderTraceId());
        resultMessage.setType(type);
        resultMessage.setAutoHireMasterWebRqt(enterpriseAppointMessage.getAutoHireMasterWebRqt());

        resultMessage.setMatchMaster(filterMatchMaster);

        pushQueueService.sendAgreementMasterMatchMessage(resultMessage, 1L);

        String orderVersion = String.valueOf(System.currentTimeMillis());

        pushProgressRepository.insertBasePushZeroProgress(enterpriseAppointMessage.getGlobalOrderTraceId(), orderVersion, new Date(), "enterprise_match_agreement_master");
    }

    /**
     * 依据日志记录获取过滤的协议师傅
     * @param agreementMasterList
     * @param agreementMasterMatchMap
     * @return
     */
    private List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> getFilterMatchMaster(List<AgreementMasterExtra> agreementMasterList,
                                                                                               Map<String, EnterpriseOrderAgreementMasterMatch> agreementMasterMatchMap) {

        List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> matchMasterList = Lists.newArrayList();

        Map<String, AgreementMasterExtra> agreementMasterExtraMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(agreementMasterList)) {
            agreementMasterExtraMap = agreementMasterList.stream()
                    .collect(Collectors.toMap(AgreementMasterExtra::getId, agreementMasterExtra -> agreementMasterExtra));
        }

        if (CollectionUtil.isNotEmpty(agreementMasterMatchMap)) {

            for (Map.Entry<String, EnterpriseOrderAgreementMasterMatch> entry : agreementMasterMatchMap.entrySet()) {

                EnterpriseOrderAgreementMasterMatch match = entry.getValue();

                AgreementMasterExtra agreementMasterExtra = agreementMasterExtraMap.get(entry.getKey());

                EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = new EnterpriseMatchAgreementMasterResultMessage.MatchMaster();
                matchMaster.setMasterId(match.getMasterId());
                matchMaster.setRecruitId(match.getRecruitId());
                if (Objects.nonNull(agreementMasterExtra)) {
                    matchMaster.setPriceType(agreementMasterExtra.getPricingMethod());
                }
                matchMaster.setTagName(match.getRecruitTagName());
                matchMaster.setSort(matchMasterList.size() + 1);

                if (Objects.nonNull(match.getIsCalculatePriceSucc())
                        && match.getIsCalculatePriceSucc() == 0) {
                    matchMaster.setIsCanHired(0);
                    matchMaster.setReason(match.getCalculatePriceFailReason());
                    matchMasterList.add(matchMaster);
                }

                if (Objects.nonNull(match.getIsFilter())
                        && match.getIsFilter() == 1) {
                    matchMaster.setIsCanHired(0);
                    matchMaster.setReason(match.getFilterReason());
                    matchMasterList.add(matchMaster);
                }

            }

        }
        return matchMasterList;

    }

    /**
     * 调度规则调度
     * @param scorerMasterList
     * @param finalAgreementMasterExtraList
     * @param filterMatchMaster
     * @param distributeRule
     * @return
     */
    private EnterpriseMatchAgreementMasterResultDto distribute(List<ScorerMaster> scorerMasterList,
                                                               List<AgreementMasterExtra> finalAgreementMasterExtraList,
                                                               List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster,
                                                               String distributeRule) {

        EnterpriseMatchAgreementMasterResultDto resultDto = new EnterpriseMatchAgreementMasterResultDto();


        Map<String, AgreementMasterExtra> agreementMasterExtraMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(finalAgreementMasterExtraList)) {
            agreementMasterExtraMap = finalAgreementMasterExtraList.stream()
                    .collect(Collectors.toMap(agreementMasterExtra -> String.valueOf(agreementMasterExtra.getMasterId()), agreementMasterExtra -> agreementMasterExtra));
        }


        List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> canHiredMaster = Lists.newArrayList();

        if (OrderDistributeRule.SCORING_ORDER.getCode().equals(distributeRule)
                || OrderDistributeRule.INTELLIGENT.getCode().equals(distributeRule)) {
            Collections.sort(scorerMasterList);
            for (ScorerMaster scorerMaster : scorerMasterList) {
                AgreementMasterExtra agreementMasterExtra = agreementMasterExtraMap.get(scorerMaster.getMasterId());

                EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = new EnterpriseMatchAgreementMasterResultMessage.MatchMaster();

                matchMaster.setMasterId(Long.valueOf(scorerMaster.getMasterId()));
                matchMaster.setIsCanHired(1);
                matchMaster.setSort(canHiredMaster.size() + 1);
                if (Objects.nonNull(agreementMasterExtra)) {
                    matchMaster.setRecruitId(agreementMasterExtra.getRecruitId());
                    matchMaster.setPriceType(agreementMasterExtra.getPricingMethod());
                    matchMaster.setTagName(agreementMasterExtra.getTagName());

                } else {
                    log.error("enterpriseAppointMaster scoring_order distribute agreementMasterExtra is null,masterId:{}", scorerMaster.getMasterId());
                }
                canHiredMaster.add(matchMaster);
            }
            resultDto.setCanHiredMaster(canHiredMaster);
            resultDto.setFilterMaster(filterMatchMaster);
            return resultDto;
        } else if (OrderDistributeRule.SCORING_ORDER_TOP50_RANDOM.getCode().equals(distributeRule)
                || OrderDistributeRule.SCORING_ORDER_TOP10_RANDOM.getCode().equals(distributeRule)) {
            //兼容线上已配置的top50的配置，后续线上没有top50的配置，则可以去掉
            Collections.sort(scorerMasterList);

            List<ScorerMaster> canHiredScorerMasterList = Lists.newArrayList();
            List<ScorerMaster> filterScorerMasterList = Lists.newArrayList();
            for (int i = 0; i < scorerMasterList.size(); i++) {
                if (i < 10) {
                    canHiredScorerMasterList.add(scorerMasterList.get(i));
                } else {
                    filterScorerMasterList.add(scorerMasterList.get(i));
                }
            }

            Collections.shuffle(canHiredScorerMasterList);

            for (ScorerMaster scorerMaster : canHiredScorerMasterList) {
                AgreementMasterExtra agreementMasterExtra = agreementMasterExtraMap.get(scorerMaster.getMasterId());

                EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = new EnterpriseMatchAgreementMasterResultMessage.MatchMaster();

                matchMaster.setMasterId(Long.valueOf(scorerMaster.getMasterId()));
                matchMaster.setIsCanHired(1);
                matchMaster.setSort(canHiredMaster.size() + 1);
                if (Objects.nonNull(agreementMasterExtra)) {
                    matchMaster.setRecruitId(agreementMasterExtra.getRecruitId());
                    matchMaster.setPriceType(agreementMasterExtra.getPricingMethod());
                    matchMaster.setTagName(agreementMasterExtra.getTagName());

                } else {
                    log.error("enterpriseAppointMaster scoring_order_top10_random distribute agreementMasterExtra is null,masterId:{}", scorerMaster.getMasterId());
                }
                canHiredMaster.add(matchMaster);
            }

            for (ScorerMaster scorerMaster : filterScorerMasterList) {
                AgreementMasterExtra agreementMasterExtra = agreementMasterExtraMap.get(scorerMaster.getMasterId());

                EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = new EnterpriseMatchAgreementMasterResultMessage.MatchMaster();

                matchMaster.setMasterId(Long.valueOf(scorerMaster.getMasterId()));
                matchMaster.setIsCanHired(0);
                matchMaster.setSort(filterMatchMaster.size() + 1);
                matchMaster.setReason("不符合评分top10");
                if (Objects.nonNull(agreementMasterExtra)) {
                    matchMaster.setRecruitId(agreementMasterExtra.getRecruitId());
                    matchMaster.setPriceType(agreementMasterExtra.getPricingMethod());
                    matchMaster.setTagName(agreementMasterExtra.getTagName());

                } else {
                    log.error("enterpriseAppointMaster scoring_order_top10_random distribute agreementMasterExtra is null,masterId:{}", scorerMaster.getMasterId());
                }
                filterMatchMaster.add(matchMaster);
            }
            resultDto.setCanHiredMaster(canHiredMaster);
            resultDto.setFilterMaster(filterMatchMaster);
            return resultDto;
        } else if (OrderDistributeRule.RANDOM.getCode().equals(distributeRule)) {
            Collections.shuffle(scorerMasterList);

            for (ScorerMaster scorerMaster : scorerMasterList) {
                AgreementMasterExtra agreementMasterExtra = agreementMasterExtraMap.get(scorerMaster.getMasterId());

                EnterpriseMatchAgreementMasterResultMessage.MatchMaster matchMaster = new EnterpriseMatchAgreementMasterResultMessage.MatchMaster();

                matchMaster.setMasterId(Long.valueOf(scorerMaster.getMasterId()));
                matchMaster.setIsCanHired(1);
                matchMaster.setSort(canHiredMaster.size() + 1);
                if (Objects.nonNull(agreementMasterExtra)) {
                    matchMaster.setRecruitId(agreementMasterExtra.getRecruitId());
                    matchMaster.setPriceType(agreementMasterExtra.getPricingMethod());
                    matchMaster.setTagName(agreementMasterExtra.getTagName());

                } else {
                    log.error("enterpriseAppointMaster random distribute agreementMasterExtra is null,masterId:{}", scorerMaster.getMasterId());
                }
                canHiredMaster.add(matchMaster);
            }
            resultDto.setCanHiredMaster(canHiredMaster);
            resultDto.setFilterMaster(filterMatchMaster);
            return resultDto;
        }
        return null;

    }

    /**
     * 聚合能指派和不能指派的协议师傅，并设置连续的sort排序字段值
     * @param canHiredMatchMaster
     * @param filterMatchMaster
     * @return
     */
    private List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> aggregateMaster(List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> canHiredMatchMaster,
                                                                                          List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> filterMatchMaster) {
        List<EnterpriseMatchAgreementMasterResultMessage.MatchMaster> resList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(canHiredMatchMaster)) {
            resList.addAll(canHiredMatchMaster);
        }
        if (CollectionUtil.isNotEmpty(filterMatchMaster)) {
            for (EnterpriseMatchAgreementMasterResultMessage.MatchMaster filterMaster : filterMatchMaster) {
                filterMaster.setSort(resList.size() + 1);
                resList.add(filterMaster);
            }
        }

        return resList;
    }



    private Long intelligentDistribute(List<ScorerMaster> scorerMasterList){

        scorerMasterList = scorerMasterList.stream().sorted().collect(Collectors.toList());

        Integer totalScore = scorerMasterList.stream().map(ScorerMaster::getScore).mapToInt(BigDecimal::intValue).sum();
        Random random = new Random();
        int randomNum = random.nextInt(totalScore + 1);
        for(ScorerMaster scoreMaster : scorerMasterList){
            if(randomNum <= scoreMaster.getScore().intValue()){
                return Long.valueOf(scoreMaster.getMasterId());
            }
        }

        return Long.valueOf(scorerMasterList.get(0).getMasterId());

    }





    private List<AgreementMasterBase> getAgreementMasterCooperationPrice(List<AgreementMasterExtra> agreementMasterExtraList,
                                                                         OrderDetailData orderDetailData, Long fourthDivisionId,
                                                                         Map<String, EnterpriseOrderAgreementMasterMatch> agreementMasterMatchMap
    ){

        List<AgreementMasterBase> agreementMasterBaseList = new ArrayList<>();

        try{
            log.info("getAgreementMasterCooperationPrice" + JSON.toJSONString(agreementMasterExtraList));


            Long orderId = orderDetailData.getMasterOrderId();
            OrderGoodsInfoRqt orderGoodsInfoRqt = new OrderGoodsInfoRqt();
            orderGoodsInfoRqt.setOrderId(orderDetailData.getEnterpriseOrderId());
            orderGoodsInfoRqt.setEnterpriseId(orderDetailData.getAccountId());
            OrderGoodsInfoResp orderGoodsInfoResp = infoQueryApi.orderGoodsInfo(orderGoodsInfoRqt);
            List<OrderServiceAttributeInfo> orderServiceAttributeInfoList = orderGoodsInfoResp.getOrderGoodsItemList().stream().map(orderGoodsItemVo -> {
                return orderGoodsItemVo.getAttributeInfo();
            }).collect(Collectors.toList());

            log.info("orderServiceAttributeInfoList " + JSON.toJSONString(orderServiceAttributeInfoList));


            ApplyOrderCalculateBatchReq batchReq = new ApplyOrderCalculateBatchReq();
            ApplyOrderCalculateReq calculateReq = new ApplyOrderCalculateReq();

            com.wanshifu.fee.center.domain.dto.OrderBase orderBaseParam = new com.wanshifu.fee.center.domain.dto.OrderBase();
            orderBaseParam.setOrderId(orderId);
            orderBaseParam.setGlobalOrderTraceId(orderDetailData.getGlobalOrderId());
            orderBaseParam.setCreateTime(orderDetailData.getOrderCreateTime());
            calculateReq.setOrderBase(orderBaseParam);
            List<CalculateServiceInfo> serviceInfos = new ArrayList<>();
            orderServiceAttributeInfoList.forEach(orderServiceAttributeInfo -> serviceInfos.add(JSON.parseObject(orderServiceAttributeInfo.getServiceInfos(),CalculateServiceInfo.class)));
            calculateReq.setServiceInfos(serviceInfos);
            AccountInfo from = new AccountInfo();
            from.setAccountId(orderDetailData.getAccountId());
            from.setAccountType(orderDetailData.getAccountType());
            calculateReq.setFrom(from);


            String pricingMethod = agreementMasterExtraList.get(0).getPricingMethod();

            List<BizRuleBatchReq> bizRuleBatchReqList = new ArrayList<>();


            if("master".equals(pricingMethod)){
                log.info("pricingMethod master");
                List<String> sceneCode = Collections.singletonList(SceneCodeEnum.CONTRACT_MASTER.getCode());
                calculateReq.setSceneCode(sceneCode);
                AddressInfo addressInfo = new AddressInfo();
                addressInfo.setDivisionType(DivisionTypeEnum.STREET.code);
                Address address = addressApi.getDivisionInfoByDivisionId(fourthDivisionId);
                BeanUtils.copyProperties(address,addressInfo);
                calculateReq.setAddressInfo(addressInfo);
                Integer appointType = orderDetailData.getAppointType();
                if(AppointType.OPEN.value.equals(appointType)){
                    calculateReq.setIsMappingPricing(false);
                }else if(AppointType.DEFINITE_PRICE.value.equals(appointType)){
                    calculateReq.setFromSceneCode(SceneCodeEnum.CONTRACT_MASTER.getCode());
                }


                agreementMasterExtraList.forEach(agreementMasterExtra -> {
                    BizRuleBatchReq bizRuleBatchReq = new BizRuleBatchReq();
                    bizRuleBatchReq.setBizId(String.valueOf(agreementMasterExtra.getMasterId()));
                    bizRuleBatchReq.setBizTag(String.valueOf(agreementMasterExtra.getRecruitId()));
                    bizRuleBatchReqList.add(bizRuleBatchReq);

                });

            }else{
                //按招募计价
                log.info("pricingMethod recruit");
                List<String> sceneCode = Collections.singletonList(SceneCodeEnum.MASTER_RECRUIT_ACTIVE_PRICE.getCode());
                calculateReq.setSceneCode(sceneCode);
                AddressInfo addressInfo = new AddressInfo();
                DivisionTypeEnum divisionType = null;
                Long divisionId = 0L;
                if(RecruitPricingType.UNITE.getCode().equals(pricingMethod)){
                    divisionType = DivisionTypeEnum.COUNTRY;
                    divisionId = orderDetailData.getSecondDivisionId();
                }else if(RecruitPricingType.CITY.getCode().equals(pricingMethod)){
                    divisionType = DivisionTypeEnum.CITY;
                    divisionId = orderDetailData.getSecondDivisionId();
                }else if(RecruitPricingType.REGIONAL.getCode().equals(pricingMethod)){
                    divisionType = DivisionTypeEnum.DISTRICT;
                    divisionId = orderDetailData.getThirdDivisionId();
                }
                if(Objects.isNull(divisionType)){
                    log.error("enterpriseAppointMaster calculatePrice divisionType is null!");
                    return null;
                }
                addressInfo.setDivisionType(divisionType.code);
                Address address = addressApi.getDivisionInfoByDivisionId(divisionId);
                BeanUtils.copyProperties(address,addressInfo);
                calculateReq.setAddressInfo(addressInfo);
                Integer appointType = orderDetailData.getAppointType();
                if(AppointType.OPEN.value.equals(appointType)){
                    calculateReq.setIsMappingPricing(false);
                }else if(AppointType.DEFINITE_PRICE.value.equals(appointType)){
                    calculateReq.setFromSceneCode(SceneCodeEnum.MASTER_RECRUIT_ACTIVE_PRICE.getCode());
                }


                Map<Long, List<AgreementMasterExtra>> agreementMasterExtraMap = agreementMasterExtraList.stream().collect(Collectors.groupingBy(AgreementMasterExtra::getRecruitId));

                agreementMasterExtraMap.keySet().forEach(recruitId -> {
                    BizRuleBatchReq bizRuleBatchReq = new BizRuleBatchReq();
                    bizRuleBatchReq.setBizTag(String.valueOf(recruitId));
                    bizRuleBatchReqList.add(bizRuleBatchReq);

                });
            }

            batchReq.setBizRuleBatchReqList(bizRuleBatchReqList);


            batchReq.setApplyOrderCalculateReq(calculateReq);





            Map<String, AgreementMaster> agreementMasterMap = agreementMasterExtraList.stream().collect(Collectors.toMap(AgreementMaster::getId, Function.identity()));

            log.info("applyOrderCalculateBatch req:" + JSON.toJSONString(batchReq));

            List<ApplyOrderCalculateBatchResp> applyOrderCalculateBatchRespList = feeRuleApi.applyOrderCalculateBatch(batchReq);
            log.info("applyOrderCalculateBatch resp:" + JSON.toJSONString(applyOrderCalculateBatchRespList));
            List<ApplyOrderCalculateBatchResp> feeList = applyOrderCalculateBatchRespList.stream().filter(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getApplyOrderCalculateResp().getSceneResultList().get(0).isSuccess()).collect(Collectors.toList());
            if(AppointType.DEFINITE_PRICE.value.equals(orderDetailData.getAppointType())){
                feeList = feeList.stream().filter(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getApplyOrderCalculateResp().getCost().compareTo(orderDetailData.getDefiniteServeFee()) <= 0).collect(Collectors.toList());
            }
            if(CollectionUtils.isNotEmpty(feeList)){

                if("master".equals(pricingMethod)){
                    Map<String, ApplyOrderCalculateBatchResp> applyOrderCalculateBatchRespMap = feeList.stream()
                            .collect(Collectors.toMap(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getBizTag() + ":" + applyOrderCalculateBatchResp.getBizId(), Function.identity()));
                    agreementMasterExtraList.forEach(agreementMasterExtra -> {

                        ApplyOrderCalculateBatchResp resp = applyOrderCalculateBatchRespMap.get(agreementMasterExtra.getId());
                        if (Objects.nonNull(resp)) {
                            AgreementMasterBase agreementMasterBase = new AgreementMasterBase();
                            agreementMasterBase.setMasterId(resp.getBizId());
                            agreementMasterBase.setRecruitId(resp.getBizTag());
                            agreementMasterBase.setId(agreementMasterBase.getRecruitId() + ":" + agreementMasterBase.getMasterId());
                            agreementMasterBase.setCooperationPrice(this.getCost(resp));
                            agreementMasterBase.setPricingMethod(pricingMethod);
                            agreementMasterBase.setRecruitTagName(agreementMasterMap.get(resp.getBizTag() + ":" + resp.getBizId()).getTagName());
                            agreementMasterBaseList.add(agreementMasterBase);
                        }

                    });

                    if(Objects.nonNull(agreementMasterMatchMap)){
                        feeList.forEach(feeResp -> {
                            String id = feeResp.getBizTag() + ":" + feeResp.getBizId();
                            agreementMasterMatchMap.get(id).setIsCalculatePriceSucc(1);
                            agreementMasterMatchMap.get(id).setCooperationPrice(this.getCost(feeResp));

                        });
                    }


                }else{

                    Map<String,ApplyOrderCalculateBatchResp> applyOrderCalculateBatchRespMap =  feeList.stream().collect(Collectors.toMap(ApplyOrderCalculateBatchResp::getBizTag,each->each,(value1, value2) -> value1));

                    agreementMasterExtraList.forEach(agreementMasterExtra -> {
                        ApplyOrderCalculateBatchResp resp = applyOrderCalculateBatchRespMap.get(String.valueOf(agreementMasterExtra.getRecruitId()));
                        if(Objects.nonNull(resp)){
                            AgreementMasterBase agreementMasterBase = new AgreementMasterBase();
                            agreementMasterBase.setMasterId(String.valueOf(agreementMasterExtra.getMasterId()));
                            agreementMasterBase.setRecruitId(resp.getBizTag());
                            agreementMasterBase.setId(agreementMasterBase.getRecruitId() + ":" + agreementMasterBase.getMasterId());
                            agreementMasterBase.setCooperationPrice(this.getCost(resp));
                            agreementMasterBase.setPricingMethod(pricingMethod);
                            agreementMasterBase.setRecruitTagName(agreementMasterMap.get(agreementMasterBase.getRecruitId() + ":" + agreementMasterBase.getMasterId()).getTagName());
                            agreementMasterBaseList.add(agreementMasterBase);
                        }


                    });


                    if(Objects.nonNull(agreementMasterMatchMap)){
                        if (CollectionUtils.isNotEmpty(agreementMasterBaseList)) {
                            agreementMasterBaseList.forEach(agreementMasterBase -> {
                                agreementMasterMatchMap.get(agreementMasterBase.getId()).setIsCalculatePriceSucc(1);
                                if (Objects.nonNull(agreementMasterBase.getCooperationPrice())) {
                                    agreementMasterMatchMap.get(agreementMasterBase.getId()).setCooperationPrice(agreementMasterBase.getCooperationPrice());
                                }
                            });
                        }
                    }

                }

            }



            if(Objects.nonNull(agreementMasterMatchMap)){
                List<ApplyOrderCalculateBatchResp> failFeeList = applyOrderCalculateBatchRespList.stream().filter(applyOrderCalculateBatchResp -> (!applyOrderCalculateBatchResp.getApplyOrderCalculateResp().getSceneResultList().get(0).isSuccess())).collect(Collectors.toList());


                if (CollectionUtils.isNotEmpty(failFeeList)) {
                    if ("master".equals(pricingMethod)) {
                        //按师傅计价
                        failFeeList.forEach(resp -> {
                            String agreementMasterId = resp.getBizTag() + ":" + resp.getBizId();
                            agreementMasterMatchMap.get(agreementMasterId).setIsCalculatePriceSucc(0);
                            String calculatePriceReason = "";
                            if (Objects.nonNull(resp.getApplyOrderCalculateResp()) && CollectionUtils.isNotEmpty(resp.getApplyOrderCalculateResp().getSceneResultList())) {
                                calculatePriceReason = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getErrorInfo();

                            }
                            agreementMasterMatchMap.get(agreementMasterId).setCalculatePriceFailReason(calculatePriceReason);
                        });
                    } else {
                        //按招募活动计价
                        Map<String, List<ApplyOrderCalculateBatchResp>> failOrderCalculateBatchRespMap = failFeeList.stream().collect(Collectors.groupingBy(applyOrderCalculateBatchResp -> applyOrderCalculateBatchResp.getBizTag()));

                        agreementMasterExtraList.forEach(master -> {
                            ApplyOrderCalculateBatchResp resp = failOrderCalculateBatchRespMap.get(master.getRecruitId().toString()).get(0);
                            if (Objects.nonNull(resp)) {

                                agreementMasterMatchMap.get(master.getId()).setIsCalculatePriceSucc(0);
                                String calculatePriceReason = "";
                                if (Objects.nonNull(resp.getApplyOrderCalculateResp()) && CollectionUtils.isNotEmpty(resp.getApplyOrderCalculateResp().getSceneResultList())) {
                                    calculatePriceReason = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getErrorInfo();

                                }
                                agreementMasterMatchMap.get(master.getId()).setCalculatePriceFailReason(calculatePriceReason);
                            }

                        });
                    }
                }



            }


        }catch(Exception e){
            log.error("getAgreementMasterCooperationPrice error",e);
        }


        return agreementMasterBaseList;
    }

    /**
     * 根据合作价格过滤师傅
     * @param agreementMasterList
     * @return
     */
    private List<AgreementMasterBase> getAgreementMasterMinCooperationPrice(List<AgreementMasterExtra> agreementMasterList, OrderDetailData orderDetailData,
                                                                            Map<String, EnterpriseOrderAgreementMasterMatch> agreementMasterMatchMap){

        List<AgreementMasterBase> agreementMasterBaseList = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(agreementMasterList)){
            Map<String, List<AgreementMasterExtra>> agreementMasterExtraMap = agreementMasterList.stream().collect(Collectors.groupingBy(item -> item.getPricingMethod()));
            agreementMasterExtraMap.keySet().forEach(pricingType -> {
                List<AgreementMasterBase> calculatePriceAgreementMasterBaseList = getAgreementMasterCooperationPrice(agreementMasterExtraMap.get(pricingType), orderDetailData, orderDetailData.getFourthDivisionId(), agreementMasterMatchMap);
                if (Objects.nonNull(calculatePriceAgreementMasterBaseList)
                        && CollectionUtil.isNotEmpty(calculatePriceAgreementMasterBaseList)) {
                    agreementMasterBaseList.addAll(calculatePriceAgreementMasterBaseList);
                }
            });
        }



        if(CollectionUtils.isNotEmpty(agreementMasterBaseList)){

            Map<String, List<AgreementMasterBase>> agreementMasterBaseMap = agreementMasterBaseList.stream().collect(Collectors.groupingBy(AgreementMasterBase::getMasterId));

            List<AgreementMasterBase> finalAgreementMasterBaseList = new ArrayList<>();
            agreementMasterBaseMap.keySet().forEach(masterId -> {
                List<AgreementMasterBase> tempAgreementMasterBaseList = agreementMasterBaseMap.get(masterId);

                if (tempAgreementMasterBaseList.size() > 1) {
                    AgreementMasterBase agreementMasterBase = tempAgreementMasterBaseList.stream()
                            .min(Comparator.comparing(AgreementMasterBase::getCooperationPrice)).orElse(null);
                    for (AgreementMasterBase item : tempAgreementMasterBaseList) {
                        if (item.getId().equals(agreementMasterBase.getId())) {
                            finalAgreementMasterBaseList.add(agreementMasterBase);
                        } else {
                            if (Objects.nonNull(agreementMasterMatchMap.get(item.getId()))) {
                                agreementMasterMatchMap.get(item.getId()).setIsFilter(1);
                                agreementMasterMatchMap.get(item.getId()).setFilterReason("相同师傅不同招募时取最低价逻辑过滤！");
                            }
                        }
                    }
                } else {
                    finalAgreementMasterBaseList.add(tempAgreementMasterBaseList.get(0));
                }

            });

            return finalAgreementMasterBaseList;
        }

        return agreementMasterBaseList;
    }



    private BigDecimal getCost(ApplyOrderCalculateBatchResp resp){
        BigDecimal cost = resp.getApplyOrderCalculateResp().getCost();
        BigDecimal basePrice = resp.getApplyOrderCalculateResp().getSceneResultList().get(0).getBasePrice();
        if(Objects.isNull(basePrice)){
            basePrice = BigDecimal.ZERO;
        }
        return cost.compareTo(basePrice) >= 0 ? cost : basePrice;
    }





    @Override
    public Integer matchTechniqueVerifyMaster(MatchSupportMasterRqt rqt) {


            Long globalOrderId = rqt.getGlobalOrderTraceId();
            OrderDetailData orderDetailData = orderDataBuilder.buildEnterpriseOrder(globalOrderId);
            if (Objects.isNull(orderDetailData)) {
                return null;
            }

            if (apolloConfigUtils.checkIsNoPushCity(orderDetailData.getSecondDivisionId())) {
                insertAgreementMasterMatch(orderDetailData, "该城市订单不派单");
                return null;
            }


            log.info("orderDetailData:" + JSON.toJSONString(orderDetailData));


            MasterMatchCondition masterMatchCondition = new MasterMatchCondition();
            masterMatchCondition.setThirdDivisionId(orderDetailData.getThirdDivisionId());
            masterMatchCondition.setFourthDivisionId(orderDetailData.getFourthDivisionId());
            OrderMasterMatcher orderMasterMatcher = OrderMasterMatcherFactory.build(PushMode.ENTERPRISE_TECHNIQUE_VERIFY_MASTER);
            orderMasterMatcher.executeMatch(orderDetailData,masterMatchCondition);

            return 1;

        }








}
