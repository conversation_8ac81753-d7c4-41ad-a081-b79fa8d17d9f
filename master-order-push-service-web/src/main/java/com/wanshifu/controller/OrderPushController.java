package com.wanshifu.controller;

import com.alibaba.fastjson.JSON;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.configuration.HandlerMapping;
import com.wanshifu.master.information.domain.api.request.settle.SaveSettleAuditConfigRqt;
import com.wanshifu.master.order.push.domain.common.MatchSupportMasterRqt;
import com.wanshifu.master.order.push.domain.dto.*;
import com.wanshifu.master.order.push.domain.message.*;
import com.wanshifu.master.order.push.domain.po.DistributeResultNotices;
import com.wanshifu.master.order.push.domain.resp.orderPush.GetOrderPushScoreResp;
import com.wanshifu.master.order.push.domain.rqt.*;
import com.wanshifu.master.order.push.domain.rqt.orderPush.GetOrderPushScoreRqt;
import com.wanshifu.master.order.push.service.*;
import com.wanshifu.master.order.push.service.impl.NormalOrderAgreementAutoGrabService;
import com.wanshifu.master.order.push.service.impl.OrderDataBuilder;
import com.wanshifu.master.order.push.util.SpringContextUtil;
import com.wanshifu.mq.comsumeController.OrderDistributeResultNoticeConsumeController;
import com.wanshifu.mq.comsumeController.OrderPushResultNoticeConsumeController;
import com.wanshifu.order.offer.domains.vo.OrderCanceledAutoGrabMessage;
import com.wanshifu.order.offer.domains.vo.publish.OrderPackageConfirmMessage;
import com.wanshifu.order.offer.domains.vo.push.OrderPushNotices;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 推单Controller
 * <AUTHOR>
 */
@RestController
@RequestMapping("/order/push")
@Slf4j
public class OrderPushController {

    @Resource
    private OrderPushService orderPushService;

    @Resource
    private OrderMatchMasterService orderMatchMasterService;

    @Resource
    private PushControllerFacade pushControllerFacade;


    @Autowired
    private HandlerMapping handlerMapping;

    @Autowired
    private DynamicRoundsPushService dynamicRoundsPushService;


    @Resource
    private MasterOperationNotifyService masterOperationNotifyService;


    @Resource
    private EnterpriseAppointMasterService enterpriseAppointMasterService;

    @Resource
    private LongTailPushService longTailPushService;

    @Resource
    private NormalOrderAgreementAutoGrabService normalOrderAgreementAutoGrabService;

    @PostMapping("test")
    public Integer test() {
        final OrderPushServiceImpl orderPushServiceImpl = (OrderPushServiceImpl)orderPushService;
        final OrderDataBuilder bean = SpringContextUtil.getBean(OrderDataBuilder.class);
        final OrderMatchMasterRqt orderMatchMasterRqt = new OrderMatchMasterRqt();
        orderMatchMasterRqt.setMasterOrderId(61329463884L);
        orderMatchMasterRqt.setBusinessLineId(1);

        OrderDetailData orderDetailData = bean.build(orderMatchMasterRqt);

        orderDetailData.setSecondDivisionId(440300L);
        orderDetailData.setOrderCategoryId(1L);
        orderDetailData.setBusinessLineId(2);

        System.out.println(JSON.toJSONString(orderDetailData));

//        orderPushServiceImpl.longTailPushOpen(orderDetailData,"313",13);
//------------------------------------------------------------------------------------
        final OrderPushRqt orderPushRqt = new OrderPushRqt();
        orderPushRqt.setOrderLngLat("{\"buyerAddressLatitude\":23.0020520,\"buyerAddressLongitude\":116.1627580}");
        orderPushRqt.setMasterOrderId(61329488309L);
        orderPushRqt.setBusinessLineId(1);
        orderPushRqt.setGlobalOrderId(61329488262L);
        orderPushRqt.setBindingTechnologyIds("1026|1033|1054|1057|1065|1086|1090|1099|1120|1122|1125|1127|1131|1133|1134|1141|2100|2101|2102|2107|2199");
        orderPushRqt.setSecondDivisionId(440300L);
        orderPushRqt.setThirdDivisionId(440306L);

        orderPushServiceImpl.orderPush(orderPushRqt);

        return 1;
    }

    @PostMapping("repushSync")
    public Integer repushSync(@RequestParam("globalOrderId") Long globalOrderId) {
        return orderPushService.repushSync(globalOrderId);
    }



    @PostMapping("dropHistoryPushTable")
    public Integer dropHistoryPushTable() {
        return orderPushService.dropHistoryPushTable();
    }


    @PostMapping("batchOrderMatch")
    public Integer batchOrderMatch(@Valid @RequestBody BatchOrderMatchRqt rqt) {
        return orderPushService.batchOrderMatch(rqt);
    }



    @PostMapping("orderMatchMaster")
    public Integer batchOrderMatch(@Valid @RequestBody OrderMatchMasterRqt rqt) {
        return orderMatchMasterService.match(rqt);
    }


    @PostMapping("delayPush")
    public Integer delayPush(@Valid @RequestBody DelayPushRqt rqt) {
        pushControllerFacade.delayPush(rqt);
        return 1;
    }


    @PostMapping("longTailPush")
    public Integer longTailPush(@Valid @RequestBody LongTailOrderPushRqt rqt) {
        longTailPushService.longTailPush(rqt);
        return 1;
    }


    @PostMapping("longTailBatchPush")
    public Integer longTailPush(@Valid @RequestBody LongTailBatchPushMessage rqt) {
        longTailPushService.longTailBatchPush(rqt);
        return 1;
    }


    @PostMapping("masterSettleIn")
    public Integer masterSettleIn(@Valid @RequestBody MasterSettleInRqt rqt) {
        masterOperationNotifyService.masterSettleIn(rqt);
        return 1;
    }


    @PostMapping("distributeResultNotices")
    public Integer distributeResultNotices(@Valid @RequestBody DistributeResultNotices resultNotices) {
        orderMatchMasterService.distributeResultNotices(resultNotices);
        return 1;
    }


    @PostMapping("compensateDistribute")
    public Integer compensateDistribute(@Valid @RequestBody CompensateDistributeMessage compensateDistributeMessage) {
        orderMatchMasterService.compensateDistribute(compensateDistributeMessage);
        return 1;
    }


    @PostMapping("getOrderPushScore")
    public GetOrderPushScoreResp getOrderPushScore(@Valid @RequestBody GetOrderPushScoreRqt rqt) {
        return orderMatchMasterService.getOrderPushScore(rqt);
    }


    @PostMapping("wheelRoundsPush")
    public Integer wheelRoundsPush(@Valid @RequestBody WheelRoundsPushMessage rqt) {
        return dynamicRoundsPushService.wheelRoundsPush(rqt);
    }


    @PostMapping("repushOrder")
    public Integer repushOrder(@Valid @RequestBody RepushOrderRqt rqt) {
        return orderPushService.repushOrder(rqt);
    }


    @PostMapping("matchSupportMaster")
    public Integer matchSupportMaster(@Valid @RequestBody MatchSupportMasterRqt rqt) {
        orderMatchMasterService.matchSupportMaster(rqt);
        return 1;
    }


    @PostMapping("orderPackageConfirm")
    public Integer orderPackageConfirm(@Valid @RequestBody OrderPackageConfirmMessage rqt) {
        orderMatchMasterService.orderPackageConfirm(rqt);
        return 1;
    }


    @PostMapping("afreshPush")
    public Integer afreshPush(@Valid @RequestBody OrderAfreshPushRqt rqt) {
        orderPushService.afreshPush(rqt);
        return 1;
    }


    @PostMapping("orderCancelAutoGrab")
    public Integer orderCancelAutoGrab(@Valid @RequestBody OrderCanceledAutoGrabMessage rqt) {
        orderMatchMasterService.orderCancelAutoGrab(rqt);
        return 1;
    }


    @PostMapping("masterServeFinish")
    public Integer masterServeFinish(@Valid @RequestBody MasterServeFinishMessage message) {
        masterOperationNotifyService.masterServeFinish(message);
        return 1;
    }


    @PostMapping("enterpriseAppoint")
    public Long enterpriseAppoint(@Valid @RequestBody EnterpriseAppointMessage message) {
        return enterpriseAppointMasterService.matchAgreementMaster(message);
    }



    @PostMapping("addTechnique")
    public Integer addTechnique(@Valid @RequestBody MasterAddTechniqueMessage message) {
        masterOperationNotifyService.addTechnique(message);
        return 1;
    }


    @PostMapping("addServeFourthDivision")
    public Integer addServeFourthDivision(@Valid @RequestBody MasterAddFourthDivisionMessage message) {
        masterOperationNotifyService.addServeFourthDivision(message);
        return 1;
    }


    @PostMapping("orderPushNotices")
    public Integer orderPushNotices(@Valid @RequestBody OrderPushNotices message) {
        orderMatchMasterService.orderPushNotices(message);
        return 1;
    }


    @PostMapping("confirmAppointMaster")
    public Integer confirmAppointMaster(@Valid @RequestBody ConfirmAppointMasterMessage message) {
        orderMatchMasterService.confirmAppointMaster(message);
        return 1;
    }


    @Resource
    private OrderDistributeResultNoticeConsumeController orderDistributeResultNoticeConsumeController;

    @Resource
    private OrderPushResultNoticeConsumeController orderPushResultNoticeConsumeController;


    @PostMapping("orderPushedResultNotice")
    public Integer orderPushedResultNotice(@Valid @RequestBody OrderPushedResultNotice message) {
        orderPushResultNoticeConsumeController.orderPushedResultNotice(message,null);
        return 1;
    }

    @PostMapping("orderDistributeResultNotice")
    public Integer orderDistributeResultNotice(@Valid @RequestBody OrderDistributeResultMessage message) {
        orderDistributeResultNoticeConsumeController.orderDistributeResultNotice(message,null);
        return 1;
    }
    @PostMapping("portPush")
    public Integer portPush(@Valid @RequestBody PortPushMessage rqt) {
        orderMatchMasterService.portPush(rqt);
        return 1;
    }





    @PostMapping("agreementAutoReceive")
    public Integer agreementAutoReceive(@Valid @RequestBody CompensateDistributeMessage message) {
        normalOrderAgreementAutoGrabService.agreementAutoReceive(message);
        return 1;
    }


    @PostMapping("masterOfferPrice")
    public Integer masterOfferPrice(@Valid @RequestBody MasterOfferPriceMessage message) {
        masterOperationNotifyService.masterOfferPrice(message);
        return 1;
    }



    @PostMapping("pushGoldMedalMaster")
    public Integer pushGoldMedalMaster(@Valid @RequestBody PushGoldMedalMasterMessage message) {
        orderMatchMasterService.pushGoldMedalMaster(message);
        return 1;
    }


    @PostMapping("masterModifyOfferPrice")
    public Integer masterModifyOfferPrice(@Valid @RequestBody MasterModifyOfferPriceMessage message) {
        masterOperationNotifyService.masterModifyOfferPrice(message);
        return 1;
    }


    @PostMapping("orderChangeGrabPush")
    public Integer orderChangeGrabPush(@Valid @RequestBody OrderChangeGrabPushMessage message) {
        orderMatchMasterService.orderChangeGrabPush(message);
        return 1;
    }


    @PostMapping("tocMasterAtTobAppOfferLimit")
    public Integer tocMasterAtTobAppOfferLimit(@Valid @RequestBody TocMasterAtTobAppOfferLimitMessage message) {
        masterOperationNotifyService.tocMasterAtTobAppOfferLimit(message);
        return 1;
    }

    @PostMapping("matchTechniqueVerifyMaster")
    public Integer matchTechniqueVerifyMaster(@Valid @RequestBody MatchSupportMasterRqt message) {
        enterpriseAppointMasterService.matchTechniqueVerifyMaster(message);
        return 1;
    }


}
