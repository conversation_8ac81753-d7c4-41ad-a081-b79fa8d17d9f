package com.wanshifu.controller;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.api.SpecialGroupStrategyApi;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy.*;
import com.wanshifu.master.order.push.service.SpecialGroupStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 特殊人群策略控制器
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/specialGroupStrategy")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SpecialGroupStrategyController implements SpecialGroupStrategyApi {

    @Resource
    private SpecialGroupStrategyService specialGroupStrategyService;

    /**
     * 创建特殊人群策略
     * 
     * @param rqt 创建请求
     * @return 策略id
     */
    @Override
    @PostMapping("/create")
    public int create(@RequestBody @Valid CreateRqt rqt) {
        return specialGroupStrategyService.create(rqt);
    }

    /**
     * 更新特殊人群策略
     * 
     * @param rqt 更新请求
     * @return 更新结果
     */
    @Override
    @PostMapping("/update")
    public int update(@RequestBody @Valid UpdateRqt rqt) {
        return specialGroupStrategyService.update(rqt);
    }

    /**
     * 获取特殊人群策略详情
     * 
     * @param rqt 详情请求
     * @return 策略详情
     */
    @Override
    @PostMapping("/detail")
    public SpecialGroupStrategy detail(@RequestBody @Valid DetailRqt rqt) {
        return specialGroupStrategyService.detail(rqt);
    }

    /**
     * 分页查询特殊人群策略列表
     * 
     * @param rqt 列表查询请求
     * @return 分页结果
     */
    @Override
    @PostMapping("/list")
    public SimplePageInfo<SpecialGroupStrategy> list(@RequestBody @Valid ListRqt rqt) {
        return specialGroupStrategyService.list(rqt);
    }

    /**
     * 启用/禁用特殊人群策略
     * 
     * @param rqt 启用/禁用请求
     * @return 操作结果
     */
    @Override
    @PostMapping("/enable")
    public Integer enable(@RequestBody @Valid EnableRqt rqt) {
        return specialGroupStrategyService.enable(rqt);
    }

    /**
     * 删除特殊人群策略
     * 
     * @param rqt 删除请求
     * @return 删除结果
     */
    @Override
    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid DeleteRqt rqt) {
        return specialGroupStrategyService.delete(rqt);
    }
}
