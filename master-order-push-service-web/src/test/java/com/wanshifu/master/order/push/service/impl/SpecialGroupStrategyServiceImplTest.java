package com.wanshifu.master.order.push.service.impl;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.master.order.push.domain.po.SpecialGroupStrategy;
import com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy.*;
import com.wanshifu.master.order.push.repository.SpecialGroupStrategyRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 特殊人群策略服务测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@RunWith(MockitoJUnitRunner.class)
public class SpecialGroupStrategyServiceImplTest {

    @Mock
    private SpecialGroupStrategyRepository specialGroupStrategyRepository;

    @InjectMocks
    private SpecialGroupStrategyServiceImpl specialGroupStrategyService;

    @Test
    public void testCreate() {
        // 准备测试数据
        CreateRqt rqt = new CreateRqt();
        rqt.setStrategyName("测试策略");
        rqt.setStrategyDesc("测试策略描述");
        rqt.setServeIds("1,2,3");
        rqt.setRegionLevel("city");
        rqt.setCityIds("1,2,3");
        rqt.setPushGroups("group1,group2");
        rqt.setPushGroupsExpression("expression");
        rqt.setServeModel(1L);
        rqt.setDelayMinutes(30);
        rqt.setFilterGroups("filter1,filter2");
        rqt.setFilterGroupsExpression("filter_expression");
        rqt.setCreateAccountId(1001L);

        // Mock 方法调用
        when(specialGroupStrategyRepository.selectByStrategyName(anyString(), any())).thenReturn(null);
        when(specialGroupStrategyRepository.insert(anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), any(), any(), anyString(), anyString(), any()))
                .thenReturn(1);

        // 执行测试
        int result = specialGroupStrategyService.create(rqt);

        // 验证结果
        assertEquals(1, result);
        verify(specialGroupStrategyRepository, times(1)).selectByStrategyName(eq("测试策略"), eq(null));
        verify(specialGroupStrategyRepository, times(1)).insert(
                eq("测试策略"), eq("测试策略描述"), eq("1,2,3"), eq("city"),
                eq("1,2,3"), eq("group1,group2"), eq("expression"), eq(1L),
                eq(30), eq("filter1,filter2"), eq("filter_expression"), eq(1001L));
    }

    @Test
    public void testUpdate() {
        // 准备测试数据
        UpdateRqt rqt = new UpdateRqt();
        rqt.setStrategyId(1);
        rqt.setStrategyName("更新策略");
        rqt.setStrategyDesc("更新策略描述");
        rqt.setUpdateAccountId(1002L);

        // Mock 方法调用
        when(specialGroupStrategyRepository.selectByStrategyName(anyString(), any())).thenReturn(null);
        when(specialGroupStrategyRepository.update(any(), anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), any(), any(), anyString(), anyString(), any()))
                .thenReturn(1);

        // 执行测试
        int result = specialGroupStrategyService.update(rqt);

        // 验证结果
        assertEquals(1, result);
        verify(specialGroupStrategyRepository, times(1)).selectByStrategyName(eq("更新策略"), eq(1));
    }

    @Test
    public void testDetail() {
        // 准备测试数据
        DetailRqt rqt = new DetailRqt();
        rqt.setStrategyId(1);

        SpecialGroupStrategy strategy = new SpecialGroupStrategy();
        strategy.setStrategyId(1);
        strategy.setStrategyName("测试策略");

        // Mock 方法调用
        when(specialGroupStrategyRepository.selectByPrimaryKey(1)).thenReturn(strategy);

        // 执行测试
        SpecialGroupStrategy result = specialGroupStrategyService.detail(rqt);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getStrategyId());
        assertEquals("测试策略", result.getStrategyName());
    }

    @Test
    public void testEnable() {
        // 准备测试数据
        EnableRqt rqt = new EnableRqt();
        rqt.setStrategyId(1);
        rqt.setStrategyStatus(1);
        rqt.setUpdateAccountId(1003L);

        // Mock 方法调用
        when(specialGroupStrategyRepository.updateStatus(1, 1, 1003L)).thenReturn(1);

        // 执行测试
        Integer result = specialGroupStrategyService.enable(rqt);

        // 验证结果
        assertEquals(Integer.valueOf(1), result);
        verify(specialGroupStrategyRepository, times(1)).updateStatus(1, 1, 1003L);
    }

    @Test
    public void testDelete() {
        // 准备测试数据
        DeleteRqt rqt = new DeleteRqt();
        rqt.setStrategyId(1);
        rqt.setUpdateAccountId(1004L);

        // Mock 方法调用
        when(specialGroupStrategyRepository.softDeleteWithUpdateAccountId(1, 1004L)).thenReturn(1);

        // 执行测试
        Integer result = specialGroupStrategyService.delete(rqt);

        // 验证结果
        assertEquals(Integer.valueOf(1), result);
        verify(specialGroupStrategyRepository, times(1)).softDeleteWithUpdateAccountId(1, 1004L);
    }
}
