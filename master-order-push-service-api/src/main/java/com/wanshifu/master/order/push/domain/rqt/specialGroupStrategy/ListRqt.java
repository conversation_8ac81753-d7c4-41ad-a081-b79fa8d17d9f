package com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy;

import com.wanshifu.framework.core.page.Pager;
import lombok.Data;

import java.util.Date;

/**
 * 特殊人群策略列表查询请求类
 *
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Data
public class ListRqt extends Pager {

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 服务id集合
     */
    private String serveIds;

    /**
     * 创建开始时间
     */
    private Date createStartTime;

    /**
     * 创建结束时间
     */
    private Date createEndTime;

    /**
     * 策略状态
     */
    private Integer strategyStatus;
}
