package com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * 创建特殊人群策略请求类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Data
public class CreateRqt {

    /**
     * 策略名称
     */
    @NotEmpty(message = "策略名称不能为空")
    private String strategyName;

    /**
     * 策略描述
     */
    private String strategyDesc;

    /**
     * 服务id集合
     */
    private String serveIds;

    /**
     * 区域级别
     */
    private String regionLevel;

    /**
     * 城市id集合
     */
    private String cityIds;

    /**
     * 调度人群
     */
    private String pushGroups;

    /**
     * 调度人群表达式
     */
    private String pushGroupsExpression;

    /**
     * 下单模式
     */
    private Long serveModel;

    /**
     * 延迟推送下一优先级的时间（分钟）
     */
    private Integer delayMinutes;

    /**
     * 过滤人群
     */
    private String filterGroups;

    /**
     * 过滤人群表达式
     */
    private String filterGroupsExpression;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空")
    private Long createAccountId;
}
