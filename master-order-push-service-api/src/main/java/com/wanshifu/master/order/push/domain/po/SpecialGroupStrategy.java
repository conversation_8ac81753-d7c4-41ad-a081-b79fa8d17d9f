package com.wanshifu.master.order.push.domain.po;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Table(name = "special_group_strategy")
public class SpecialGroupStrategy {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "strategy_id")
    private Integer strategyId;


    @Column(name = "strategy_name")
    private String strategyName;

    @Column(name = "strategy_desc")
    private String strategyDesc;

    @Column(name = "serve_ids")
    private String serveIds;

    @Column(name = "region_level")
    private String regionLevel;

    @Column(name = "city_ids")
    private String cityIds;

    /**
     * 调度人群
     */
    @Column(name = "push_groups")
    private String pushGroups;


    /**
     * 调度人群表达式
     */
    @Column(name = "push_groups_expression")
    private String pushGroupsExpression;


    /**
     * 下单模式
     */
    @Column(name = "serve_model")
    private Long serveModel;

    /**
     * 延迟推送下一优先级的时间（分钟）
     */
    @Column(name = "delay_minutes")
    private Integer delayMinutes;

    /**
     * 过滤人群
     */
    @Column(name = "filter_groups")
    private String filterGroups;


    /**
     * 过滤人群表达式
     */
    @Column(name = "filter_groups_expression")
    private String filterGroupsExpression;


    @Column(name = "strategy_status")
    private Integer strategyStatus;


    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @Column(name = "create_account_id")
    private Long createAccountId;


    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    /**
     * 更新人id
     */
    @Column(name = "update_account_id")
    private Long updateAccountId;


}
