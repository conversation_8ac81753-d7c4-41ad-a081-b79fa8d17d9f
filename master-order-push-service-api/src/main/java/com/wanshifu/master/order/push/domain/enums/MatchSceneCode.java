package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


/**
 * 匹配师傅场景
 */
@AllArgsConstructor
@Getter
public enum MatchSceneCode {

    AFRESH_NEW_MODEL_SINGLE("afresh_new_model_single", "重新推送主力师傅"),

    COOPERATION_BUSINESS("cooperation_business", "推送合作经营师傅"),

    FULL_TIME_MASTER("full_time_master", "推送全时师傅"),

    EXTRA_CONTEST_OFFER_NUMBER("extra_contest_offer_number", "额外竞报扩充名额"),

    NEW_MODEL_RECOMMEND_MASTER_LIST("new_model_recommend_master_list", "样板订单推荐师傅列表"),

    ORDER_CREATE("order_create", "创建订单"),

    MASTER_OFFER_PRICE("master_offer_price", "师傅报价"),

    NONE_OFFER_PRICE("none_offer_price", "无人报价"),

    INTERFERE_ORDER_PUSH("interfere_order_push","平台订单干预"),

    FAMILY_AGREEMENT_PUSH_NORMAL("family_agreement_push_normal","家庭协议师傅转推普通师傅"),

    TOC_MASTER_AT_TOB_APP_OFFER_LIMIT("toc_master_at_tob_app_offer_limit","家庭师傅B端达到报价上限"),

    ORDER_CHANGE_GRAB("order_change_grab","订单改派"),

    AFRESH_PUSH("afresh_push","订单重推");


    private final String code;

    private final String desc;



    private static final Map<String, MatchSceneCode> valueMapping = new HashMap<>((int) (MatchSceneCode.values().length / 0.75));


    static {
        for (MatchSceneCode instance : MatchSceneCode.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static MatchSceneCode asCode(String code) {
        return valueMapping.get(code);
    }

}
