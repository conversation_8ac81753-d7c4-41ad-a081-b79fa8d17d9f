package com.wanshifu.master.order.push.domain.es;

import com.google.common.collect.Lists;
import com.wanshifu.master.order.push.domain.annotation.Document;
import com.wanshifu.master.order.push.domain.annotation.Field;
import com.wanshifu.master.order.push.domain.annotation.FieldType;
import lombok.Data;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;

/**
 * 筛选师傅
 * <AUTHOR>
 * @date 2023-12-25 16:45:00
 */
@Data
@Component
@Document(indexAlias = "master_base", type = "master_base_info")
public class MasterBaseSearch {

    /**
     * 师傅id
     */
    @Field(type = FieldType.Long)
    private String masterId;

    /**
     * 账号冻结时间
     */
    @Field(type = FieldType.Long)
    private Long freezingTime;

    /**
     * 师傅已选择技能
     */
    @Field(type = FieldType.Text)
    private String masterTechniqueIds;

    /**
     * 账号状态是否正常
     */
    @Field(type = FieldType.Long)
    private Long isAccountNormal;

    /**
     * 师傅开工状态
     */
    @Field(type = FieldType.Long)
    private Long restState;

    /**
     * 入驻状态是否正常
     */
    @Field(type = FieldType.Long)
    private Long isSettleStatusNormal;

    /**
     * 违规考试状态是否正常
     */
    @Field(type = FieldType.Long)
    private Long isRuleExamStatusNormal;

    /**
     * 黑名单状态是否正常
     */
    @Field(type = FieldType.Long)
    private Long isBlackListStatusNormal;

    /**
     * 退单状态是否正常
     */
    @Field(type = FieldType.Long)
    private Long isPushRestrictNormal;


    /**
     * 师傅所在地经纬度
     */
    @Field(type = FieldType.Text)
    private String latLng;


    /**
     * 最近活跃时间
     */
    @Field(type = FieldType.Long)
    private Long lastActiveTime;


    /**
     * 禁止推单订单服务
     */
    @Field(type = FieldType.Text)
    private String masterForbiddenServeIds;


    /**
     * 服务区域（三级地址）
     */
    @Field(type = FieldType.Text)
    private String serveDivisionIds;

    /**
     * 服务区域（四级地址）
     */
    @Field(type = FieldType.Text)
    private String serveFourthDivisionIds;

    /**
     师傅品牌
     */
    @Field(type = FieldType.Text)
    private String masterBrand;

    /**
     * 师傅禁止推单业务线id
     */
    @Field(type = FieldType.Text)
    private String masterForbiddenBusinessIds;


    /**
     * 师傅禁止推单业务线id
     */
    @Field(type = FieldType.Long)
    private Long cityDivisionId;



    /**
     * 师傅身份证号
     */
    @Field(type = FieldType.Keyword)
    private String idCardNumber;


    /**
     * 师傅来源类型，tob: B端师傅，toc: C端师傅
     */
    @Field(type = FieldType.Keyword)
    private String masterSourceType;

    /**
     * 合作经营师傅字段
     * 是否限制推单 0:不限制,1:限制
     */
    @Field(type = FieldType.Integer)
    private Integer isRestrictPushOrder;

    /**
     * 合作经营师傅字段
     * 是否开启派单 1:开启,0:关闭
     */
    @Field(type = FieldType.Integer)
    private Integer isDistributeOrder;


    /**
     * 合作经营师傅字段
     * 最近结束合作时间
     * 0: 没有结束合作
     */
    @Field(type = FieldType.Long)
    private Long finishCooperativeTime;

    /**
     * 合作经营师傅字段
     * 师傅每日接单(接合作经营订单)上限
     * 0:未设置每日接单上线，不限制
     */
    @Field(type = FieldType.Integer)
    private Integer receiveOrderLimit;

    /**
     * 合作经营师傅字段
     * 接单范围(师傅所在地与合作经营订单距离)，单位：公里
     * 0：未设置接单范围，不限制
     */
    @Field(type = FieldType.Integer)
    private Integer receiveOrderDistanceRange;

    /**
     * 合作经营师傅字段
     * 师傅每日抢单(抢合作经营订单)上限
     * 0:未设置每日抢单上线，不限制
     */
    @Field(type = FieldType.Integer)
    private Integer grabOrderLimit;

    /**
     * 合作经营师傅字段
     * 师傅可接单区域(4级地址)
     */
    @Field(type = FieldType.Text)
    @Deprecated
    private String receiveOrderFourthDivisionIds;

    /**
     * 合作经营师傅字段
     * 师傅可接单区域(4级地址)
     */
    @Field(type = FieldType.Text)
    private String receiveOrderFourthDivisionIdsAnalyzed;


    /**
     * 合作经营师傅每日报价已满报价次数
     */
    @Field(type = FieldType.Integer)
    private Integer additionalQuoteOrderNum;


    /**
     * 金牌维修师傅状态
     */
    @Field(type = FieldType.Integer)
    private Integer goldMedalMasterStatus;


    /**
     * 金牌维修师傅状态
     */
    @Field(type = FieldType.Long)
    private Long supportMasterPushStatus;


    /**
     * 金牌维修师傅状态
     */
    @Field(type = FieldType.Text)
    private String goldMedalMasterCityDivisionIds;


    /**
     * 金牌维修师傅地区设置类型
     */
    @Field(type = FieldType.Integer)
    private Integer goldMedalMasterDivisionSetType;



    /**
     * 合作经营师傅周抢单量上限
     */
    @Field(type = FieldType.Integer)
    private Integer cooperationBusinessWeekReceiveOrderLimit;


    /**
     * 合作经营师傅周指派量上限
     */
    @Field(type = FieldType.Integer)
    private Integer cooperationBusinessWeekGrabOrderLimit;



    /**
     * 待确认技能验证的类目
     */
    @Field(type = FieldType.Text)
    private String waitConfirmTechniqueVerifyCategoryIds;


    /**
     * 待技能验证派单的类目
     */
    @Field(type = FieldType.Text)
    private String toTechniqueVerifyCategoryIds;

    /**
     * 技能验证中的类目
     */
    @Field(type = FieldType.Text)
    private String techniqueVerifyingCategoryIds;


    /**
     * 技能验证失败的类目
     */
    @Field(type = FieldType.Text)
    private String techniqueVerifyFailedCategoryIds;



    /**
     * 技能验证暂时完成的类目
     */
    @Field(type = FieldType.Text)
    private String techniqueVerifyTempCompletedCategoryIds;


    /**
     * 技能验证完成的类目
     */
    @Field(type = FieldType.Text)
    private String techniqueVerifyCompletedCategoryIds;


    /**
     * 技能验证挂起的类目
     */
    @Field(type = FieldType.Text)
    private String techniqueVerifyPauseCategoryIds;


    /**
     * 全时/分时师傅
     */
    @Field(type = FieldType.Integer)
    private Integer masterTimeType;


    /**
     * 技能验证后师傅字段
     * 是否开启派单 1:开启,0:关闭
     */
    @Field(type = FieldType.Integer)
    private Integer afterTechniqueVerifyIsDistributeOrder;

    /**
     * 技能验证后师傅字段
     * 可派单类目(技能验证后的类目)
     */
    @Field(type = FieldType.Text)
    private String afterTechniqueVerifyCategoryIds;



    /**
     * 全时师傅派单状态
     */
    @Field(type = FieldType.Integer)
    private Integer fullTimeMasterDispatchStatus;


    /**
     * 全时师傅可接单区域（四级地址）
     */
    @Field(type = FieldType.Text)
    private String fullTimeMasterServeFourthDivisionIds;


    /**
     * 全时师傅可接单服务
     */
    @Field(type = FieldType.Text)
    private String fullTimeMasterServeIds;


    /**
     * 全时师傅可接单距离范围
     */
    @Field(type = FieldType.Integer)
    private Integer fullTimeMasterServeDistanceRange;



    /**
     * 全时师傅每日抢单量上限
     */
    @Field(type = FieldType.Integer)
    private Integer fullTimeMasterDailyGrabOrderLimit;


    /**
     * 全时师傅每日指派量上限
     */
    @Field(type = FieldType.Integer)
    private Integer fullTimeMasterDailyAppointOrderLimit;



    /**
     * 全时师傅周抢单量上限
     */
    @Field(type = FieldType.Integer)
    private Integer fullTimeMasterWeekGrabOrderLimit;


    /**
     * 全时师傅周指派量上限
     */
    @Field(type = FieldType.Integer)
    private Integer fullTimeMasterWeekAppointOrderLimit;




    /**
     * 金牌维修师傅服务区域（四级地址）
     */
    @Field(type = FieldType.Text)
    private String goldMedalMasterFourthDivisionIds;




}

