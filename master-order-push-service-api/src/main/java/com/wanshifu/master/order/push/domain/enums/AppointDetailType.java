package com.wanshifu.master.order.push.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 订单调度类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum AppointDetailType {

    /**
     * 自动报价-协议师傅
     */
    AUTO_OFFER_AGREEMENT(200500L ,"自动报价-协议师傅"),


    /**
     * 自动抢单-协议师傅
     */
    AUTO_GRAB_AGREEMENT(400500L, "自动抢单-协议师傅"),


    /**
     * 自动抢单-样板师傅
     */
    AUTO_GRAB_NEW_MODEL(400600L,"自动抢单-样板师傅"),


    /**
     * 自动抢单-合作经营师傅
     */
    AUTO_GRAB_COOPERATION_BUSINESS(400700L,"自动抢单-合作经营师傅"),


    /**
     * 自动报价-作业帮
     */
    AUTO_OFFER_ZUOYEBANG(200520L,"自动报价-作业帮"),


    /**
     * 自动报价-协议师傅兜底
     */
    AUTO_OFFER_AGREEMENT_UNDERTAKE(200510L,"自动报价-协议师傅兜底"),


    /**
     * 自动抢单-协议师傅兜底
     */
    AUTO_GRAB_AGREEMENT_UNDERTAKE(400510L,"自动抢单-协议师傅兜底"),



    /**
     * 自动报价-师傅自动接单
     */
    AUTO_OFFER_MASTER(200300L,"自动报价-师傅自动接单"),


    /**
     * 自动抢单-师傅自动接单
     */
    AUTO_GRAB_MASTER(400300L,"自动抢单-师傅自动接单"),


    /**
     * 自动抢单-样板师傅
     */
    AUTO_GRAB_TECHNIQUE_VERIFY(400800L,"自动抢单-技能验证单"),


    /**
     * 自动抢单-技能验证后派单
     */
    AUTO_GRAB_AFTER_TECHNIQUE_VERIFY(400900L, "自动抢单-技能验证后派单"),

    /**
     * 自动抢单-全时师傅
     */
    AUTO_GRAB_FULL_TIME_MASTER(400910L,"自动抢单-全时师傅");

    private final Long code;


    private final String desc;



    private static final Map<Long, AppointDetailType> valueMapping = new HashMap<>((int) (AppointDetailType.values().length / 0.75));



    static {
        for (AppointDetailType instance : AppointDetailType.values()) {
            valueMapping.put(instance.code, instance);
        }
    }

    public static AppointDetailType asCode(String code) {
        return valueMapping.get(code);
    }
}
