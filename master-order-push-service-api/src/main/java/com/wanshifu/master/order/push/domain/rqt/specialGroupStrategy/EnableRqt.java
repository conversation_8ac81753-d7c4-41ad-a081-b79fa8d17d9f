package com.wanshifu.master.order.push.domain.rqt.specialGroupStrategy;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 启用/禁用特殊人群策略请求类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-05
 */
@Data
public class EnableRqt {

    /**
     * 策略id
     */
    @NotNull(message = "策略id不能为空")
    private Integer strategyId;

    /**
     * 策略状态 1:启用 0:禁用
     */
    @NotNull(message = "策略状态不能为空")
    private Integer strategyStatus;

    /**
     * 更新人id
     */
    @NotNull(message = "更新人id不能为空")
    private Long updateAccountId;
}
